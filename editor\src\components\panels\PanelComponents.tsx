/**
 * 面板组件
 */
import React from 'react';
import { useTranslation } from 'react-i18next';

/**
 * 面板拖拽容器属性
 */
interface PanelDragContainerProps {
  children: React.ReactNode;
  dataTestId?: string;
}

/**
 * 面板拖拽容器组件
 */
export const PanelDragContainer: React.FC<PanelDragContainerProps> = ({
  children,
  dataTestId
}) => {
  return (
    <div
      data-testid={dataTestId}
      style={{
        display: 'flex',
        alignItems: 'center',
        height: '100%',
        padding: '0 8px',
        cursor: 'grab',
        userSelect: 'none'
      }}
    >
      {children}
    </div>
  );
};

/**
 * 面板标题属性
 */
interface PanelTitleProps {
  children: React.ReactNode;
}

/**
 * 面板标题组件
 */
export const PanelTitle: React.FC<PanelTitleProps> = ({ children }) => {
  return (
    <span style={{
      color: '#fff',
      fontSize: '14px',
      fontWeight: 500,
      lineHeight: '20px'
    }}>
      {children}
    </span>
  );
};

// 各个面板的标题组件
export const ViewportPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="viewport-panel-tab">
      <PanelTitle>{t('editor.viewport') || '视口'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const HierarchyPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="hierarchy-panel-tab">
      <PanelTitle>{t('editor.hierarchy') || '层级'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const AssetsPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="assets-panel-tab">
      <PanelTitle>{t('editor.assets') || '资源'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const ScenePanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="scene-panel-tab">
      <PanelTitle>{t('editor.scene') || '场景'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const InspectorPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="inspector-panel-tab">
      <PanelTitle>{t('editor.inspector') || '检查器'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const PropertiesPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="properties-panel-tab">
      <PanelTitle>{t('editor.properties') || '属性'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const MaterialsPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="materials-panel-tab">
      <PanelTitle>{t('editor.materials') || '材质'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const ConsolePanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="console-panel-tab">
      <PanelTitle>{t('editor.console') || '控制台'}</PanelTitle>
    </PanelDragContainer>
  );
};

export const VisualScriptPanelTitle = () => {
  const { t } = useTranslation();

  return (
    <PanelDragContainer dataTestId="visualscript-panel-tab">
      <PanelTitle>{t('editor.visualScript') || '可视化脚本'}</PanelTitle>
    </PanelDragContainer>
  );
};