/**
 * 场景加载器
 * 处理GLTF场景文件的加载和解析
 */

export interface SceneData {
  id: string;
  name: string;
  description?: string;
  nodes: SceneNode[];
  materials: Material[];
  textures: Texture[];
  animations: Animation[];
  cameras: Camera[];
  lights: Light[];
}

export interface SceneNode {
  id: string;
  name: string;
  type: 'mesh' | 'light' | 'camera' | 'empty' | 'group';
  transform: {
    position: [number, number, number];
    rotation: [number, number, number, number]; // quaternion
    scale: [number, number, number];
  };
  children: string[];
  parent?: string;
  mesh?: string;
  material?: string;
  visible: boolean;
}

export interface Material {
  id: string;
  name: string;
  type: 'standard' | 'basic' | 'physical' | 'unlit';
  properties: {
    baseColor?: [number, number, number, number];
    metallic?: number;
    roughness?: number;
    emissive?: [number, number, number];
    normalTexture?: string;
    baseColorTexture?: string;
    metallicRoughnessTexture?: string;
  };
}

export interface Texture {
  id: string;
  name: string;
  uri: string;
  format: string;
  width?: number;
  height?: number;
}

export interface Animation {
  id: string;
  name: string;
  duration: number;
  channels: AnimationChannel[];
}

export interface AnimationChannel {
  target: string; // node id
  property: 'translation' | 'rotation' | 'scale';
  keyframes: Keyframe[];
}

export interface Keyframe {
  time: number;
  value: number[];
  interpolation: 'linear' | 'step' | 'cubic';
}

export interface Camera {
  id: string;
  name: string;
  type: 'perspective' | 'orthographic';
  fov?: number;
  near: number;
  far: number;
  aspectRatio?: number;
}

export interface Light {
  id: string;
  name: string;
  type: 'directional' | 'point' | 'spot' | 'ambient';
  color: [number, number, number];
  intensity: number;
  range?: number;
  innerConeAngle?: number;
  outerConeAngle?: number;
}

class SceneLoader {
  private static instance: SceneLoader;

  public static getInstance(): SceneLoader {
    if (!SceneLoader.instance) {
      SceneLoader.instance = new SceneLoader();
    }
    return SceneLoader.instance;
  }

  /**
   * 加载GLTF场景文件
   */
  public async loadScene(scenePath: string): Promise<SceneData> {
    try {
      console.log('加载场景:', scenePath);
      
      // 检查场景文件是否存在
      const response = await fetch(scenePath);
      if (!response.ok) {
        throw new Error(`场景文件不存在: ${scenePath}`);
      }

      const gltfData = await response.json();
      console.log('GLTF数据:', gltfData);

      // 解析GLTF数据
      const sceneData = this.parseGLTF(gltfData, scenePath);
      console.log('解析后的场景数据:', sceneData);

      return sceneData;
    } catch (error) {
      console.error('加载场景失败:', error);
      
      // 返回一个默认的空场景
      return this.createDefaultScene(scenePath);
    }
  }

  /**
   * 解析GLTF数据
   */
  private parseGLTF(gltfData: any, scenePath: string): SceneData {
    const sceneData: SceneData = {
      id: this.getSceneIdFromPath(scenePath),
      name: gltfData.asset?.extras?.title || '未命名场景',
      description: gltfData.asset?.extras?.description,
      nodes: [],
      materials: [],
      textures: [],
      animations: [],
      cameras: [],
      lights: []
    };

    // 解析节点
    if (gltfData.nodes) {
      sceneData.nodes = gltfData.nodes.map((node: any, index: number) => ({
        id: `node_${index}`,
        name: node.name || `Node ${index}`,
        type: this.getNodeType(node),
        transform: {
          position: node.translation || [0, 0, 0],
          rotation: node.rotation || [0, 0, 0, 1],
          scale: node.scale || [1, 1, 1]
        },
        children: node.children || [],
        mesh: node.mesh !== undefined ? `mesh_${node.mesh}` : undefined,
        material: node.material !== undefined ? `material_${node.material}` : undefined,
        visible: true
      }));
    }

    // 解析材质
    if (gltfData.materials) {
      sceneData.materials = gltfData.materials.map((material: any, index: number) => ({
        id: `material_${index}`,
        name: material.name || `Material ${index}`,
        type: 'standard',
        properties: {
          baseColor: material.pbrMetallicRoughness?.baseColorFactor || [1, 1, 1, 1],
          metallic: material.pbrMetallicRoughness?.metallicFactor || 0,
          roughness: material.pbrMetallicRoughness?.roughnessFactor || 1,
          emissive: material.emissiveFactor || [0, 0, 0]
        }
      }));
    }

    // 解析纹理
    if (gltfData.textures) {
      sceneData.textures = gltfData.textures.map((texture: any, index: number) => ({
        id: `texture_${index}`,
        name: `Texture ${index}`,
        uri: gltfData.images?.[texture.source]?.uri || '',
        format: 'image/jpeg'
      }));
    }

    return sceneData;
  }

  /**
   * 获取节点类型
   */
  private getNodeType(node: any): 'mesh' | 'light' | 'camera' | 'empty' | 'group' {
    if (node.mesh !== undefined) return 'mesh';
    if (node.camera !== undefined) return 'camera';
    if (node.extensions?.KHR_lights_punctual?.light !== undefined) return 'light';
    if (node.children && node.children.length > 0) return 'group';
    return 'empty';
  }

  /**
   * 从路径获取场景ID
   */
  private getSceneIdFromPath(scenePath: string): string {
    const filename = scenePath.split('/').pop() || '';
    return filename.replace('.gltf', '').replace('.glb', '');
  }

  /**
   * 创建默认场景
   */
  private createDefaultScene(scenePath: string): SceneData {
    return {
      id: this.getSceneIdFromPath(scenePath),
      name: '默认场景',
      description: '无法加载原始场景，显示默认内容',
      nodes: [
        {
          id: 'default_cube',
          name: '立方体',
          type: 'mesh',
          transform: {
            position: [0, 0, 0],
            rotation: [0, 0, 0, 1],
            scale: [1, 1, 1]
          },
          children: [],
          visible: true
        },
        {
          id: 'default_light',
          name: '方向光',
          type: 'light',
          transform: {
            position: [5, 5, 5],
            rotation: [0, 0, 0, 1],
            scale: [1, 1, 1]
          },
          children: [],
          visible: true
        }
      ],
      materials: [
        {
          id: 'default_material',
          name: '默认材质',
          type: 'standard',
          properties: {
            baseColor: [0.8, 0.8, 0.8, 1],
            metallic: 0,
            roughness: 0.5
          }
        }
      ],
      textures: [],
      animations: [],
      cameras: [],
      lights: [
        {
          id: 'default_light',
          name: '方向光',
          type: 'directional',
          color: [1, 1, 1],
          intensity: 1
        }
      ]
    };
  }

  /**
   * 获取场景统计信息
   */
  public getSceneStats(sceneData: SceneData) {
    return {
      nodeCount: sceneData.nodes.length,
      materialCount: sceneData.materials.length,
      textureCount: sceneData.textures.length,
      animationCount: sceneData.animations.length,
      cameraCount: sceneData.cameras.length,
      lightCount: sceneData.lights.length
    };
  }
}

export default SceneLoader;
