/**
 * 资源管理服务
 * 处理各种类型资源的加载、预览、管理和应用
 */

export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  AUDIO = 'audio',
  VIDEO = 'video',
  SCRIPT = 'script',
  PREFAB = 'prefab',
  SCENE = 'scene'
}

export interface Asset {
  id: string;
  name: string;
  type: AssetType;
  path: string;
  thumbnail?: string;
  size?: number;
  format?: string;
  metadata?: Record<string, any>;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface AssetCategory {
  id: string;
  name: string;
  icon: string;
  types: AssetType[];
  count: number;
}

export interface AssetImportOptions {
  generateThumbnail: boolean;
  optimizeForWeb: boolean;
  compressionLevel: number;
  generateMipmaps: boolean;
}

class AssetManagerService {
  private static instance: AssetManagerService;
  
  private assets: Map<string, Asset> = new Map();
  private categories: AssetCategory[] = [];
  private loadedAssets: Map<string, any> = new Map(); // 缓存已加载的资源
  
  // 事件监听器
  private assetLoadListeners: ((asset: Asset, data: any) => void)[] = [];
  private assetImportListeners: ((asset: Asset) => void)[] = [];
  private assetDeleteListeners: ((assetId: string) => void)[] = [];

  public static getInstance(): AssetManagerService {
    if (!AssetManagerService.instance) {
      AssetManagerService.instance = new AssetManagerService();
    }
    return AssetManagerService.instance;
  }

  constructor() {
    this.initializeCategories();
  }

  /**
   * 初始化资源分类
   */
  private initializeCategories(): void {
    this.categories = [
      {
        id: 'models',
        name: '模型',
        icon: 'model',
        types: [AssetType.MODEL, AssetType.PREFAB],
        count: 0
      },
      {
        id: 'textures',
        name: '纹理',
        icon: 'texture',
        types: [AssetType.TEXTURE],
        count: 0
      },
      {
        id: 'materials',
        name: '材质',
        icon: 'material',
        types: [AssetType.MATERIAL],
        count: 0
      },
      {
        id: 'audio',
        name: '音频',
        icon: 'audio',
        types: [AssetType.AUDIO],
        count: 0
      },
      {
        id: 'video',
        name: '视频',
        icon: 'video',
        types: [AssetType.VIDEO],
        count: 0
      },
      {
        id: 'scripts',
        name: '脚本',
        icon: 'script',
        types: [AssetType.SCRIPT],
        count: 0
      },
      {
        id: 'scenes',
        name: '场景',
        icon: 'scene',
        types: [AssetType.SCENE],
        count: 0
      }
    ];
  }

  /**
   * 加载项目资源
   */
  public async loadProjectAssets(projectPath: string): Promise<Asset[]> {
    try {
      console.log('加载项目资源:', projectPath);
      
      // 模拟加载项目资源
      const mockAssets: Asset[] = [
        {
          id: 'sample-audio',
          name: '示例音频',
          type: AssetType.AUDIO,
          path: `${projectPath}/assets/SampleAudio.mp3`,
          thumbnail: `${projectPath}/assets/thumbnails/SampleAudio.jpg`,
          size: 1024000,
          format: 'mp3',
          tags: ['demo', 'audio'],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'sample-video',
          name: '示例视频',
          type: AssetType.VIDEO,
          path: `${projectPath}/assets/SampleVideo.mp4`,
          thumbnail: `${projectPath}/assets/thumbnails/SampleVideo.jpg`,
          size: 5120000,
          format: 'mp4',
          tags: ['demo', 'video'],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'apartment-model',
          name: '公寓模型',
          type: AssetType.MODEL,
          path: `${projectPath}/assets/apartment.glb`,
          thumbnail: `${projectPath}/assets/thumbnails/apartment.jpg`,
          size: 2048000,
          format: 'glb',
          tags: ['architecture', 'interior'],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'platform-model',
          name: '平台模型',
          type: AssetType.MODEL,
          path: `${projectPath}/assets/platform.glb`,
          thumbnail: `${projectPath}/assets/thumbnails/platform.jpg`,
          size: 512000,
          format: 'glb',
          tags: ['geometry', 'basic'],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      // 存储资源
      mockAssets.forEach(asset => {
        this.assets.set(asset.id, asset);
      });

      // 更新分类计数
      this.updateCategoryCounts();

      console.log('项目资源加载完成，总数:', mockAssets.length);
      return mockAssets;
    } catch (error) {
      console.error('加载项目资源失败:', error);
      return [];
    }
  }

  /**
   * 更新分类计数
   */
  private updateCategoryCounts(): void {
    this.categories.forEach(category => {
      category.count = Array.from(this.assets.values())
        .filter(asset => category.types.includes(asset.type))
        .length;
    });
  }

  /**
   * 获取所有资源
   */
  public getAssets(): Asset[] {
    return Array.from(this.assets.values());
  }

  /**
   * 根据类型获取资源
   */
  public getAssetsByType(type: AssetType): Asset[] {
    return Array.from(this.assets.values()).filter(asset => asset.type === type);
  }

  /**
   * 根据分类获取资源
   */
  public getAssetsByCategory(categoryId: string): Asset[] {
    const category = this.categories.find(cat => cat.id === categoryId);
    if (!category) return [];
    
    return Array.from(this.assets.values())
      .filter(asset => category.types.includes(asset.type));
  }

  /**
   * 搜索资源
   */
  public searchAssets(query: string, type?: AssetType): Asset[] {
    const assets = type ? this.getAssetsByType(type) : this.getAssets();
    
    return assets.filter(asset => 
      asset.name.toLowerCase().includes(query.toLowerCase()) ||
      asset.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );
  }

  /**
   * 获取资源分类
   */
  public getCategories(): AssetCategory[] {
    return this.categories;
  }

  /**
   * 根据ID获取资源
   */
  public getAsset(id: string): Asset | null {
    return this.assets.get(id) || null;
  }

  /**
   * 加载资源数据
   */
  public async loadAssetData(asset: Asset): Promise<any> {
    // 检查缓存
    if (this.loadedAssets.has(asset.id)) {
      return this.loadedAssets.get(asset.id);
    }

    try {
      console.log('加载资源数据:', asset.name);
      
      let data: any = null;
      
      switch (asset.type) {
        case AssetType.TEXTURE:
          data = await this.loadTexture(asset);
          break;
        case AssetType.MODEL:
          data = await this.loadModel(asset);
          break;
        case AssetType.AUDIO:
          data = await this.loadAudio(asset);
          break;
        case AssetType.VIDEO:
          data = await this.loadVideo(asset);
          break;
        default:
          console.warn('不支持的资源类型:', asset.type);
          break;
      }

      if (data) {
        this.loadedAssets.set(asset.id, data);
        this.assetLoadListeners.forEach(listener => listener(asset, data));
      }

      return data;
    } catch (error) {
      console.error('加载资源数据失败:', error);
      return null;
    }
  }

  /**
   * 加载纹理
   */
  private async loadTexture(asset: Asset): Promise<any> {
    return new Promise((resolve, reject) => {
      const image = new Image();
      image.onload = () => resolve(image);
      image.onerror = reject;
      image.src = asset.path;
    });
  }

  /**
   * 加载模型
   */
  private async loadModel(asset: Asset): Promise<any> {
    // 这里应该使用Three.js的GLTFLoader
    // 暂时返回模拟数据
    return {
      type: 'model',
      path: asset.path,
      format: asset.format
    };
  }

  /**
   * 加载音频
   */
  private async loadAudio(asset: Asset): Promise<any> {
    return new Promise((resolve, reject) => {
      const audio = new Audio();
      audio.oncanplaythrough = () => resolve(audio);
      audio.onerror = reject;
      audio.src = asset.path;
    });
  }

  /**
   * 加载视频
   */
  private async loadVideo(asset: Asset): Promise<any> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.oncanplaythrough = () => resolve(video);
      video.onerror = reject;
      video.src = asset.path;
    });
  }

  /**
   * 导入资源
   */
  public async importAsset(
    file: File, 
    options: Partial<AssetImportOptions> = {}
  ): Promise<Asset | null> {
    try {
      console.log('导入资源:', file.name);
      
      const assetType = this.getAssetTypeFromFile(file);
      const assetId = `imported-${Date.now()}`;
      
      const asset: Asset = {
        id: assetId,
        name: file.name.split('.')[0],
        type: assetType,
        path: URL.createObjectURL(file),
        size: file.size,
        format: file.name.split('.').pop()?.toLowerCase(),
        tags: ['imported'],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // 生成缩略图
      if (options.generateThumbnail) {
        asset.thumbnail = await this.generateThumbnail(file, assetType);
      }

      this.assets.set(asset.id, asset);
      this.updateCategoryCounts();
      
      this.assetImportListeners.forEach(listener => listener(asset));
      
      console.log('资源导入完成:', asset.name);
      return asset;
    } catch (error) {
      console.error('导入资源失败:', error);
      return null;
    }
  }

  /**
   * 从文件获取资源类型
   */
  private getAssetTypeFromFile(file: File): AssetType {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'gltf':
      case 'glb':
      case 'fbx':
      case 'obj':
        return AssetType.MODEL;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'webp':
      case 'ktx2':
        return AssetType.TEXTURE;
      case 'mp3':
      case 'wav':
      case 'ogg':
        return AssetType.AUDIO;
      case 'mp4':
      case 'webm':
        return AssetType.VIDEO;
      case 'js':
      case 'ts':
        return AssetType.SCRIPT;
      default:
        return AssetType.MODEL; // 默认类型
    }
  }

  /**
   * 生成缩略图
   */
  private async generateThumbnail(file: File, type: AssetType): Promise<string> {
    // 简化的缩略图生成
    if (type === AssetType.TEXTURE) {
      return URL.createObjectURL(file);
    }
    
    // 其他类型返回默认缩略图
    return '/assets/icons/default-thumbnail.png';
  }

  /**
   * 删除资源
   */
  public deleteAsset(assetId: string): boolean {
    const asset = this.assets.get(assetId);
    if (!asset) return false;
    
    // 清理缓存
    this.loadedAssets.delete(assetId);
    
    // 删除资源
    this.assets.delete(assetId);
    this.updateCategoryCounts();
    
    this.assetDeleteListeners.forEach(listener => listener(assetId));
    
    console.log('资源已删除:', asset.name);
    return true;
  }

  /**
   * 添加资源加载监听器
   */
  public addAssetLoadListener(listener: (asset: Asset, data: any) => void): void {
    this.assetLoadListeners.push(listener);
  }

  /**
   * 添加资源导入监听器
   */
  public addAssetImportListener(listener: (asset: Asset) => void): void {
    this.assetImportListeners.push(listener);
  }

  /**
   * 添加资源删除监听器
   */
  public addAssetDeleteListener(listener: (assetId: string) => void): void {
    this.assetDeleteListeners.push(listener);
  }

  /**
   * 清理服务
   */
  public dispose(): void {
    this.assets.clear();
    this.loadedAssets.clear();
    this.assetLoadListeners = [];
    this.assetImportListeners = [];
    this.assetDeleteListeners = [];
    
    console.log('资源管理服务已清理');
  }
}

export default AssetManagerService;
