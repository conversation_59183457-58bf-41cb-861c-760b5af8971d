/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and 
provide for limited attribution for the Original Developer. In addition, 
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2023 
Infinite Reality Engine. All Rights Reserved.
*/

import { ModerationType } from '@ir-engine/common/src/schemas/moderation/moderation.schema'
import { ModerationDetail } from './ModerationDetail'
const argTypes = {
  'selectedModeration.type': {
    control: 'select',
    options: ['user', 'location'],
    description: 'Type of moderation'
  },
  'selectedModeration.abuseReason': {
    control: 'select',
    options: ['spam', 'harassment', 'cheating'], // Replace with actual abuse reasons
    description: 'Reason for the report'
  },
  'selectedModeration.ipAddress': {
    control: 'text',
    description: 'IP address of the reported user'
  },
  'selectedModeration.reportDetails': {
    control: 'text',
    description: 'Details of the report'
  },
  'selectedModeration.status': {
    control: 'select',
    options: ['open', 'resolved'],
    description: 'Status of the moderation'
  },
  'selectedModeration.reportedAt': {
    control: 'date',
    description: 'Date and time when the report was made'
  }
  // Add more controls as needed for other properties
}

export default {
  title: 'Client/ModerationDetail',
  component: ModerationDetail,
  parameters: {
    componentSubtitle: 'ModerationDetail',
    design: {
      type: 'figma',
      url: ''
    }
  },
  argTypes
}

export const Default = {
  args: {
    report: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      type: 'user',
      abuseReason: 'spam',
      reportedUserId: '123e4567-e89b-12d3-a456-426614174001',
      reportedLocationId: '123e4567-e89b-12d3-a456-426614174002',
      ipAddress: '***********',
      reportDetails: 'User was spamming in the chat.',
      status: 'open',
      reportedAt: new Date().toISOString(),
      createdBy: '123e4567-e89b-12d3-a456-426614174003',
      updatedBy: '123e4567-e89b-12d3-a456-426614174004',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as unknown as ModerationType
  }
}
