/**
 * 应用程序主组件
 */
import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, theme, Spin } from 'antd';
import { useTranslation } from 'react-i18next';

import { useAppDispatch, useAppSelector } from './store';
import { checkAuth } from './store/auth/authSlice';
import { ThemeType } from './store/ui/uiSlice';
import { AppLayout } from './components/layout/AppLayout';
import { LoginPage } from './pages/LoginPage';
import { RegisterPage } from './pages/RegisterPage';
import { ProjectsPage } from './pages/ProjectsPage';
import { EditorPage } from './pages/EditorPage';
import { NotFoundPage } from './pages/NotFoundPage';



const App: React.FC = () => {
  const dispatch = useAppDispatch();
  const { t, i18n } = useTranslation();
  const { isAuthenticated, isLoading } = useAppSelector((state) => state.auth);
  const { theme: themeType, language } = useAppSelector((state) => state.ui);

  // 检查认证状态
  useEffect(() => {
    const token = localStorage.getItem('token');
    console.log('App初始化，检查认证状态:', { token: !!token, isAuthenticated, isLoading });

    if (token && !isAuthenticated && !isLoading) {
      console.log('发现本地token，验证认证状态');
      dispatch(checkAuth());
    } else if (!token && !isAuthenticated && !isLoading && process.env.NODE_ENV === 'development') {
      // 在开发环境下，如果没有token，自动创建开发token并验证
      console.log('开发环境：自动创建开发认证');
      const payload = {
        sub: 'dev-user-123',
        username: 'developer',
        email: '<EMAIL>',
        role: 'admin',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
      };

      const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
      const payloadStr = btoa(JSON.stringify(payload));
      const signature = btoa('dev-signature');

      const devToken = `${header}.${payloadStr}.${signature}`;
      localStorage.setItem('token', devToken);

      // 验证开发认证
      dispatch(checkAuth());
    }
  }, [dispatch, isAuthenticated, isLoading]);

  // 设置语言
  useEffect(() => {
    if (language && i18n.isInitialized) {
      i18n.changeLanguage(language);
    }
  }, [language, i18n]);

  // 如果正在加载认证状态，显示加载中
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip={t('common.loading')} />
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: themeType === ThemeType.DARK ? theme.darkAlgorithm : theme.defaultAlgorithm
      }}
    >
      <Routes>
        {/* 公共路由 */}
        <Route
          path="/login"
          element={
            isLoading ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                <Spin size="large" tip={t('auth.verifyingLogin')} />
              </div>
            ) : (
              <LoginPage />
            )
          }
        />
        <Route
          path="/register"
          element={
            isLoading ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                <Spin size="large" tip={t('auth.verifyingLogin')} />
              </div>
            ) : (
              <RegisterPage />
            )
          }
        />

        {/* 编辑器路由（独立，不需要AppLayout包装） */}
        <Route
          path="/editor/:projectId/:sceneId"
          element={
            isLoading ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                <Spin size="large" tip={t('common.loading')} />
              </div>
            ) : isAuthenticated ? (
              <EditorPage />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        />

        {/* 需要认证的路由 */}
        <Route
          path="/"
          element={
            isLoading ? (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                <Spin size="large" tip={t('common.loading')} />
              </div>
            ) : isAuthenticated ? (
              <AppLayout />
            ) : (
              <Navigate to="/login" replace />
            )
          }
        >
          <Route index element={<Navigate to="/projects" replace />} />
          <Route path="projects" element={<ProjectsPage />} />
        </Route>

        {/* 404页面 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </ConfigProvider>
  );
};

export default App;
