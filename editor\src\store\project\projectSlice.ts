/**
 * 项目状态切片
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
// import { httpClient } from '../../utils/httpClient';

// 定义项目类型
export interface Project {
  id: string;
  name: string;
  description: string;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  scenes: Scene[];
  isPublic: boolean;
  ownerId: string;
}

// 定义场景类型
export interface Scene {
  id: string;
  name: string;
  description?: string;
  thumbnail?: string;
  createdAt: string;
  updatedAt: string;
  projectId: string;
}

// 定义项目状态
interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  currentScene: Scene | null;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: ProjectState = {
  projects: [],
  currentProject: null,
  currentScene: null,
  isLoading: false,
  error: null};

// 获取项目列表
export const fetchProjects = createAsyncThunk('project/fetchProjects', async (_, { rejectWithValue }) => {
  try {
    // 暂时返回模拟数据，避免404错误导致循环重定向
    const mockProjects = [
      {
        id: '1',
        name: '示例项目',
        description: '这是一个示例项目',
        thumbnail: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        scenes: [
          {
            id: 'scene-1',
            name: '主场景',
            description: '项目的主要场景',
            thumbnail: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            projectId: '1'
          }
        ],
        isPublic: false,
        ownerId: 'current-user'
      },
      {
        id: '2',
        name: '我的项目',
        description: '这是我的第一个项目',
        thumbnail: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        scenes: [
          {
            id: 'scene-2',
            name: '开始场景',
            description: '游戏开始的场景',
            thumbnail: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            projectId: '2'
          }
        ],
        isPublic: true,
        ownerId: 'current-user'
      }
    ];
    return mockProjects;

    // TODO: 等API网关添加项目路由后，恢复真实API调用
    // const response = await axios.get('/api/projects');
    // return response.data.data || response.data;
  } catch (error: any) {
    // 不要因为API错误而重定向，只记录错误
    console.warn('获取项目列表失败:', error);
    return rejectWithValue(error.response?.data?.message || '获取项目列表失败');
  }
});

// 获取项目详情
export const fetchProjectById = createAsyncThunk(
  'project/fetchProjectById',
  async (projectId: string, { rejectWithValue }) => {
    try {
      // 暂时返回模拟数据
      const mockProject = {
        id: projectId,
        name: projectId === '1' ? '示例项目' : '我的项目',
        description: projectId === '1' ? '这是一个示例项目' : '这是我的第一个项目',
        thumbnail: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        scenes: [
          {
            id: projectId === '1' ? 'scene-1' : 'scene-2',
            name: projectId === '1' ? '主场景' : '开始场景',
            description: projectId === '1' ? '项目的主要场景' : '游戏开始的场景',
            thumbnail: '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            projectId: projectId
          }
        ],
        isPublic: projectId === '2',
        ownerId: 'current-user'
      };
      return mockProject;

      // TODO: 等API网关添加项目路由后，恢复真实API调用
      // const response = await axios.get(`/api/projects/${projectId}`);
      // return response.data.data || response.data;
    } catch (error: any) {
      console.warn('获取项目详情失败:', error);
      return rejectWithValue(error.response?.data?.message || '获取项目详情失败');
    }
  }
);

// 创建新项目
export const createProject = createAsyncThunk(
  'project/createProject',
  async (
    { name, description, isPublic = false }: { name: string; description: string; isPublic?: boolean },
    { rejectWithValue }
  ) => {
    try {
      // 暂时返回模拟数据
      const mockProject = {
        id: Date.now().toString(),
        name,
        description,
        thumbnail: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        scenes: [],
        isPublic,
        ownerId: 'current-user'
      };
      return mockProject;

      // TODO: 等API网关添加项目路由后，恢复真实API调用
      // const response = await axios.post('/api/projects', { name, description, isPublic });
      // return response.data.data || response.data;
    } catch (error: any) {
      console.warn('创建项目失败:', error);
      return rejectWithValue(error.response?.data?.message || '创建项目失败');
    }
  }
);

// 更新项目
export const updateProject = createAsyncThunk(
  'project/updateProject',
  async (
    {
      projectId,
      data}: {
      projectId: string;
      data: { name?: string; description?: string; isPublic?: boolean };
    },
    { rejectWithValue }
  ) => {
    try {
      // 暂时返回模拟数据
      const mockProject = {
        id: projectId,
        name: data.name || '更新的项目',
        description: data.description || '项目描述',
        thumbnail: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        scenes: [],
        isPublic: data.isPublic || false,
        ownerId: 'current-user'
      };
      return mockProject;

      // TODO: 等API网关添加项目路由后，恢复真实API调用
      // const response = await axios.patch(`/api/projects/${projectId}`, data);
      // return response.data.data || response.data;
    } catch (error: any) {
      console.warn('更新项目失败:', error);
      return rejectWithValue(error.response?.data?.message || '更新项目失败');
    }
  }
);

// 删除项目
export const deleteProject = createAsyncThunk(
  'project/deleteProject',
  async (projectId: string, { rejectWithValue }) => {
    try {
      // 暂时直接返回项目ID，表示删除成功
      console.log('模拟删除项目:', projectId);
      return projectId;

      // TODO: 等API网关添加项目路由后，恢复真实API调用
      // await axios.delete(`/api/projects/${projectId}`);
      // return projectId;
    } catch (error: any) {
      console.warn('删除项目失败:', error);
      return rejectWithValue(error.response?.data?.message || '删除项目失败');
    }
  }
);

// 创建场景
export const createScene = createAsyncThunk(
  'project/createScene',
  async (
    {
      projectId,
      name,
      description}: {
      projectId: string;
      name: string;
      description?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      // 暂时返回模拟场景数据
      const mockScene = {
        id: Date.now().toString(),
        name,
        description: description || '',
        thumbnail: '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        projectId
      };
      return mockScene;

      // TODO: 等API网关添加项目路由后，恢复真实API调用
      // const response = await axios.post(`/api/projects/${projectId}/scenes`, { name, description });
      // return response.data.data || response.data;
    } catch (error: any) {
      console.warn('创建场景失败:', error);
      return rejectWithValue(error.response?.data?.message || '创建场景失败');
    }
  }
);

// 创建项目切片
const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    setCurrentProject: (state, action: PayloadAction<Project>) => {
      state.currentProject = action.payload;
    },
    setCurrentScene: (state, action: PayloadAction<Scene>) => {
      state.currentScene = action.payload;
    },
    clearCurrentProject: (state) => {
      state.currentProject = null;
      state.currentScene = null;
    },
    clearError: (state) => {
      state.error = null;
    }},
  extraReducers: (builder) => {
    // 获取项目列表
    builder
      .addCase(fetchProjects.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects = action.payload;
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取项目详情
    builder
      .addCase(fetchProjectById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjectById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentProject = action.payload;
        // 如果项目有场景，设置第一个场景为当前场景
        if (action.payload.scenes && action.payload.scenes.length > 0) {
          state.currentScene = action.payload.scenes[0];
        }
      })
      .addCase(fetchProjectById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建项目
    builder
      .addCase(createProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createProject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects.push(action.payload);
        state.currentProject = action.payload;
      })
      .addCase(createProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 更新项目
    builder
      .addCase(updateProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(updateProject.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.projects.findIndex((p) => p.id === action.payload.id);
        if (index !== -1) {
          state.projects[index] = action.payload;
        }
        if (state.currentProject?.id === action.payload.id) {
          state.currentProject = action.payload;
        }
      })
      .addCase(updateProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 删除项目
    builder
      .addCase(deleteProject.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteProject.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects = state.projects.filter((p) => p.id !== action.payload);
        if (state.currentProject?.id === action.payload) {
          state.currentProject = null;
          state.currentScene = null;
        }
      })
      .addCase(deleteProject.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建场景
    builder
      .addCase(createScene.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createScene.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.currentProject) {
          state.currentProject.scenes.push(action.payload);
          state.currentScene = action.payload;
        }
      })
      .addCase(createScene.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }});

export const { setCurrentProject, setCurrentScene, clearCurrentProject, clearError } = projectSlice.actions;
export default projectSlice.reducer;
