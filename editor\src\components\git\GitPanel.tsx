/**
 * Git面板组件
 * 提供Git版本控制功能界面
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Card, Space, Button, Tooltip, message, Spin } from 'antd';
import {
  BranchesOutlined,
  HistoryOutlined,
  SyncOutlined,
  CloseCircleOutlined,
  FileOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setShowGitPanel } from '../../store/git/gitSlice';
import { getGitService } from '../../services/GitService';
import GitStatusPanel from './GitStatusPanel';
import GitHistoryPanel from './GitHistoryPanel';
import GitBranchPanel from './GitBranchPanel';
import './GitPanel.less';

/**
 * Git面板组件
 */
const GitPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const {
    status,
    isLoading,
    showGitPanel
  } = useSelector((state: RootState) => state.git);
  const [activeTab, setActiveTab] = useState<string>('status');

  // 初始化 - 只在面板显示时才调用API
  useEffect(() => {
    // 只有当Git面板显示时才获取数据，避免在编辑器加载时立即调用API
    if (showGitPanel) {
      // 延迟获取Git服务实例，避免在模块加载时立即初始化
      const gitService = getGitService();

      // 获取Git状态
      gitService.fetchStatus();

      // 获取提交历史
      gitService.fetchCommitHistory();

      // 获取远程仓库信息
      gitService.fetchRemotes();
    }
  }, [showGitPanel]); // 依赖showGitPanel，只在面板显示时才执行

  // 处理关闭面板
  const handleClose = () => {
    dispatch(setShowGitPanel(false));
  };

  // 处理刷新
  const handleRefresh = () => {
    const gitService = getGitService();
    gitService.fetchStatus();

    if (activeTab === 'history') {
      gitService.fetchCommitHistory();
    }

    message.success(t('git.refreshSuccess'));
  };

  // 渲染面板标题
  const renderTitle = () => {
    return (
      <Space>
        <BranchesOutlined />
        <span>{t('git.title')}</span>
        {status && (
          <Tooltip title={`${status.branch} (${status.ahead}↑ ${status.behind}↓)`}>
            <span className="git-branch-info">
              {status.branch} ({status.ahead}↑ {status.behind}↓)
            </span>
          </Tooltip>
        )}
      </Space>
    );
  };

  // 渲染面板操作按钮
  const renderActions = () => {
    return (
      <Space>
        <Tooltip title={t('git.refresh')}>
          <Button
            icon={<SyncOutlined />}
            onClick={handleRefresh}
            loading={isLoading}
          />
        </Tooltip>
        <Tooltip title={t('git.close')}>
          <Button
            icon={<CloseCircleOutlined />}
            onClick={handleClose}
          />
        </Tooltip>
      </Space>
    );
  };

  // 渲染面板内容
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="git-loading">
          <Spin size="large" />
          <div className="git-loading-text">{t('git.loading')}</div>
        </div>
      );
    }

    return (
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'status',
            label: (
              <span>
                <FileOutlined />
                {t('git.status')}
              </span>
            ),
            children: <GitStatusPanel />
          },
          {
            key: 'history',
            label: (
              <span>
                <HistoryOutlined />
                {t('git.history')}
              </span>
            ),
            children: <GitHistoryPanel />
          },
          {
            key: 'branches',
            label: (
              <span>
                <BranchesOutlined />
                {t('git.branches')}
              </span>
            ),
            children: <GitBranchPanel />
          }
        ]}
      />
    );
  };

  // 如果面板不可见，则不渲染
  if (!showGitPanel) {
    return null;
  }

  return (
    <div className="git-panel">
      <Card
        title={renderTitle()}
        extra={renderActions()}
        className="git-card"
      >
        {renderContent()}
      </Card>
    </div>
  );
};

export default GitPanel;
