/**
 * 运行时错误修复脚本
 * 修复编辑器中常见的运行时错误
 */
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 开始修复运行时错误...');

// 修复Three.js API兼容性问题
function fixThreeJSCompatibility() {
  console.log('修复Three.js API兼容性问题...');
  
  const files = [
    'src/services/ThreeRenderService.ts',
    'src/services/ThreePerformanceService.ts',
    'src/components/panels/ViewportPanel.tsx'
  ];
  
  files.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 替换过时的API
      content = content.replace(/THREE\.sRGBEncoding/g, 'THREE.SRGBColorSpace');
      content = content.replace(/outputEncoding/g, 'outputColorSpace');
      content = content.replace(/physicallyCorrectLights\s*=\s*true/g, 'useLegacyLights = false');
      content = content.replace(/physicallyCorrectLights\s*=\s*false/g, 'useLegacyLights = true');
      content = content.replace(/\.physicallyCorrectLights/g, '.useLegacyLights');
      
      fs.writeFileSync(filePath, content);
      console.log(`✅ 修复了 ${filePath}`);
    }
  });
}

// 修复导入错误
function fixImportErrors() {
  console.log('修复导入错误...');
  
  // 检查并修复常见的导入问题
  const srcDir = 'src';
  
  function processFile(filePath) {
    if (path.extname(filePath) === '.ts' || path.extname(filePath) === '.tsx') {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      // 修复相对路径导入
      if (content.includes("from '../")) {
        // 确保相对路径正确
        content = content.replace(/from '\.\.\/\.\.\//g, "from '../../");
        modified = true;
      }
      
      // 修复Three.js导入
      if (content.includes("import * as THREE from 'three'")) {
        // 确保Three.js导入正确
        if (!content.includes('THREE.SRGBColorSpace')) {
          content = content.replace(/THREE\.sRGBEncoding/g, 'THREE.SRGBColorSpace');
          modified = true;
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ 修复了导入: ${filePath}`);
      }
    }
  }
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        walkDir(filePath);
      } else if (stat.isFile()) {
        processFile(filePath);
      }
    });
  }
  
  if (fs.existsSync(srcDir)) {
    walkDir(srcDir);
  }
}

// 修复类型错误
function fixTypeErrors() {
  console.log('修复类型错误...');
  
  // 修复常见的TypeScript类型错误
  const files = [
    'src/services/ThreeRenderService.ts',
    'src/services/ObjectEditService.ts',
    'src/components/panels/ViewportPanel.tsx'
  ];
  
  files.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      // 修复Three.js类型问题
      if (content.includes('THREE.TextureEncoding')) {
        content = content.replace(/THREE\.TextureEncoding/g, 'THREE.ColorSpace');
        modified = true;
      }
      
      // 修复事件处理器类型
      if (content.includes('NodeJS.Timeout')) {
        if (!content.includes('| null')) {
          content = content.replace(/NodeJS\.Timeout/g, 'NodeJS.Timeout | null');
          modified = true;
        }
      }
      
      if (modified) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ 修复了类型: ${filePath}`);
      }
    }
  });
}

// 修复CSS导入问题
function fixCSSImports() {
  console.log('修复CSS导入问题...');
  
  // 检查CSS文件是否存在
  const cssFiles = [
    'src/styles/index.less',
    'src/styles/variables.less',
    'src/components/debug/DevToolsPanel.less',
    'src/components/debug/ServiceStatusMonitor.less'
  ];
  
  cssFiles.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  缺少CSS文件: ${filePath}`);
      
      // 创建基本的CSS文件
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      let content = '';
      if (filePath.includes('DevToolsPanel.less')) {
        content = `
.dev-tools-panel {
  .ant-drawer-body {
    padding: 0;
  }
}

.dev-tools-float-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}
`;
      } else if (filePath.includes('ServiceStatusMonitor.less')) {
        content = `
.service-status-monitor {
  .service-status-card {
    margin-bottom: 16px;
  }
  
  .service-status-table {
    .ant-table-tbody > tr > td {
      padding: 8px 16px;
    }
  }
}
`;
      }
      
      if (content) {
        fs.writeFileSync(filePath, content);
        console.log(`✅ 创建了CSS文件: ${filePath}`);
      }
    }
  });
}

// 修复环境配置
function fixEnvironmentConfig() {
  console.log('修复环境配置...');
  
  const configPath = 'src/config/environment.ts';
  if (fs.existsSync(configPath)) {
    let content = fs.readFileSync(configPath, 'utf8');
    
    // 确保开发环境配置正确
    if (!content.includes('enableDebug: true')) {
      content = content.replace(/enableDebug:\s*false/g, 'enableDebug: true');
      fs.writeFileSync(configPath, content);
      console.log('✅ 启用了调试模式');
    }
  }
}

// 主修复函数
async function main() {
  try {
    fixThreeJSCompatibility();
    fixImportErrors();
    fixTypeErrors();
    fixCSSImports();
    fixEnvironmentConfig();
    
    console.log('✅ 所有错误修复完成！');
    console.log('🔄 请重新启动开发服务器以应用修复');
  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
    process.exit(1);
  }
}

main();
