/**
 * 布局状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LayoutData } from 'rc-dock';
//import { PanelType } from './uiSlice';

// 定义布局状态接口
export interface LayoutState {
  layout: LayoutData | null;
  savedLayouts: Record<string, LayoutData>;
  activeLayout: string;
  theme: 'light' | 'dark';
}

// 默认布局配置 - 参考原项目ir-engine-dev的布局结构
// 注意：content 字段在实际使用时会被转换为 React 元素
// 根据图片要求，右侧面板区域采用左图3D场景的布局设计
export const defaultLayout: LayoutData = {
  dockbox: {
    mode: 'horizontal',
    children: [
      {
        mode: 'vertical',
        size: 8, // 左侧主要区域，包含3D视口
        children: [
          {
            tabs: [
              { id: 'viewport', title: '视口', content: 'viewport' as any, closable: false }
            ]
          },
          {
            tabs: [
              { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false },
              { id: 'visualscript', title: '可视化脚本', content: 'visualscript' as any, closable: true }
            ]
          }
        ]
      },
      {
        mode: 'horizontal', // 改为水平布局，模拟3D场景的布局结构
        size: 4, // 右侧面板区域，增加宽度以容纳新的布局
        children: [
          {
            mode: 'vertical',
            size: 2, // 左侧子区域
            children: [
              {
                tabs: [
                  { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false },
                  { id: 'scene', title: '场景面板', content: 'scene' as any, closable: true }
                ]
              },
              {
                tabs: [
                  { id: 'materials', title: '材质面板', content: 'materials' as any, closable: true }
                ]
              }
            ]
          },
          {
            mode: 'vertical',
            size: 2, // 右侧子区域，采用3D场景的垂直布局结构
            children: [
              {
                tabs: [
                  { id: 'properties', title: '属性面板', content: 'properties' as any, closable: false }
                ],
                activeId: 'properties' // 默认激活属性面板
              },
              {
                tabs: [
                  { id: 'inspector', title: '检查器', content: 'inspector' as any, closable: false }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
};

// 预定义布局配置
export const predefinedLayouts: Record<string, LayoutData> = {
  default: defaultLayout,
  debug: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 8, // 左侧主要区域
          children: [
            {
              tabs: [
                { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
              ]
            },
            {
              size: 300,
              tabs: [
                { id: 'debug', title: '调试面板', content: 'debug' as any, closable: true }
              ]
            }
          ]
        },
        {
          mode: 'horizontal', // 右侧采用水平布局，模拟3D场景的布局结构
          size: 4,
          children: [
            {
              mode: 'vertical',
              size: 2,
              children: [
                {
                  tabs: [
                    { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false }
                  ]
                }
              ]
            },
            {
              mode: 'vertical',
              size: 2, // 右侧子区域，采用3D场景的垂直布局结构
              children: [
                {
                  tabs: [
                    { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  coding: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          mode: 'vertical',
          size: 8, // 左侧主要区域
          children: [
            {
              tabs: [
                { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
              ]
            },
            {
              size: 200,
              tabs: [
                { id: 'console', title: '控制台', content: 'console' as any, closable: true }
              ]
            }
          ]
        },
        {
          mode: 'horizontal', // 右侧采用水平布局，模拟3D场景的布局结构
          size: 4,
          children: [
            {
              mode: 'vertical',
              size: 2, // 左侧子区域
              children: [
                {
                  tabs: [
                    { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false }
                  ]
                }
              ]
            },
            {
              mode: 'vertical',
              size: 2, // 右侧子区域，采用3D场景的垂直布局结构
              children: [
                {
                  tabs: [
                    { id: 'inspector', title: '属性面板', content: 'inspector' as any, closable: false }
                  ]
                },
                {
                  tabs: [
                    { id: 'assets', title: '资源面板', content: 'assets' as any, closable: false }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  },
  minimal: {
    dockbox: {
      mode: 'horizontal',
      children: [
        {
          size: 200,
          tabs: [
            { id: 'hierarchy', title: '层级面板', content: 'hierarchy' as any, closable: false }
          ]
        },
        {
          size: 800,
          tabs: [
            { id: 'scene', title: '场景视图', content: 'scene' as any, closable: false }
          ]
        }
      ]
    }
  }
};

// 初始状态
const initialState: LayoutState = {
  layout: null,
  savedLayouts: predefinedLayouts,
  activeLayout: 'default',
  theme: 'light'
};

// 创建布局切片
const layoutSlice = createSlice({
  name: 'layout',
  initialState,
  reducers: {
    // 设置当前布局
    setLayout: (state, action: PayloadAction<LayoutData>) => {
      // 存入 Redux 时做深拷贝，避免把 rc-dock 的可变引用直接放进 store
      try {
        state.layout = JSON.parse(JSON.stringify(action.payload));
      } catch {
        state.layout = action.payload;
      }
    },

    // 保存布局
    saveLayout: (state, action: PayloadAction<{ name: string; layout: LayoutData }>) => {
      const { name, layout } = action.payload;
      try {
        state.savedLayouts[name] = JSON.parse(JSON.stringify(layout));
      } catch {
        state.savedLayouts[name] = layout;
      }
      state.activeLayout = name;
    },

    // 加载布局
    loadLayout: (state, action: PayloadAction<string>) => {
      const layoutName = action.payload;
      if (state.savedLayouts[layoutName]) {
        try {
          state.layout = JSON.parse(JSON.stringify(state.savedLayouts[layoutName]));
        } catch {
          state.layout = state.savedLayouts[layoutName];
        }
        state.activeLayout = layoutName;
      }
    },

    // 删除保存的布局
    deleteLayout: (state, action: PayloadAction<string>) => {
      const layoutName = action.payload;
      if (layoutName !== 'default' && state.savedLayouts[layoutName]) {
        delete state.savedLayouts[layoutName];
        if (state.activeLayout === layoutName) {
          state.activeLayout = 'default';
          state.layout = state.savedLayouts.default;
        }
      }
    },

    // 重置为默认布局
    resetLayout: (state) => {
      state.layout = defaultLayout;
      state.activeLayout = 'default';

      // 保存到本地存储
      try {
        localStorage.setItem('dl-engine-editor-layout', JSON.stringify(defaultLayout));
        localStorage.setItem('dl-engine-editor-active-layout', 'default');
      } catch (error) {
        console.error('保存布局到本地存储失败:', error);
      }
    },

    // 切换主题
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },

    // 保存布局到本地存储
    saveLayoutToStorage: (state, action: PayloadAction<LayoutData>) => {
      try {
        localStorage.setItem('dl-engine-editor-layout', JSON.stringify(action.payload));
        localStorage.setItem('dl-engine-editor-saved-layouts', JSON.stringify(state.savedLayouts));
        localStorage.setItem('dl-engine-editor-active-layout', state.activeLayout);
      } catch (error) {
        console.error('保存布局到本地存储失败:', error);
      }
    },

    // 从本地存储加载布局
    loadLayoutFromStorage: (state) => {
      try {
        const savedLayout = localStorage.getItem('dl-engine-editor-layout');
        const savedLayouts = localStorage.getItem('dl-engine-editor-saved-layouts');
        const activeLayout = localStorage.getItem('dl-engine-editor-active-layout');

        if (savedLayout) {
          state.layout = JSON.parse(savedLayout);
        } else {
          state.layout = defaultLayout;
        }

        if (savedLayouts) {
          state.savedLayouts = JSON.parse(savedLayouts);
        }

        if (activeLayout) {
          state.activeLayout = activeLayout;
        }
      } catch (error) {
        console.error('从本地存储加载布局失败:', error);
        state.layout = defaultLayout;
      }
    }
  }
});

// 导出actions
export const {
  setLayout,
  saveLayout,
  loadLayout,
  deleteLayout,
  resetLayout,
  toggleTheme,
  saveLayoutToStorage,
  loadLayoutFromStorage
} = layoutSlice.actions;

// 导出reducer
export default layoutSlice.reducer;
