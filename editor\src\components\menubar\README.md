# 编辑器菜单栏组件

## 概述

编辑器菜单栏是位于编辑器界面最顶部的组件，提供了传统桌面应用程序风格的菜单系统。根据图片中显示的界面设计，菜单栏包含文件、编辑、视图、工具、帮助等主要菜单项，以及项目路径显示和发布按钮。

## 功能特性

### 🎯 核心功能
- **文件菜单**: 新建、打开、保存、导出、导入项目和场景
- **编辑菜单**: 撤销、重做、复制、粘贴、删除操作
- **视图菜单**: 显示选项控制，坐标空间切换
- **工具菜单**: 变换工具选择，播放控制
- **帮助菜单**: 文档、教程、关于信息

### 🎨 界面设计
- **传统菜单风格**: 符合桌面应用程序的用户习惯
- **暗色主题**: 与编辑器整体风格保持一致
- **响应式布局**: 在小屏幕上隐藏项目路径
- **图标支持**: 每个菜单项都有对应的图标

### ⚡ 交互体验
- **下拉菜单**: 点击菜单项显示子菜单
- **快捷键显示**: 菜单项显示对应的快捷键
- **状态反馈**: 菜单项状态实时反映编辑器状态
- **工具提示**: 悬停显示详细说明

## 文件结构

```
menubar/
├── MenuBar.tsx          # 主组件文件
├── MenuBar.less         # 样式文件
├── README.md           # 说明文档
└── __tests__/
    └── MenuBar.test.tsx # 测试文件
```

## 组件架构

### 布局结构
```
菜单栏 (32px高度)
├── 左侧菜单区域
│   ├── 文件菜单
│   ├── 编辑菜单
│   ├── 视图菜单
│   ├── 工具菜单
│   └── 帮助菜单
├── 中央项目路径
│   └── 项目名称显示
└── 右侧操作区域
    ├── 用户按钮
    └── 发布按钮
```

### 状态管理
- **Editor State**: 变换模式、显示选项、播放状态等
- **UI State**: 对话框状态、通知等
- **Redux Integration**: 完整的状态同步和更新

## 菜单功能映射

### 文件菜单
| 菜单项 | 功能 | 快捷键 | 图标 |
|--------|------|--------|------|
| 新建项目 | 创建新项目 | Ctrl+N | PlusOutlined |
| 打开项目 | 打开现有项目 | Ctrl+O | FolderOpenOutlined |
| 保存场景 | 保存当前场景 | Ctrl+S | SaveOutlined |
| 另存为 | 场景另存为 | Ctrl+Shift+S | - |
| 导出场景 | 导出为GLTF等格式 | - | ExportOutlined |
| 导入场景 | 导入场景文件 | - | ImportOutlined |

### 编辑菜单
| 菜单项 | 功能 | 快捷键 | 图标 |
|--------|------|--------|------|
| 撤销 | 撤销上一步操作 | Ctrl+Z | UndoOutlined |
| 重做 | 重做操作 | Ctrl+Y | RedoOutlined |
| 复制 | 复制选中对象 | Ctrl+C | CopyOutlined |
| 粘贴 | 粘贴对象 | Ctrl+V | ScissorOutlined |
| 删除 | 删除选中对象 | Delete | DeleteOutlined |

### 视图菜单
| 菜单项 | 功能 | 快捷键 | 图标 |
|--------|------|--------|------|
| 显示/隐藏网格 | 切换网格显示 | G | BorderOutlined |
| 显示/隐藏坐标轴 | 切换坐标轴显示 | - | EyeOutlined |
| 世界空间 | 切换到世界坐标系 | - | GlobalOutlined |
| 本地空间 | 切换到本地坐标系 | - | HomeOutlined |

### 工具菜单
| 菜单项 | 功能 | 快捷键 | 图标 |
|--------|------|--------|------|
| 选择工具 | 激活选择模式 | Q | SelectOutlined |
| 移动工具 | 激活移动模式 | W | AppstoreOutlined |
| 旋转工具 | 激活旋转模式 | E | UndoOutlined |
| 缩放工具 | 激活缩放模式 | R | ScissorOutlined |
| 播放/暂停 | 控制场景播放 | Space | PlayCircleOutlined |

### 帮助菜单
| 菜单项 | 功能 | 快捷键 | 图标 |
|--------|------|--------|------|
| 文档 | 打开用户文档 | F1 | BookOutlined |
| 教程 | 打开教程页面 | - | QuestionCircleOutlined |
| 关于 | 显示关于信息 | - | QuestionCircleOutlined |

## 样式系统

### CSS类名规范
- `.editor-menubar`: 主容器
- `.menubar-left`: 左侧菜单区域
- `.menubar-center`: 中央项目路径
- `.menubar-right`: 右侧操作区域
- `.menu-button`: 菜单按钮
- `.project-path`: 项目路径显示
- `.publish-button`: 发布按钮

### 主题变量
- 背景色: `#2a2a2a`
- 边框色: `#3a3a3a`
- 文字色: `#ccc` / `#fff`
- 悬停色: `rgba(255, 255, 255, 0.1)`
- 主色调: `#1890ff`

## 使用方法

### 基本使用
```tsx
import MenuBar from './components/menubar/MenuBar';

function EditorLayout() {
  return (
    <div className="editor-layout">
      <MenuBar />
      {/* 其他编辑器组件 */}
    </div>
  );
}
```

### 状态集成
菜单栏自动与Redux状态管理系统集成，菜单项的状态会根据编辑器状态自动更新。

### 自定义样式
```less
.editor-menubar {
  // 自定义菜单栏样式
  .menu-button {
    // 自定义菜单按钮样式
  }
}
```

## 与工具栏的关系

### 布局层次
```
编辑器界面
├── 菜单栏 (32px) - 传统菜单功能
├── 工具栏 (40px) - 快速工具按钮
└── 主编辑区域
```

### 功能分工
- **菜单栏**: 提供完整的功能访问，适合不常用但重要的功能
- **工具栏**: 提供常用工具的快速访问，适合频繁使用的功能

### 状态同步
菜单栏和工具栏共享相同的Redux状态，确保功能状态的一致性。

## 测试

### 运行测试
```bash
npm test -- MenuBar.test.tsx
```

### 测试覆盖
- ✅ 组件渲染
- ✅ 菜单项显示
- ✅ 下拉菜单功能
- ✅ 状态更新
- ✅ 响应式布局

## 扩展性

### 添加新菜单
1. 在相应的菜单数组中添加菜单项
2. 实现菜单项的点击处理函数
3. 更新状态管理
4. 添加样式和测试

### 自定义菜单项
菜单项支持图标、快捷键显示、禁用状态等自定义选项。

## 依赖项

- React 18+
- Ant Design 5+
- Redux Toolkit
- React Router
- Less

## 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 更新日志

### v1.0.0
- ✅ 完整的菜单栏功能实现
- ✅ 传统桌面应用程序风格
- ✅ 与工具栏的完美配合
- ✅ 响应式设计支持
- ✅ 完整的测试覆盖
