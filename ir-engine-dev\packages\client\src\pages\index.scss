@mixin purple-pink-linear-gradient() {
  background: linear-gradient(90deg, var(--buttonGradientStart), var(--buttonGradientEnd));
}

.lander {
  height: 100%;
  background-image: linear-gradient(150deg, #00022e, #ff007d4a);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  * {
    font-family: Figtree, sans-serif;
    font-style: normal;
    font-weight: 400;
    z-index: 1;
  }

  .main-background {
    position: absolute;
    bottom: 0;
    z-index: 0;

    img {
      display: block;
    }

    @media (max-width: 550px) {
      .img-container {
        position: absolute;
        width: 700px;
        bottom: 0;
        left: -100px;
      }
    }

    @media (min-width: 769px) {
      bottom: -75px;
    }

    @media (min-width: 950px) {
      bottom: -175px;
    }

    @media (min-width: 1250px) {
      top: 130px;
      bottom: 0;
      width: 100%;

      .img-container,
      img {
        width: 100%;
      }
    }
  }

  .logo-bottom {
    text-align: right;
    margin-top: 15px;
    font-size: 14px;

    span {
      margin-right: 5px;
    }

    .gray-txt {
      color: grey;
    }

    .gradiant-txt {
      @include purple-pink-linear-gradient;

      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
    }

    .white-txt {
      color: white;
      margin-right: 0;
    }
  }

  .link-container {
    position: absolute;
    right: 30px;
    top: 35px;

    .link-block {
      display: flex;

      .icon {
        width: 30px;
      }

      .icon + .icon {
        margin-left: 20px;
      }

      .icon:hover,
      .icon:focus {
        opacity: 0.8;
      }
    }

    .logo-bottom {
      display: none;
    }

    @media (max-width: 550px) {
      width: 100%;
      display: flex;
      position: relative;
      justify-content: center;
      flex-direction: column;
      align-items: center;
      right: 0;
      top: 0;
      padding: 20px;
      background: #000c;

      .logo-bottom {
        display: block;
      }
    }
  }

  .navbar {
    padding: 30px 30px 15px;
    background: #0002;

    .logo-section {
      display: inline-block;

      .lander-logo {
        width: auto;
        height: 50px;
        max-width: 250px;
      }
    }

    @media (max-width: 550px) {
      text-align: center;
    }
  }

  .main-section {
    display: flex;
    flex-direction: column;
    padding: 5px;
    max-width: 1024px;
    margin: auto;
    width: 100%;
    overflow-y: auto;

    .desc {
      font-size: 30px;
      margin: 10px 10px 60px;
      color: white;
      max-width: 400px;

      @media (max-width: 550px) {
        font-size: 20px;
        text-align: center;
        margin: 10px 0 60px;
      }
    }

    @media (min-width: 850px) {
      flex-direction: row;
      align-items: flex-start;

      .desc {
        width: 400px;
        flex-shrink: 0;
        margin-top: 100px;
      }
    }
  }
}


.gradientButton {
  background: linear-gradient(90deg, #5236ff, #c20560);
  color: #fff !important;
  width: 100%;

  &:hover {
    opacity: 0.8;
  }

  &:disabled {
    opacity: 0.5;
  }
}
