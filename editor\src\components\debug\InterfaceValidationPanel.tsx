/**
 * 界面验证面板组件
 * 用于显示界面功能验证结果
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Progress, Table, Tag, Space, Typography, Collapse } from 'antd';
import {
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { interfaceValidator, ValidationResult } from '../../utils/interfaceValidator';

const { Title, Text } = Typography;
const { Panel } = Collapse;

const InterfaceValidationPanel: React.FC = () => {
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    success: 0,
    warning: 0,
    error: 0,
    successRate: 0
  });

  // 执行验证
  const runValidation = async () => {
    setLoading(true);
    try {
      // 模拟异步验证过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const results = interfaceValidator.validateAll();
      const validationStats = interfaceValidator.getValidationStats();
      
      setValidationResults(results);
      setStats(validationStats);
    } catch (error) {
      console.error('验证过程出错:', error);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时自动执行验证
  useEffect(() => {
    runValidation();
  }, []);

  // 获取状态图标
  // const getStatusIcon = (status: string) => {
  //   switch (status) {
  //     case 'success':
  //       return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
  //     case 'warning':
  //       return <WarningOutlined style={{ color: '#faad14' }} />;
  //     case 'error':
  //       return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
  //     default:
  //       return null;
  //   }
  // };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const colors = {
      success: 'success',
      warning: 'warning',
      error: 'error'
    };
    
    const texts = {
      success: '正常',
      warning: '警告',
      error: '错误'
    };

    return (
      <Tag color={colors[status as keyof typeof colors]}>
        {texts[status as keyof typeof texts]}
      </Tag>
    );
  };

  // 表格列定义
  const columns = [
    {
      title: '组件',
      dataIndex: 'component',
      key: 'component',
      width: 120,
    },
    {
      title: '功能',
      dataIndex: 'feature',
      key: 'feature',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '说明',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true,
    },
  ];

  // 按组件分组结果
  const groupedResults = validationResults.reduce((groups, result) => {
    if (!groups[result.component]) {
      groups[result.component] = [];
    }
    groups[result.component].push(result);
    return groups;
  }, {} as Record<string, ValidationResult[]>);

  // 下载报告
  const downloadReport = () => {
    const report = interfaceValidator.generateReport();
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'interface-validation-report.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <div style={{ marginBottom: '20px' }}>
          <Title level={3}>界面功能验证</Title>
          <Text type="secondary">
            检查编辑器界面中各个功能的可用性和关联性
          </Text>
        </div>

        {/* 操作按钮 */}
        <Space style={{ marginBottom: '20px' }}>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            loading={loading}
            onClick={runValidation}
          >
            重新验证
          </Button>
          <Button 
            icon={<DownloadOutlined />}
            onClick={downloadReport}
            disabled={validationResults.length === 0}
          >
            下载报告
          </Button>
        </Space>

        {/* 统计信息 */}
        <Card size="small" style={{ marginBottom: '20px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <Text strong>验证统计</Text>
              <div style={{ marginTop: '8px' }}>
                <Space>
                  <Text>总计: {stats.total}</Text>
                  <Text style={{ color: '#52c41a' }}>成功: {stats.success}</Text>
                  <Text style={{ color: '#faad14' }}>警告: {stats.warning}</Text>
                  <Text style={{ color: '#f5222d' }}>错误: {stats.error}</Text>
                </Space>
              </div>
            </div>
            <div style={{ width: '200px' }}>
              <Text>成功率</Text>
              <Progress 
                percent={stats.successRate} 
                status={stats.successRate >= 90 ? 'success' : stats.successRate >= 70 ? 'normal' : 'exception'}
                format={percent => `${percent}%`}
              />
            </div>
          </div>
        </Card>

        {/* 验证结果 */}
        <Collapse defaultActiveKey={Object.keys(groupedResults)}>
          {Object.entries(groupedResults).map(([component, results]) => {
            const componentStats = {
              total: results.length,
              success: results.filter(r => r.status === 'success').length,
              warning: results.filter(r => r.status === 'warning').length,
              error: results.filter(r => r.status === 'error').length,
            };

            return (
              <Panel 
                key={component}
                header={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Text strong>{component}</Text>
                    <Space>
                      {componentStats.success > 0 && (
                        <Tag color="success">{componentStats.success}</Tag>
                      )}
                      {componentStats.warning > 0 && (
                        <Tag color="warning">{componentStats.warning}</Tag>
                      )}
                      {componentStats.error > 0 && (
                        <Tag color="error">{componentStats.error}</Tag>
                      )}
                    </Space>
                  </div>
                }
              >
                <Table
                  dataSource={results}
                  columns={columns}
                  pagination={false}
                  size="small"
                  rowKey={(record, index) => `${record.component}-${record.feature}-${index}`}
                />
              </Panel>
            );
          })}
        </Collapse>

        {/* 空状态 */}
        {validationResults.length === 0 && !loading && (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Text type="secondary">暂无验证结果，请点击"重新验证"按钮开始验证</Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default InterfaceValidationPanel;
