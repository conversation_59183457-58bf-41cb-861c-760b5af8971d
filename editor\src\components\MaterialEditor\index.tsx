/**
 * 材质编辑器组件
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Tabs,
  Form,
  Input,
  Select,
  Button,
  Slider,
  message,
  Switch,
  Upload,
  Space,
  Tooltip,
  Card
} from 'antd';
import {
  UploadOutlined,
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { SketchPicker } from 'react-color';
import { useTranslation } from 'react-i18next';
// import { useSelector } from 'react-redux';
// import { RootState } from '../../store';
import { MaterialType } from '../../store/materials/materialsSlice';
import { materialService } from '../../services/materialService';
import './MaterialEditor.less';

const { TabPane } = Tabs;
const { Option } = Select;

// 材质类型选项
const materialTypeOptions = [
  { value: MaterialType.STANDARD, label: '标准材质 (Standard)' },
  { value: MaterialType.PHYSICAL, label: '物理材质 (PBR)' },
  { value: MaterialType.BASIC, label: '基础材质 (Basic)' },
  { value: MaterialType.LAMBERT, label: 'Lambert材质' },
  { value: MaterialType.PHONG, label: 'Phong材质' },
  { value: MaterialType.TOON, label: '卡通材质 (Toon)' },
];

// 纹理类型选项
const textureTypeOptions = [
  { value: 'map', label: '颜色贴图 (Diffuse)' },
  { value: 'normalMap', label: '法线贴图 (Normal)' },
  { value: 'bumpMap', label: '凹凸贴图 (Bump)' },
  { value: 'roughnessMap', label: '粗糙度贴图 (Roughness)' },
  { value: 'metalnessMap', label: '金属度贴图 (Metalness)' },
  { value: 'aoMap', label: '环境光遮蔽贴图 (AO)' },
  { value: 'emissiveMap', label: '自发光贴图 (Emissive)' },
  { value: 'displacementMap', label: '位移贴图 (Displacement)' },
  { value: 'envMap', label: '环境贴图 (Environment)' },
  { value: 'lightMap', label: '光照贴图 (Light Map)' },
  { value: 'alphaMap', label: '透明度贴图 (Alpha)' },
  { value: 'specularMap', label: '高光贴图 (Specular)' },
];

// 预览几何体选项
const previewGeometryOptions = [
  { value: 'sphere', label: '球体', icon: '🌐' },
  { value: 'cube', label: '立方体', icon: '📦' },
  { value: 'plane', label: '平面', icon: '📄' },
  { value: 'cylinder', label: '圆柱体', icon: '🥫' },
  { value: 'torus', label: '圆环', icon: '🍩' },
];

// 材质面选项
const materialSideOptions = [
  { value: 'front', label: '正面' },
  { value: 'back', label: '背面' },
  { value: 'double', label: '双面' },
];

// 混合模式选项 (暂未使用，保留以备将来扩展)
// const blendingModeOptions = [
//   { value: 'normal', label: '正常' },
//   { value: 'additive', label: '相加' },
//   { value: 'subtractive', label: '相减' },
//   { value: 'multiply', label: '相乘' },
// ];

interface MaterialEditorProps {
  materialId?: string;
  onSave?: (material: any) => void;
  onCancel?: () => void;
  readonly?: boolean;
}

const MaterialEditor: React.FC<MaterialEditorProps> = ({
  materialId, // 用于加载和保存材质
  onSave, // 保存回调
  onCancel, // 取消回调
  readonly = false // 只读模式标志
}) => {
  const { t } = useTranslation();
  // const dispatch = useDispatch(); // 暂未使用，保留以备将来扩展
  const [form] = Form.useForm();
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<any>(null);
  const previewSceneRef = useRef<any>(null);
  const previewMeshRef = useRef<any>(null);

  // 材质状态
  const [materialType, setMaterialType] = useState<MaterialType>(MaterialType.STANDARD);
  const [colorPickerVisible, setColorPickerVisible] = useState<{ [key: string]: boolean }>({
    color: false,
    emissive: false,
    specular: false
  });
  const [previewMode, setPreviewMode] = useState<'sphere' | 'cube' | 'plane' | 'cylinder' | 'torus'>('sphere');
  const [loading, setLoading] = useState(false);
  const [textureUploading, setTextureUploading] = useState<{ [key: string]: boolean }>({});
  const [previewEnvironment, setPreviewEnvironment] = useState<'studio' | 'outdoor' | 'indoor'>('studio');

  // 从Redux获取材质数据 (暂未使用，保留以备将来扩展)
  // const material = useSelector((state: RootState) => {
  //   if (!materialId) return null;
  //   // 从材质状态中获取材质数据
  //   return state.materials?.materials?.find(m => m.id === materialId) || null;
  // });
  
  // 初始化引擎和预览
  useEffect(() => {
    initializeEngine();
    return () => {
      cleanupEngine();
    };
  }, []);

  // 应用纹理到材质
  const applyTexturesToMaterial = useCallback((material: any, textures: any[]) => {
    if (!material || !textures) return;

    textures.forEach(texture => {
      if (texture.url && texture.type) {
        try {
          // 加载纹理
          const textureLoader = engineRef.current?.getTextureLoader();
          if (textureLoader) {
            const loadedTexture = textureLoader.load(texture.url);
            // 设置纹理属性
            material.setTexture(texture.type, loadedTexture);
          }
        } catch (error) {
          console.error(`加载纹理失败: ${texture.url}`, error);
        }
      }
    });
  }, []);

  // 创建预览对象
  const createPreviewObject = useCallback((mode: 'sphere' | 'cube' | 'plane' | 'cylinder' | 'torus') => {
    if (!engineRef.current || !previewSceneRef.current) return;

    try {
      // 移除现有的预览对象
      if (previewMeshRef.current) {
        previewSceneRef.current.removeEntity(previewMeshRef.current);
        previewMeshRef.current = null;
      }

      // 创建新的预览对象
      let geometry;
      switch (mode) {
        case 'sphere':
          geometry = engineRef.current.createGeometry({
            type: 'sphere',
            radius: 1,
            widthSegments: 32,
            heightSegments: 32
          });
          break;
        case 'cube':
          geometry = engineRef.current.createGeometry({
            type: 'box',
            width: 1.5,
            height: 1.5,
            depth: 1.5
          });
          break;
        case 'plane':
          geometry = engineRef.current.createGeometry({
            type: 'plane',
            width: 2,
            height: 2
          });
          break;
        case 'cylinder':
          geometry = engineRef.current.createGeometry({
            type: 'cylinder',
            radiusTop: 1,
            radiusBottom: 1,
            height: 2,
            radialSegments: 32
          });
          break;
        case 'torus':
          geometry = engineRef.current.createGeometry({
            type: 'torus',
            radius: 1,
            tube: 0.4,
            radialSegments: 16,
            tubularSegments: 32
          });
          break;
        default:
          geometry = engineRef.current.createGeometry({
            type: 'sphere',
            radius: 1,
            widthSegments: 32,
            heightSegments: 32
          });
      }

      // 获取当前表单值
      const formValues = form.getFieldsValue();

      // 创建材质
      const materialConfig = {
        type: materialType,
        color: formValues.color || '#ffffff',
        metalness: formValues.metalness || 0,
        roughness: formValues.roughness || 0.5,
        emissive: formValues.emissive || '#000000',
        emissiveIntensity: formValues.emissiveIntensity || 0,
        transparent: formValues.transparent || false,
        opacity: formValues.opacity || 1,
        side: formValues.side || 'front'
      };

      const material = engineRef.current.createMaterial(materialConfig);

      // 应用纹理
      if (formValues.textures && formValues.textures.length > 0) {
        applyTexturesToMaterial(material, formValues.textures);
      }

      // 创建网格
      previewMeshRef.current = engineRef.current.createMesh({
        geometry,
        material
      });

      // 添加到场景
      previewSceneRef.current.addEntity(previewMeshRef.current);

    } catch (error) {
      console.error('创建预览对象失败:', error);
    }
  }, [materialType, form, applyTexturesToMaterial]);

  // 更新预览
  const updatePreview = useCallback((materialData: any) => {
    if (!engineRef.current || !previewMeshRef.current) return;

    try {
      // 获取预览对象的材质组件
      const materialComponent = previewMeshRef.current.getComponent('material');
      if (!materialComponent) return;

      // 更新材质属性
      Object.keys(materialData).forEach(key => {
        if (key !== 'id' && key !== 'name' && key !== 'type' && key !== 'textures' && key !== 'createdAt' && key !== 'updatedAt') {
          try {
            materialComponent.setProperty(key, materialData[key]);
          } catch (error) {
            console.warn(`设置材质属性失败: ${key}`, error);
          }
        }
      });

      // 应用纹理
      if (materialData.textures && materialData.textures.length > 0) {
        applyTexturesToMaterial(materialComponent, materialData.textures);
      }

      // 如果材质类型变化，重新创建材质
      if (materialData.type && materialData.type !== materialType) {
        setMaterialType(materialData.type);
        createPreviewObject(previewMode);
      }

    } catch (error) {
      console.error('更新预览失败:', error);
    }
  }, [materialType, previewMode, createPreviewObject, applyTexturesToMaterial]);

  // 重置为默认值
  const resetToDefault = useCallback(() => {
    const defaultMaterial = {
      name: '新材质',
      type: MaterialType.STANDARD,
      color: '#ffffff',
      metalness: 0,
      roughness: 0.5,
      emissive: '#000000',
      emissiveIntensity: 0,
      transparent: false,
      opacity: 1,
      side: 'front',
      textures: []
    };

    form.setFieldsValue(defaultMaterial);
    setMaterialType(MaterialType.STANDARD);
    updatePreview(defaultMaterial);
  }, [form, updatePreview]);

  // 加载材质
  const loadMaterial = useCallback(async (id: string) => {
    if (!id) return;

    setLoading(true);
    try {
      // 从服务获取材质数据
      const materialData = await materialService.getMaterial(id);

      if (materialData) {
        // 设置表单值
        form.setFieldsValue(materialData);
        setMaterialType(materialData.type || MaterialType.STANDARD);

        // 更新预览
        updatePreview(materialData);
      }
    } catch (error) {
      console.error('加载材质失败:', error);
      message.error('加载材质失败');
    } finally {
      setLoading(false);
    }
  }, [form]);

  // 当材质ID变化时加载材质
  useEffect(() => {
    if (materialId) {
      loadMaterial(materialId);
    } else {
      // 重置表单为默认值
      resetToDefault();
    }
  }, [materialId, loadMaterial, resetToDefault]);

  // 初始化引擎
  const initializeEngine = useCallback(async () => {
    if (!previewCanvasRef.current) return;

    try {
      // 动态导入引擎（从本地 libs 包）
      const { Engine } = await import('../../libs/dl-engine');

      // 创建引擎实例
      engineRef.current = new Engine({
        canvas: previewCanvasRef.current,
        // width: previewCanvasRef.current.clientWidth, // 暂时注释掉，因为 EngineOptions 中没有 width 属性
        // height: previewCanvasRef.current.clientHeight, // 暂时注释掉，因为 EngineOptions 中没有 height 属性
        // antialias: true, // 暂时注释掉，因为 EngineOptions 中没有 antialias 属性
        // alpha: true // 暂时注释掉，因为 EngineOptions 中没有 alpha 属性
      });

      // 设置预览场景
      await setupPreviewScene();

    } catch (error) {
      console.error('引擎初始化失败:', error);
      message.error('预览引擎初始化失败');
    }
  }, []);

  // 清理引擎
  const cleanupEngine = useCallback(() => {
    if (engineRef.current) {
      engineRef.current.dispose();
      engineRef.current = null;
    }
    if (previewSceneRef.current) {
      previewSceneRef.current = null;
    }
    if (previewMeshRef.current) {
      previewMeshRef.current = null;
    }
  }, []);

  // 设置预览场景
  const setupPreviewScene = useCallback(async () => {
    if (!engineRef.current) return;

    try {
      // 创建场景、相机和灯光
      previewSceneRef.current = engineRef.current.createScene();
      engineRef.current.createCamera({
        type: 'perspective',
        position: [0, 0, 5],
        lookAt: [0, 0, 0]
      });

      // 添加环境光和方向光
      engineRef.current.createLight({
        type: 'ambient',
        intensity: 0.4
      });

      engineRef.current.createLight({
        type: 'directional',
        intensity: 0.8,
        position: [1, 1, 1],
        castShadow: true
      });

      // 根据环境设置添加额外光源
      setupEnvironmentLighting();

      // 创建预览对象
      createPreviewObject(previewMode);

      // 启动引擎
      engineRef.current.start();

    } catch (error) {
      console.error('预览场景设置失败:', error);
    }
  }, [previewMode]);

  // 设置环境光照
  const setupEnvironmentLighting = useCallback(() => {
    if (!engineRef.current) return;

    switch (previewEnvironment) {
      case 'studio':
        // 工作室环境：柔和均匀的光照
        engineRef.current.createLight({
          type: 'point',
          intensity: 0.6,
          position: [-2, 2, 2],
          color: '#ffffff'
        });
        break;
      case 'outdoor':
        // 户外环境：强烈的方向光
        engineRef.current.createLight({
          type: 'directional',
          intensity: 1.2,
          position: [1, 3, 1],
          color: '#fff8dc'
        });
        break;
      case 'indoor':
        // 室内环境：温暖的点光源
        engineRef.current.createLight({
          type: 'point',
          intensity: 0.8,
          position: [0, 2, 2],
          color: '#fff5ee'
        });
        break;
    }
  }, [previewEnvironment]);

  // 处理表单值变化
  const handleValuesChange = useCallback((changedValues: any, allValues: any) => {
    // 如果材质类型变化，更新预览对象
    if (changedValues.type && changedValues.type !== materialType) {
      setMaterialType(changedValues.type);
      createPreviewObject(previewMode);
    } else {
      // 否则只更新材质属性
      updatePreview(allValues);
    }
  }, [materialType, previewMode, createPreviewObject, updatePreview]);

  // 处理保存
  const handleSave = useCallback(async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const materialData = {
        id: materialId,
        ...values,
        updatedAt: new Date().toISOString()
      };

      if (materialId) {
        // 更新现有材质
        await materialService.updateMaterial(materialId, materialData);
        message.success('材质更新成功');
      } else {
        // 创建新材质
        const newMaterial = await materialService.createMaterial(materialData);
        materialData.id = newMaterial.id;
        message.success('材质创建成功');
      }

      if (onSave) {
        onSave(materialData);
      }
    } catch (error) {
      console.error('保存材质失败:', error);
      message.error('保存材质失败');
    } finally {
      setLoading(false);
    }
  }, [form, materialId, onSave]);

  // 处理颜色变化
  const handleColorChange = useCallback((colorType: string, color: any) => {
    const colorValue = color.hex || color;
    form.setFieldValue(colorType, colorValue);
    updatePreview({ [colorType]: colorValue });
  }, [form, updatePreview]);

  // 处理预览模式变化
  const handlePreviewModeChange = useCallback((mode: 'sphere' | 'cube' | 'plane' | 'cylinder' | 'torus') => {
    setPreviewMode(mode);
    createPreviewObject(mode);
  }, [createPreviewObject]);

  // 处理环境变化
  const handleEnvironmentChange = useCallback((environment: 'studio' | 'outdoor' | 'indoor') => {
    setPreviewEnvironment(environment);
    setupEnvironmentLighting();
  }, [setupEnvironmentLighting]);

  // 处理纹理上传
  const handleTextureUpload = useCallback(async (textureType: string, file: File) => {
    setTextureUploading(prev => ({ ...prev, [textureType]: true }));

    try {
      // 这里应该实现实际的文件上传逻辑
      // 暂时使用本地URL
      const url = URL.createObjectURL(file);

      // 更新表单中的纹理数据
      const textures = form.getFieldValue('textures') || [];
      const existingIndex = textures.findIndex((t: any) => t.type === textureType);

      if (existingIndex >= 0) {
        textures[existingIndex].url = url;
      } else {
        textures.push({ type: textureType, url });
      }

      form.setFieldValue('textures', textures);

      // 更新预览
      const formValues = form.getFieldsValue();
      updatePreview(formValues);

      message.success('纹理上传成功');
    } catch (error) {
      console.error('纹理上传失败:', error);
      message.error('纹理上传失败');
    } finally {
      setTextureUploading(prev => ({ ...prev, [textureType]: false }));
    }
  }, [form, updatePreview]);
  
  return (
    <div className="material-editor">
      <div className="material-editor-header">
        <h2>{materialId ? t('editor.materialLibrary.editor.edit') : t('editor.materialLibrary.editor.create')}</h2>
        <div className="material-editor-actions">
          <Button onClick={onCancel}>{t('common.cancel')}</Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={loading}
          >
            {t('common.save')}
          </Button>
        </div>
      </div>
      
      <div className="material-editor-content">
        <div className="material-editor-preview">
          <div className="preview-header">
            <Space>
              <Tooltip title={t('editor.materialLibrary.editor.reloadPreview')}>
                <Button
                  icon={<ReloadOutlined />}
                  size="small"
                  onClick={() => createPreviewObject(previewMode)}
                />
              </Tooltip>
              <Tooltip title={t('editor.materialLibrary.editor.previewSettings')}>
                <Button icon={<SettingOutlined />} size="small" />
              </Tooltip>
            </Space>
          </div>

          <canvas ref={previewCanvasRef} className="preview-canvas" />

          <div className="preview-controls">
            <div className="preview-geometry">
              <span className="control-label">几何体:</span>
              <Space size="small">
                {previewGeometryOptions.map(option => (
                  <Tooltip key={option.value} title={option.label}>
                    <Button
                      size="small"
                      type={previewMode === option.value ? 'primary' : 'default'}
                      onClick={() => handlePreviewModeChange(option.value as any)}
                    >
                      {option.icon}
                    </Button>
                  </Tooltip>
                ))}
              </Space>
            </div>

            <div className="preview-environment">
              <span className="control-label">环境:</span>
              <Select
                size="small"
                value={previewEnvironment}
                onChange={handleEnvironmentChange}
                style={{ width: 100 }}
              >
                <Option value="studio">工作室</Option>
                <Option value="outdoor">户外</Option>
                <Option value="indoor">室内</Option>
              </Select>
            </div>
          </div>
        </div>
        
        <div className="material-editor-form">
          <Form
            form={form}
            layout="vertical"
            onValuesChange={handleValuesChange}
          >
            <Form.Item name="name" label={t('editor.materialLibrary.editor.name')} rules={[{ required: true }]}>
              <Input disabled={readonly} />
            </Form.Item>

            <Form.Item name="type" label={t('editor.materialLibrary.editor.type')} rules={[{ required: true }]}>
              <Select disabled={readonly}>
                {materialTypeOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Tabs defaultActiveKey="basic">
              <TabPane tab={t('editor.materialLibrary.editor.basicProps')} key="basic">
                <Form.Item name="color" label={t('editor.materialLibrary.editor.color')}>
                  <div className="color-picker-field">
                    <div
                      className="color-preview"
                      style={{ backgroundColor: form.getFieldValue('color') || '#ffffff' }}
                      onClick={() => setColorPickerVisible({ ...colorPickerVisible, color: !colorPickerVisible.color })}
                    />
                    <Input
                      value={form.getFieldValue('color')}
                      disabled={readonly}
                      onChange={e => {
                        form.setFieldValue('color', e.target.value);
                        updatePreview({ color: e.target.value });
                      }}
                    />
                    {colorPickerVisible.color && (
                      <div className="color-picker-popover">
                        <div
                          className="color-picker-cover"
                          onClick={() => setColorPickerVisible({ ...colorPickerVisible, color: false })}
                        />
                        <SketchPicker
                          color={form.getFieldValue('color') || '#ffffff'}
                          onChange={color => handleColorChange('color', color)}
                        />
                      </div>
                    )}
                  </div>
                </Form.Item>
                
                {(materialType === MaterialType.STANDARD || materialType === MaterialType.PHYSICAL) && (
                  <>
                    <Form.Item name="metalness" label={t('editor.materialLibrary.editor.metalness')}>
                      <Slider min={0} max={1} step={0.01} disabled={readonly} />
                    </Form.Item>

                    <Form.Item name="roughness" label={t('editor.materialLibrary.editor.roughness')}>
                      <Slider min={0} max={1} step={0.01} disabled={readonly} />
                    </Form.Item>
                  </>
                )}

                <Form.Item name="emissive" label={t('editor.materialLibrary.editor.emissive')}>
                  <div className="color-picker-field">
                    <div
                      className="color-preview"
                      style={{ backgroundColor: form.getFieldValue('emissive') || '#000000' }}
                      onClick={() => setColorPickerVisible({ ...colorPickerVisible, emissive: !colorPickerVisible.emissive })}
                    />
                    <Input
                      value={form.getFieldValue('emissive')}
                      onChange={e => {
                        form.setFieldValue('emissive', e.target.value);
                        updatePreview({ emissive: e.target.value });
                      }}
                    />
                    {colorPickerVisible.emissive && (
                      <div className="color-picker-popover">
                        <div
                          className="color-picker-cover"
                          onClick={() => setColorPickerVisible({ ...colorPickerVisible, emissive: false })}
                        />
                        <SketchPicker
                          color={form.getFieldValue('emissive') || '#000000'}
                          onChange={color => handleColorChange('emissive', color)}
                        />
                      </div>
                    )}
                  </div>
                </Form.Item>
                
                <Form.Item name="emissiveIntensity" label={t('editor.materialLibrary.editor.emissiveIntensity')}>
                  <Slider min={0} max={10} step={0.1} disabled={readonly} />
                </Form.Item>

                <Form.Item name="transparent" label={t('editor.materialLibrary.editor.transparent')} valuePropName="checked">
                  <Switch disabled={readonly} />
                </Form.Item>

                <Form.Item name="opacity" label={t('editor.materialLibrary.editor.opacity')}>
                  <Slider
                    min={0}
                    max={1}
                    step={0.01}
                    disabled={readonly || !form.getFieldValue('transparent')}
                    marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
                  />
                </Form.Item>

                <Form.Item name="side" label={t('editor.materialLibrary.editor.side')}>
                  <Select disabled={readonly}>
                    {materialSideOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </TabPane>

              <TabPane tab={t('editor.materialLibrary.editor.advanced')} key="advanced">
                <Form.Item name="wireframe" label={t('editor.materialLibrary.editor.wireframe')} valuePropName="checked">
                  <Switch disabled={readonly} />
                </Form.Item>

                <Form.Item name="flatShading" label={t('editor.materialLibrary.editor.flatShading')} valuePropName="checked">
                  <Switch disabled={readonly} />
                </Form.Item>

                <Form.Item name="vertexColors" label={t('editor.materialLibrary.editor.vertexColors')} valuePropName="checked">
                  <Switch disabled={readonly} />
                </Form.Item>

                <Form.Item name="fog" label={t('editor.materialLibrary.editor.fog')} valuePropName="checked">
                  <Switch disabled={readonly} />
                </Form.Item>

                <Form.Item name="alphaTest" label={t('editor.materialLibrary.editor.alphaTest')}>
                  <Slider min={0} max={1} step={0.01} marks={{ 0: '0', 0.5: '0.5', 1: '1' }} disabled={readonly} />
                </Form.Item>

                <Form.Item name="depthTest" label={t('editor.materialLibrary.editor.depthTest')} valuePropName="checked">
                  <Switch defaultChecked disabled={readonly} />
                </Form.Item>

                <Form.Item name="depthWrite" label={t('editor.materialLibrary.editor.depthWrite')} valuePropName="checked">
                  <Switch defaultChecked disabled={readonly} />
                </Form.Item>

                {(materialType === MaterialType.STANDARD || materialType === MaterialType.PHYSICAL) && (
                  <>
                    <Form.Item name="clearcoat" label={t('editor.materialLibrary.editor.clearcoat')}>
                      <Slider min={0} max={1} step={0.01} disabled={readonly} />
                    </Form.Item>

                    <Form.Item name="clearcoatRoughness" label={t('editor.materialLibrary.editor.clearcoatRoughness')}>
                      <Slider min={0} max={1} step={0.01} disabled={readonly} />
                    </Form.Item>

                    <Form.Item name="transmission" label={t('editor.materialLibrary.editor.transmission')}>
                      <Slider min={0} max={1} step={0.01} disabled={readonly} />
                    </Form.Item>

                    <Form.Item name="thickness" label={t('editor.materialLibrary.editor.thickness')}>
                      <Slider min={0} max={5} step={0.1} disabled={readonly} />
                    </Form.Item>
                  </>
                )}
              </TabPane>
              
              <TabPane tab={t('editor.materialLibrary.editor.textures')} key="textures">
                <Form.List name="textures">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(field => (
                        <Card key={field.key} className="texture-item" size="small">
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Form.Item
                              {...field}
                              name={[field.name, 'type']}
                              label={t('editor.materialLibrary.editor.textureType')}
                              rules={[{ required: true }]}
                            >
                              <Select placeholder={t('editor.materialLibrary.editor.selectTextureType')} disabled={readonly}>
                                {textureTypeOptions.map(option => (
                                  <Option key={option.value} value={option.value}>
                                    {option.label}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>

                            <Form.Item
                              {...field}
                              name={[field.name, 'url']}
                              label={t('editor.materialLibrary.editor.textureUrl')}
                              rules={[{ required: true }]}
                            >
                              <Space.Compact style={{ width: '100%' }}>
                                <Input placeholder={t('editor.materialLibrary.editor.textureUrlPlaceholder') as string} disabled={readonly} />
                                <Upload
                                  accept="image/*"
                                  showUploadList={false}
                                  disabled={readonly}
                                  beforeUpload={(file) => {
                                    const textureType = form.getFieldValue(['textures', field.name, 'type']);
                                    if (textureType) {
                                      handleTextureUpload(textureType, file);
                                    } else {
                                      message.warning('请先选择纹理类型');
                                    }
                                    return false;
                                  }}
                                >
                                  <Button
                                    icon={<UploadOutlined />}
                                    loading={textureUploading[form.getFieldValue(['textures', field.name, 'type'])]}
                                    disabled={readonly}
                                  >
                                    上传
                                  </Button>
                                </Upload>
                              </Space.Compact>
                            </Form.Item>

                            <div style={{ textAlign: 'right' }}>
                              <Button
                                type="text"
                                danger
                                size="small"
                                icon={<DeleteOutlined />}
                                onClick={() => remove(field.name)}
                                disabled={readonly}
                              >
                                删除
                              </Button>
                            </div>
                          </Space>
                        </Card>
                      ))}

                      <Form.Item>
                        <Button
                          type="dashed"
                          onClick={() => add({ type: '', url: '' })}
                          block
                          icon={<PlusOutlined />}
                          disabled={readonly}
                        >
                          {t('editor.materialLibrary.editor.addTexture')}
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>
              </TabPane>
            </Tabs>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default MaterialEditor;
