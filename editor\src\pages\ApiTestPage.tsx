/**
 * API测试页面
 * 用于测试各种API端点的连接和功能
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Alert, Divider, Tag, List } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { httpClient } from '../utils/httpClient';
import authService from '../services/AuthService';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  name: string;
  url: string;
  status: 'pending' | 'success' | 'error';
  response?: any;
  error?: string;
  duration?: number;
}

const ApiTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [authInfo, setAuthInfo] = useState<any>(null);

  const apiTests = [
    { name: '健康检查', url: '/health' },
    { name: 'Git状态', url: '/git/status' },
    { name: 'Git分支', url: '/git/branches' },
    { name: 'Git文件', url: '/git/files' },
    { name: 'Git远程仓库', url: '/git/remotes' },
    { name: '项目列表', url: '/projects' },
    { name: '用户信息', url: '/auth/profile' },
    { name: '示例项目', url: '/examples' },
  ];

  useEffect(() => {
    // 获取认证信息
    const user = authService.getUser();
    const token = authService.getToken();
    setAuthInfo({ user, token: token ? token.substring(0, 20) + '...' : null });
  }, []);

  const runSingleTest = async (test: { name: string; url: string }): Promise<TestResult> => {
    const startTime = Date.now();
    
    try {
      const response = await httpClient.get(test.url);
      const duration = Date.now() - startTime;
      
      return {
        name: test.name,
        url: test.url,
        status: 'success',
        response: response.data,
        duration
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      return {
        name: test.name,
        url: test.url,
        status: 'error',
        error: error.response?.data?.message || error.message || '未知错误',
        duration
      };
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // 初始化所有测试为pending状态
    const initialResults: TestResult[] = apiTests.map(test => ({
      name: test.name,
      url: test.url,
      status: 'pending'
    }));
    setTestResults(initialResults);

    // 逐个运行测试
    for (let i = 0; i < apiTests.length; i++) {
      const test = apiTests[i];
      const result = await runSingleTest(test);
      
      setTestResults(prev => 
        prev.map((item, index) => 
          index === i ? result : item
        )
      );
      
      // 添加小延迟以便用户看到进度
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'pending':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'pending':
        return 'processing';
      default:
        return 'default';
    }
  };

  const successCount = testResults.filter(r => r.status === 'success').length;
  const errorCount = testResults.filter(r => r.status === 'error').length;
  const pendingCount = testResults.filter(r => r.status === 'pending').length;

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>API连接测试</Title>
      <Paragraph type="secondary">
        测试前端与API网关的连接状态，验证各个端点是否正常工作
      </Paragraph>

      <Divider />

      {/* 认证信息 */}
      <Card title="认证信息" size="small" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text strong>用户: </Text>
            <Text>{authInfo?.user?.username || '未登录'}</Text>
          </div>
          <div>
            <Text strong>角色: </Text>
            <Tag color={authInfo?.user?.role === 'admin' ? 'red' : 'blue'}>
              {authInfo?.user?.role || '无'}
            </Tag>
          </div>
          <div>
            <Text strong>令牌: </Text>
            <Text code>{authInfo?.token || '无'}</Text>
          </div>
        </Space>
      </Card>

      {/* 测试控制 */}
      <Card title="测试控制" size="small" style={{ marginBottom: '16px' }}>
        <Space>
          <Button 
            type="primary" 
            onClick={runAllTests} 
            loading={isRunning}
            disabled={isRunning}
          >
            {isRunning ? '测试中...' : '运行所有测试'}
          </Button>
          
          {testResults.length > 0 && (
            <Space>
              <Tag color="success">成功: {successCount}</Tag>
              <Tag color="error">失败: {errorCount}</Tag>
              <Tag color="processing">进行中: {pendingCount}</Tag>
            </Space>
          )}
        </Space>
      </Card>

      {/* 测试结果 */}
      {testResults.length > 0 && (
        <Card title="测试结果" size="small">
          <List
            dataSource={testResults}
            renderItem={(item) => (
              <List.Item>
                <Card size="small" style={{ width: '100%' }}>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Space>
                        {getStatusIcon(item.status)}
                        <Text strong>{item.name}</Text>
                        <Text type="secondary">{item.url}</Text>
                      </Space>
                      <Space>
                        <Tag color={getStatusColor(item.status)}>
                          {item.status === 'pending' ? '测试中' : 
                           item.status === 'success' ? '成功' : '失败'}
                        </Tag>
                        {item.duration && (
                          <Text type="secondary">{item.duration}ms</Text>
                        )}
                      </Space>
                    </div>
                    
                    {item.status === 'error' && item.error && (
                      <Alert
                        message="错误信息"
                        description={item.error}
                        type="error"
                        showIcon
                      />
                    )}
                    
                    {item.status === 'success' && item.response && (
                      <details>
                        <summary style={{ cursor: 'pointer', color: '#1890ff' }}>
                          查看响应数据
                        </summary>
                        <pre style={{ 
                          background: '#f5f5f5', 
                          padding: '8px', 
                          borderRadius: '4px',
                          fontSize: '12px',
                          marginTop: '8px',
                          maxHeight: '200px',
                          overflow: 'auto'
                        }}>
                          {JSON.stringify(item.response, null, 2)}
                        </pre>
                      </details>
                    )}
                  </Space>
                </Card>
              </List.Item>
            )}
          />
        </Card>
      )}

      {/* 说明信息 */}
      <Card title="说明" size="small" style={{ marginTop: '16px' }}>
        <Space direction="vertical">
          <Text>• 此页面用于测试前端与API网关的连接状态</Text>
          <Text>• 绿色表示API端点正常工作</Text>
          <Text>• 红色表示API端点存在问题</Text>
          <Text>• 在开发环境中会自动使用开发认证令牌</Text>
          <Text>• 点击"查看响应数据"可以查看API返回的详细信息</Text>
        </Space>
      </Card>
    </div>
  );
};

export default ApiTestPage;
