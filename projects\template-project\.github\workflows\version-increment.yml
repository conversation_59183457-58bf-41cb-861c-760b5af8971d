name: version-increment

on:
  release:
    types: [created]

jobs:
  secrets-gate:
    runs-on: ubuntu-latest
    outputs:
      ok: ${{ steps.check-secrets.outputs.ok }}
    steps:
      - name: check for secrets needed to run workflows
        id: check-secrets
        run: |
          if [ ${{ secrets.PROJECT_VERSION_INCREMENT_ENABLED }} == 'true' ]; then
            echo "ok=enabled" >> $GITHUB_OUTPUT
          fi
  publish-npm:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 22.x
          scope: '@ir-engine'
      - name: Set git username
        run: git config user.name "C<PERSON> Bot"
      - name: Set git email
        run: git config user.email <EMAIL>
      - name: Set pr_branch_name environment variable
        run: echo pr_branch_name=version-increment-${{ github.event.release.tag_name }} >> $GITHUB_ENV
      - name: Switch to branch ${{ env.pr_branch_name }}
        run: git switch -c ${{ env.pr_branch_name }}
      - name: NPM increment version
        run: npm version ${{ github.event.release.tag_name }} --no-git-tag-version
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
      - name: Move root package.json
        run: mv package.json package.json.altered
      - name: Install dependencies for running bump-project-versions
        run: npm install cross-env@7.0.3 ts-node@10.9.2 cli@1.0.1 app-root-path@3.0.0
      - name: Move root package.json back
        run: mv package.json.altered package.json
      - name: Bump default and template project version
        run: node scripts/bump-project-versions.js
      - name: Run git add
        run: git add .
      - name: Commit changes
        run: git commit -m ${{ github.event.release.tag_name }}
      - name: Push changes to new branch
        run: git push --no-verify origin ${{ env.pr_branch_name }}
      - name: Create version bump PR
        uses: repo-sync/pull-request@v2
        with:
          destination_branch: ${{ github.event.release.target_commitish }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          source_branch: ${{ env.pr_branch_name }}
          pr_title: ${{ github.event.release.tag_name }}
          pr_body: Bump version to ${{ github.event.release.tag_name }}