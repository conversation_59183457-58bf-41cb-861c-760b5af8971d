/**
 * 材质面板组件
 */
import React, { useState, useEffect } from 'react';
import {
  List,
  Button,
  Input,
  Select,
  Space,
  Tooltip,
  Tag,
  Modal,
  Form,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CopyOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// import { useAppDispatch } from '../../store'; // 暂时注释掉未使用的导入

const { Option } = Select;
const { Search } = Input;

interface Material {
  id: string;
  name: string;
  type: 'standard' | 'basic' | 'physical' | 'unlit';
  preview?: string;
  properties: {
    baseColor?: [number, number, number, number];
    metallic?: number;
    roughness?: number;
    emissive?: [number, number, number];
  };
}

const MaterialsPanel: React.FC = () => {
  const { t } = useTranslation();
  // const dispatch = useAppDispatch(); // 暂时注释掉未使用的dispatch
  const [materials, setMaterials] = useState<Material[]>([]);
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 模拟材质数据
  useEffect(() => {
    const mockMaterials: Material[] = [
      {
        id: '1',
        name: 'Default Material',
        type: 'standard',
        properties: {
          baseColor: [1, 1, 1, 1],
          metallic: 0,
          roughness: 0.5
        }
      },
      {
        id: '2',
        name: 'Metal Material',
        type: 'physical',
        properties: {
          baseColor: [0.7, 0.7, 0.7, 1],
          metallic: 1,
          roughness: 0.1
        }
      },
      {
        id: '3',
        name: 'Glass Material',
        type: 'physical',
        properties: {
          baseColor: [0.9, 0.9, 1, 0.1],
          metallic: 0,
          roughness: 0
        }
      }
    ];
    setMaterials(mockMaterials);
  }, []);

  // 过滤材质
  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchText.toLowerCase());
    const matchesType = filterType === 'all' || material.type === filterType;
    return matchesSearch && matchesType;
  });

  // 处理材质选择
  const handleSelectMaterial = (material: Material) => {
    setSelectedMaterial(material);
  };

  // 处理创建新材质
  const handleCreateMaterial = () => {
    setSelectedMaterial(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑材质
  const handleEditMaterial = (material: Material) => {
    setSelectedMaterial(material);
    form.setFieldsValue(material);
    setIsModalVisible(true);
  };

  // 处理删除材质
  const handleDeleteMaterial = (materialId: string) => {
    Modal.confirm({
      title: t('editor.materials.confirmDelete'),
      content: t('editor.materials.confirmDeleteMessage'),
      onOk: () => {
        setMaterials(prev => prev.filter(m => m.id !== materialId));
        if (selectedMaterial?.id === materialId) {
          setSelectedMaterial(null);
        }
        message.success(t('editor.materials.deleteSuccess'));
      }
    });
  };

  // 处理复制材质
  const handleCopyMaterial = (material: Material) => {
    const newMaterial: Material = {
      ...material,
      id: Date.now().toString(),
      name: `${material.name} Copy`
    };
    setMaterials(prev => [...prev, newMaterial]);
    message.success(t('editor.materials.copySuccess'));
  };

  // 处理保存材质
  const handleSaveMaterial = (values: any) => {
    if (selectedMaterial) {
      // 编辑现有材质
      setMaterials(prev => prev.map(m => 
        m.id === selectedMaterial.id ? { ...selectedMaterial, ...values } : m
      ));
      message.success(t('editor.materials.updateSuccess'));
    } else {
      // 创建新材质
      const newMaterial: Material = {
        id: Date.now().toString(),
        ...values,
        properties: values.properties || {}
      };
      setMaterials(prev => [...prev, newMaterial]);
      message.success(t('editor.materials.createSuccess'));
    }
    setIsModalVisible(false);
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              size="small"
              onClick={handleCreateMaterial}
            >
              {t('editor.materials.create')}
            </Button>
            <Button 
              icon={<UploadOutlined />} 
              size="small"
            >
              {t('editor.materials.import')}
            </Button>
            <Button 
              icon={<DownloadOutlined />} 
              size="small"
            >
              {t('editor.materials.export')}
            </Button>
          </Space>
          
          <Search
            placeholder={t('editor.materials.searchPlaceholder') as string}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            size="small"
          />
          
          <Select
            value={filterType}
            onChange={setFilterType}
            size="small"
            style={{ width: '100%' }}
          >
            <Option value="all">{t('editor.materials.allTypes')}</Option>
            <Option value="standard">{t('editor.materials.standard')}</Option>
            <Option value="physical">{t('editor.materials.physical')}</Option>
            <Option value="basic">{t('editor.materials.basic')}</Option>
            <Option value="unlit">{t('editor.materials.unlit')}</Option>
          </Select>
        </Space>
      </div>

      {/* 材质列表 */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        <List
          dataSource={filteredMaterials}
          renderItem={(material) => (
            <List.Item
              style={{
                padding: '8px',
                cursor: 'pointer',
                backgroundColor: selectedMaterial?.id === material.id ? '#e6f7ff' : 'transparent'
              }}
              onClick={() => handleSelectMaterial(material)}
              actions={[
                <Tooltip title={t('editor.materials.preview')}>
                  <Button 
                    type="text" 
                    icon={<EyeOutlined />} 
                    size="small"
                  />
                </Tooltip>,
                <Tooltip title={t('editor.materials.edit')}>
                  <Button 
                    type="text" 
                    icon={<EditOutlined />} 
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditMaterial(material);
                    }}
                  />
                </Tooltip>,
                <Tooltip title={t('editor.materials.copy')}>
                  <Button 
                    type="text" 
                    icon={<CopyOutlined />} 
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyMaterial(material);
                    }}
                  />
                </Tooltip>,
                <Tooltip title={t('editor.materials.delete')}>
                  <Button 
                    type="text" 
                    icon={<DeleteOutlined />} 
                    size="small"
                    danger
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteMaterial(material.id);
                    }}
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                title={
                  <Space>
                    {material.name}
                    <Tag color="blue">{material.type}</Tag>
                  </Space>
                }
                description={
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {material.properties.metallic !== undefined && (
                      <span>Metallic: {material.properties.metallic} </span>
                    )}
                    {material.properties.roughness !== undefined && (
                      <span>Roughness: {material.properties.roughness}</span>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </div>

      {/* 材质编辑对话框 */}
      <Modal
        title={selectedMaterial ? t('editor.materials.editTitle') : t('editor.materials.createTitle')}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveMaterial}
        >
          <Form.Item
            name="name"
            label={t('editor.materials.name')}
            rules={[{ required: true, message: t('editor.materials.nameRequired') as string }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="type"
            label={t('editor.materials.type')}
            rules={[{ required: true, message: t('editor.materials.typeRequired') as string }]}
          >
            <Select>
              <Option value="standard">{t('editor.materials.standard')}</Option>
              <Option value="physical">{t('editor.materials.physical')}</Option>
              <Option value="basic">{t('editor.materials.basic')}</Option>
              <Option value="unlit">{t('editor.materials.unlit')}</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MaterialsPanel;
