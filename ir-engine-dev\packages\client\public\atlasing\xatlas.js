/*
CPAL-1.0 License

The contents of this file are subject to the Common Public Attribution License
Version 1.0. (the "License"); you may not use this file except in compliance
with the License. You may obtain a copy of the License at
https://github.com/ir-engine/ir-engine/blob/dev/LICENSE.
The License is based on the Mozilla Public License Version 1.1, but Sections 14
and 15 have been added to cover use of software over a computer network and
provide for limited attribution for the Original Developer. In addition,
Exhibit A has been modified to be consistent with Exhibit B.

Software distributed under the License is distributed on an "AS IS" basis,
WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License for the
specific language governing rights and limitations under the License.

The Original Code is Infinite Reality Engine.

The Original Developer is the Initial Developer. The Initial Developer of the
Original Code is the Infinite Reality Engine team.

All portions of the code written by the Infinite Reality Engine team are Copyright © 2021-2025
Infinite Reality Engine. All Rights Reserved.
*/


/*! For license information please see xatlas.js.LICENSE.txt */
(()=>{var e={954:e=>{var t,r=(t="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0,function(e){var r,n,o=void 0!==(e=e||{})?e:{};o.ready=new Promise((function(e,t){r=e,n=t})),Object.getOwnPropertyDescriptor(o.ready,"_sbrk")||(Object.defineProperty(o.ready,"_sbrk",{configurable:!0,get:function(){ee("You are getting _sbrk on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_sbrk",{configurable:!0,set:function(){ee("You are setting _sbrk on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_stackSave")||(Object.defineProperty(o.ready,"_stackSave",{configurable:!0,get:function(){ee("You are getting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_stackSave",{configurable:!0,set:function(){ee("You are setting _stackSave on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_stackRestore")||(Object.defineProperty(o.ready,"_stackRestore",{configurable:!0,get:function(){ee("You are getting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_stackRestore",{configurable:!0,set:function(){ee("You are setting _stackRestore on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_stackAlloc")||(Object.defineProperty(o.ready,"_stackAlloc",{configurable:!0,get:function(){ee("You are getting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_stackAlloc",{configurable:!0,set:function(){ee("You are setting _stackAlloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"___data_end")||(Object.defineProperty(o.ready,"___data_end",{configurable:!0,get:function(){ee("You are getting ___data_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"___data_end",{configurable:!0,set:function(){ee("You are setting ___data_end on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"___wasm_call_ctors")||(Object.defineProperty(o.ready,"___wasm_call_ctors",{configurable:!0,get:function(){ee("You are getting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"___wasm_call_ctors",{configurable:!0,set:function(){ee("You are setting ___wasm_call_ctors on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_fflush")||(Object.defineProperty(o.ready,"_fflush",{configurable:!0,get:function(){ee("You are getting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_fflush",{configurable:!0,set:function(){ee("You are setting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"___errno_location")||(Object.defineProperty(o.ready,"___errno_location",{configurable:!0,get:function(){ee("You are getting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"___errno_location",{configurable:!0,set:function(){ee("You are setting ___errno_location on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_malloc")||(Object.defineProperty(o.ready,"_malloc",{configurable:!0,get:function(){ee("You are getting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_malloc",{configurable:!0,set:function(){ee("You are setting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_free")||(Object.defineProperty(o.ready,"_free",{configurable:!0,get:function(){ee("You are getting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_free",{configurable:!0,set:function(){ee("You are setting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"_setThrew")||(Object.defineProperty(o.ready,"_setThrew",{configurable:!0,get:function(){ee("You are getting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"_setThrew",{configurable:!0,set:function(){ee("You are setting _setThrew on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(o.ready,"onRuntimeInitialized")||(Object.defineProperty(o.ready,"onRuntimeInitialized",{configurable:!0,get:function(){ee("You are getting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(o.ready,"onRuntimeInitialized",{configurable:!0,set:function(){ee("You are setting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}));var i,a={};for(i in o)o.hasOwnProperty(i)&&(a[i]=o[i]);var s,c,d=[];if(s="object"==typeof window,c="function"==typeof importScripts,"object"==typeof process&&"object"==typeof process.versions&&process.versions.node,o.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var p,u="";if(!s&&!c)throw new Error("environment detection error");if(c?u=self.location.href:document.currentScript&&(u=document.currentScript.src),t&&(u=t),u=0!==u.indexOf("blob:")?u.substr(0,u.lastIndexOf("/")+1):"","object"!=typeof window&&"function"!=typeof importScripts)throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");c&&(p=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)});var l,E,T,O=o.print||console.log.bind(console),_=o.printErr||console.warn.bind(console);for(i in a)a.hasOwnProperty(i)&&(o[i]=a[i]);function f(e){f.shown||(f.shown={}),f.shown[e]||(f.shown[e]=1,_(e))}a=null,o.arguments&&(d=o.arguments),Object.getOwnPropertyDescriptor(o,"arguments")||Object.defineProperty(o,"arguments",{configurable:!0,get:function(){ee("Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),o.thisProgram&&o.thisProgram,Object.getOwnPropertyDescriptor(o,"thisProgram")||Object.defineProperty(o,"thisProgram",{configurable:!0,get:function(){ee("Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),o.quit&&o.quit,Object.getOwnPropertyDescriptor(o,"quit")||Object.defineProperty(o,"quit",{configurable:!0,get:function(){ee("Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),g(void 0===o.memoryInitializerPrefixURL,"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),g(void 0===o.pthreadMainPrefixURL,"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),g(void 0===o.cdInitializerPrefixURL,"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),g(void 0===o.filePackagePrefixURL,"Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),g(void 0===o.read,"Module.read option was removed (modify read_ in JS)"),g(void 0===o.readAsync,"Module.readAsync option was removed (modify readAsync in JS)"),g(void 0===o.readBinary,"Module.readBinary option was removed (modify readBinary in JS)"),g(void 0===o.setWindowTitle,"Module.setWindowTitle option was removed (modify setWindowTitle in JS)"),g(void 0===o.TOTAL_MEMORY,"Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),Object.getOwnPropertyDescriptor(o,"read")||Object.defineProperty(o,"read",{configurable:!0,get:function(){ee("Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(o,"readAsync")||Object.defineProperty(o,"readAsync",{configurable:!0,get:function(){ee("Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(o,"readBinary")||Object.defineProperty(o,"readBinary",{configurable:!0,get:function(){ee("Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(o,"setWindowTitle")||Object.defineProperty(o,"setWindowTitle",{configurable:!0,get:function(){ee("Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),o.wasmBinary&&(l=o.wasmBinary),Object.getOwnPropertyDescriptor(o,"wasmBinary")||Object.defineProperty(o,"wasmBinary",{configurable:!0,get:function(){ee("Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),o.noExitRuntime&&o.noExitRuntime,Object.getOwnPropertyDescriptor(o,"noExitRuntime")||Object.defineProperty(o,"noExitRuntime",{configurable:!0,get:function(){ee("Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),"object"!=typeof WebAssembly&&ee("no native wasm support detected");var h=!1;function g(e,t){e||ee("Assertion failed: "+t)}var R="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function y(e,t,r){for(var n=t+r,o=t;e[o]&&!(o>=n);)++o;if(o-t>16&&e.subarray&&R)return R.decode(e.subarray(t,o));for(var i="";t<o;){var a=e[t++];if(128&a){var s=63&e[t++];if(192!=(224&a)){var c=63&e[t++];if(224==(240&a)?a=(15&a)<<12|s<<6|c:(240!=(248&a)&&f("Invalid UTF-8 leading byte 0x"+a.toString(16)+" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!"),a=(7&a)<<18|s<<12|c<<6|63&e[t++]),a<65536)i+=String.fromCharCode(a);else{var d=a-65536;i+=String.fromCharCode(55296|d>>10,56320|1023&d)}}else i+=String.fromCharCode((31&a)<<6|s)}else i+=String.fromCharCode(a)}return i}function D(e,t){return e?y(I,e,t):""}var w="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0;function P(e,t){g(e%2==0,"Pointer passed to UTF16ToString must be aligned to two bytes!");for(var r=e,n=r>>1,o=n+t/2;!(n>=o)&&x[n];)++n;if((r=n<<1)-e>32&&w)return w.decode(I.subarray(e,r));for(var i=0,a="";;){var s=X[e+2*i>>1];if(0==s||i==t/2)return a;++i,a+=String.fromCharCode(s)}}function A(e,t,r){if(g(t%2==0,"Pointer passed to stringToUTF16 must be aligned to two bytes!"),g("number"==typeof r,"stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),void 0===r&&(r=2147483647),r<2)return 0;for(var n=t,o=(r-=2)<2*e.length?r/2:e.length,i=0;i<o;++i){var a=e.charCodeAt(i);X[t>>1]=a,t+=2}return X[t>>1]=0,t-n}function b(e){return 2*e.length}function m(e,t){g(e%4==0,"Pointer passed to UTF32ToString must be aligned to four bytes!");for(var r=0,n="";!(r>=t/4);){var o=U[e+4*r>>2];if(0==o)break;if(++r,o>=65536){var i=o-65536;n+=String.fromCharCode(55296|i>>10,56320|1023&i)}else n+=String.fromCharCode(o)}return n}function M(e,t,r){if(g(t%4==0,"Pointer passed to stringToUTF32 must be aligned to four bytes!"),g("number"==typeof r,"stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),void 0===r&&(r=2147483647),r<4)return 0;for(var n=t,o=n+r-4,i=0;i<e.length;++i){var a=e.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&e.charCodeAt(++i)),U[t>>2]=a,(t+=4)+4>o)break}return U[t>>2]=0,t-n}function S(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&++r,t+=4}return t}var v,F,I,X,x,U,j,N,H;function C(e){v=e,o.HEAP8=F=new Int8Array(e),o.HEAP16=X=new Int16Array(e),o.HEAP32=U=new Int32Array(e),o.HEAPU8=I=new Uint8Array(e),o.HEAPU16=x=new Uint16Array(e),o.HEAPU32=j=new Uint32Array(e),o.HEAPF32=N=new Float32Array(e),o.HEAPF64=H=new Float64Array(e)}var Q=14112;g(!0,"stack must start aligned");var L=5242880;o.TOTAL_STACK&&g(L===o.TOTAL_STACK,"the stack size can no longer be determined at runtime");var k=o.INITIAL_MEMORY||16777216;function W(){g(!(3&Q)),j[1+(Q>>2)]=34821223,j[2+(Q>>2)]=2310721022,U[0]=1668509029}function $(){if(!h){var e=j[1+(Q>>2)],t=j[2+(Q>>2)];34821223==e&&2310721022==t||ee("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x"+t.toString(16)+" "+e.toString(16)),1668509029!==U[0]&&ee("Runtime error: The application has corrupted its heap memory area (address zero)!")}}Object.getOwnPropertyDescriptor(o,"INITIAL_MEMORY")||Object.defineProperty(o,"INITIAL_MEMORY",{configurable:!0,get:function(){ee("Module.INITIAL_MEMORY has been replaced with plain INITIAL_INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),g(k>=L,"INITIAL_MEMORY should be larger than TOTAL_STACK, was "+k+"! (TOTAL_STACK="+L+")"),g("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),(E=o.wasmMemory?o.wasmMemory:new WebAssembly.Memory({initial:k/65536,maximum:32768}))&&(v=E.buffer),g((k=v.byteLength)%65536==0),g(!0),C(v),function(){var e=new Int16Array(1),t=new Int8Array(e.buffer);if(e[0]=25459,115!==t[0]||99!==t[1])throw"Runtime error: expected the system to be little-endian!"}();var G=[],B=[],z=[],Y=[],V=!1;g(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),g(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),g(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),g(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Z=0,q=null,J=null,K={};function ee(e){o.onAbort&&o.onAbort(e),_(e+=""),h=!0;var t,r,i="abort("+e+") at "+(r=function(){var e=new Error;if(!e.stack){try{throw new Error}catch(t){e=t}if(!e.stack)return"(no stack trace available)"}return e.stack.toString()}(),o.extraStackTrace&&(r+="\n"+o.extraStackTrace()),t=/\b_Z[\w\d_]+/g,r.replace(t,(function(e){var t,r=(t=e,f("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),t);return e===r?e:r+" ["+e+"]"})));e=i;var a=new WebAssembly.RuntimeError(e);throw n(a),a}o.preloadedImages={},o.preloadedAudios={};var te={error:function(){ee("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){te.error()},createDataFile:function(){te.error()},createPreloadedFile:function(){te.error()},createLazyFile:function(){te.error()},open:function(){te.error()},mkdev:function(){te.error()},registerDevice:function(){te.error()},analyzePath:function(){te.error()},loadFilesFromDB:function(){te.error()},ErrnoError:function(){te.error()}};o.FS_createDataFile=te.createDataFile,o.FS_createPreloadedFile=te.createPreloadedFile;function re(e){return t=e,r="data:application/octet-stream;base64,",String.prototype.startsWith?t.startsWith(r):0===t.indexOf(r);var t,r}function ne(e,t){return function(){var r=e,n=t;return t||(n=o.asm),g(V,"native function `"+r+"` called before runtime initialization"),g(!0,"native function `"+r+"` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),n[e]||g(n[e],"exported native function `"+r+"` not found"),n[e].apply(null,arguments)}}var oe,ie="xatlas.wasm";function ae(){try{if(l)return new Uint8Array(l);if(p)return p(ie);throw"both async and sync fetching of the wasm failed"}catch(e){ee(e)}}function se(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var r=t.func;"number"==typeof r?void 0===t.arg?T.get(r)():T.get(r)(t.arg):r(void 0===t.arg?null:t.arg)}else t(o)}}re(ie)||(oe=ie,ie=o.locateFile?o.locateFile(oe,u):u+oe);var ce=0,de=4,pe=8,ue=12,le=13,Ee=16;function Te(e){this.excPtr=e,this.ptr=e-Ee,this.set_type=function(e){U[this.ptr+pe>>2]=e},this.get_type=function(){return U[this.ptr+pe>>2]},this.set_destructor=function(e){U[this.ptr+ce>>2]=e},this.get_destructor=function(){return U[this.ptr+ce>>2]},this.set_refcount=function(e){U[this.ptr+de>>2]=e},this.set_caught=function(e){e=e?1:0,F[this.ptr+ue|0]=e},this.get_caught=function(){return 0!=F[this.ptr+ue|0]},this.set_rethrown=function(e){e=e?1:0,F[this.ptr+le|0]=e},this.get_rethrown=function(){return 0!=F[this.ptr+le|0]},this.init=function(e,t){this.set_type(e),this.set_destructor(t),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var e=U[this.ptr+de>>2];U[this.ptr+de>>2]=e+1},this.release_ref=function(){var e=U[this.ptr+de>>2];return U[this.ptr+de>>2]=e-1,g(e>0),1===e}}function Oe(){return Oe.uncaught_exceptions>0}var _e={};function fe(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function he(e){return this.fromWireType(j[e>>2])}var ge={},Re={},ye={},De=48,we=57;function Pe(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return t>=De&&t<=we?"_"+e:e}function Ae(e,t){return e=Pe(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(t)}function be(e,t){var r=Ae(t,(function(e){this.name=t,this.message=e;var r=new Error(e).stack;void 0!==r&&(this.stack=this.toString()+"\n"+r.replace(/^Error(:[^\n]*)?\n/,""))}));return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var me=void 0;function Me(e){throw new me(e)}function Se(e,t,r){function n(t){var n=r(t);n.length!==e.length&&Me("Mismatched type converter count");for(var o=0;o<e.length;++o)Ue(e[o],n[o])}e.forEach((function(e){ye[e]=t}));var o=new Array(t.length),i=[],a=0;t.forEach((function(e,t){Re.hasOwnProperty(e)?o[t]=Re[e]:(i.push(e),ge.hasOwnProperty(e)||(ge[e]=[]),ge[e].push((function(){o[t]=Re[e],++a===i.length&&n(o)})))})),0===i.length&&n(o)}function ve(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var Fe=void 0;function Ie(e){for(var t="",r=e;I[r];)t+=Fe[I[r++]];return t}var Xe=void 0;function xe(e){throw new Xe(e)}function Ue(e,t,r){if(r=r||{},!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||xe('type "'+n+'" must have a positive integer typeid pointer'),Re.hasOwnProperty(e)){if(r.ignoreDuplicateRegistrations)return;xe("Cannot register type '"+n+"' twice")}if(Re[e]=t,delete ye[e],ge.hasOwnProperty(e)){var o=ge[e];delete ge[e],o.forEach((function(e){e()}))}}function je(e){xe(e.$$.ptrType.registeredClass.name+" instance already deleted")}var Ne=!1;function He(e){}function Ce(e){e.count.value-=1,0===e.count.value&&function(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}(e)}function Qe(e){return"undefined"==typeof FinalizationGroup?(Qe=function(e){return e},e):(Ne=new FinalizationGroup((function(e){for(var t=e.next();!t.done;t=e.next()){var r=t.value;r.ptr?Ce(r):console.warn("object already deleted: "+r.ptr)}})),Qe=function(e){return Ne.register(e,e.$$,e.$$),e},He=function(e){Ne.unregister(e.$$)},Qe(e))}var Le=void 0,ke=[];function We(){for(;ke.length;){var e=ke.pop();e.$$.deleteScheduled=!1,e.delete()}}function $e(){}var Ge={};function Be(e,t,r){if(void 0===e[t].overloadTable){var n=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||xe("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[n.argCount]=n}}function ze(e,t,r){o.hasOwnProperty(e)?((void 0===r||void 0!==o[e].overloadTable&&void 0!==o[e].overloadTable[r])&&xe("Cannot register public name '"+e+"' twice"),Be(o,e,e),o.hasOwnProperty(r)&&xe("Cannot register multiple overloads of a function with the same number of arguments ("+r+")!"),o[e].overloadTable[r]=t):(o[e]=t,void 0!==r&&(o[e].numArguments=r))}function Ye(e,t,r,n,o,i,a,s){this.name=e,this.constructor=t,this.instancePrototype=r,this.rawDestructor=n,this.baseClass=o,this.getActualType=i,this.upcast=a,this.downcast=s,this.pureVirtualFunctions=[]}function Ve(e,t,r){for(;t!==r;)t.upcast||xe("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function Ze(e,t){if(null===t)return this.isReference&&xe("null is not a valid "+this.name),0;t.$$||xe('Cannot pass "'+Tt(t)+'" as a '+this.name),t.$$.ptr||xe("Cannot pass deleted object as a pointer of type "+this.name);var r=t.$$.ptrType.registeredClass;return Ve(t.$$.ptr,r,this.registeredClass)}function qe(e,t){var r;if(null===t)return this.isReference&&xe("null is not a valid "+this.name),this.isSmartPointer?(r=this.rawConstructor(),null!==e&&e.push(this.rawDestructor,r),r):0;t.$$||xe('Cannot pass "'+Tt(t)+'" as a '+this.name),t.$$.ptr||xe("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&xe("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass;if(r=Ve(t.$$.ptr,n,this.registeredClass),this.isSmartPointer)switch(void 0===t.$$.smartPtr&&xe("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?r=t.$$.smartPtr:xe("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:r=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)r=t.$$.smartPtr;else{var o=t.clone();r=this.rawShare(r,Et((function(){o.delete()}))),null!==e&&e.push(this.rawDestructor,r)}break;default:xe("Unsupporting sharing policy")}return r}function Je(e,t){if(null===t)return this.isReference&&xe("null is not a valid "+this.name),0;t.$$||xe('Cannot pass "'+Tt(t)+'" as a '+this.name),t.$$.ptr||xe("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&xe("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var r=t.$$.ptrType.registeredClass;return Ve(t.$$.ptr,r,this.registeredClass)}function Ke(e,t,r){if(t===r)return e;if(void 0===r.baseClass)return null;var n=Ke(e,t,r.baseClass);return null===n?null:r.downcast(n)}var et={};function tt(e,t){return t.ptrType&&t.ptr||Me("makeClassHandle requires ptr and ptrType"),!!t.smartPtrType!=!!t.smartPtr&&Me("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Qe(Object.create(e,{$$:{value:t}}))}function rt(e,t,r,n,o,i,a,s,c,d,p){this.name=e,this.registeredClass=t,this.isReference=r,this.isConst=n,this.isSmartPointer=o,this.pointeeType=i,this.sharingPolicy=a,this.rawGetPointee=s,this.rawConstructor=c,this.rawShare=d,this.rawDestructor=p,o||void 0!==t.baseClass?this.toWireType=qe:n?(this.toWireType=Ze,this.destructorFunction=null):(this.toWireType=Je,this.destructorFunction=null)}function nt(e,t,r){o.hasOwnProperty(e)||Me("Replacing nonexistant public symbol"),void 0!==o[e].overloadTable&&void 0!==r?o[e].overloadTable[r]=t:(o[e]=t,o[e].argCount=r)}function ot(e,t){var r=-1!=(e=Ie(e)).indexOf("j")?function(e,t){g(e.indexOf("j")>=0,"getDynCaller should only be called with i64 sigs");var r=[];return function(){r.length=arguments.length;for(var n=0;n<arguments.length;n++)r[n]=arguments[n];return function(e,t,r){return-1!=e.indexOf("j")?function(e,t,r){return g("dynCall_"+e in o,"bad function pointer type - no table for sig '"+e+"'"),r&&r.length?g(r.length===e.substring(1).replace(/j/g,"--").length):g(1==e.length),r&&r.length?o["dynCall_"+e].apply(null,[t].concat(r)):o["dynCall_"+e].call(null,t)}(e,t,r):T.get(t).apply(null,r)}(e,t,r)}}(e,t):T.get(t);return"function"!=typeof r&&xe("unknown function pointer with signature "+e+": "+t),r}var it=void 0;function at(e){var t=wt(e),r=Ie(t);return Dt(t),r}function st(e,t){var r=[],n={};throw t.forEach((function e(t){n[t]||Re[t]||(ye[t]?ye[t].forEach(e):(r.push(t),n[t]=!0))})),new it(e+": "+r.map(at).join([", "]))}function ct(e,t){for(var r=[],n=0;n<e;n++)r.push(U[(t>>2)+n]);return r}function dt(e,t,r,n,o){var i=t.length;i<2&&xe("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==t[1]&&null!==r,s=!1,c=1;c<t.length;++c)if(null!==t[c]&&void 0===t[c].destructorFunction){s=!0;break}var d="void"!==t[0].name,p="",u="";for(c=0;c<i-2;++c)p+=(0!==c?", ":"")+"arg"+c,u+=(0!==c?", ":"")+"arg"+c+"Wired";var l="return function "+Pe(e)+"("+p+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";s&&(l+="var destructors = [];\n");var E=s?"destructors":"null",T=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],O=[xe,n,o,fe,t[0],t[1]];for(a&&(l+="var thisWired = classParam.toWireType("+E+", this);\n"),c=0;c<i-2;++c)l+="var arg"+c+"Wired = argType"+c+".toWireType("+E+", arg"+c+"); // "+t[c+2].name+"\n",T.push("argType"+c),O.push(t[c+2]);if(a&&(u="thisWired"+(u.length>0?", ":"")+u),l+=(d?"var rv = ":"")+"invoker(fn"+(u.length>0?", ":"")+u+");\n",s)l+="runDestructors(destructors);\n";else for(c=a?1:2;c<t.length;++c){var _=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==t[c].destructorFunction&&(l+=_+"_dtor("+_+"); // "+t[c].name+"\n",T.push(_+"_dtor"),O.push(t[c].destructorFunction))}return d&&(l+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),l+="}\n",T.push(l),function(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var r=Ae(e.name||"unknownFunctionName",(function(){}));r.prototype=e.prototype;var n=new r,o=e.apply(n,t);return o instanceof Object?o:n}(Function,T).apply(null,O)}var pt=[],ut=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function lt(e){e>4&&0==--ut[e].refcount&&(ut[e]=void 0,pt.push(e))}function Et(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=pt.length?pt.pop():ut.length;return ut[t]={refcount:1,value:e},t}}function Tt(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function Ot(e,t){switch(t){case 2:return function(e){return this.fromWireType(N[e>>2])};case 3:return function(e){return this.fromWireType(H[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function _t(e,t,r){switch(t){case 0:return r?function(e){return F[e]}:function(e){return I[e]};case 1:return r?function(e){return X[e>>1]}:function(e){return x[e>>1]};case 2:return r?function(e){return U[e>>2]}:function(e){return j[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function ft(e){try{return E.grow(e-v.byteLength+65535>>>16),C(E.buffer),1}catch(t){console.error("emscripten_realloc_buffer: Attempted to grow heap from "+v.byteLength+" bytes to "+e+" bytes, but got error: "+t)}}var ht={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var r=ht.buffers[e];g(r),0===t||10===t?((1===e?O:_)(y(r,0)),r.length=0):r.push(t)},varargs:void 0,get:function(){return g(null!=ht.varargs),ht.varargs+=4,U[ht.varargs-4>>2]},getStr:function(e){return D(e)},get64:function(e,t){return g(e>=0?0===t:-1===t),e}};me=o.InternalError=be(Error,"InternalError"),function(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Fe=e}(),Xe=o.BindingError=be(Error,"BindingError"),$e.prototype.isAliasOf=function(e){if(!(this instanceof $e))return!1;if(!(e instanceof $e))return!1;for(var t=this.$$.ptrType.registeredClass,r=this.$$.ptr,n=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)r=t.upcast(r),t=t.baseClass;for(;n.baseClass;)o=n.upcast(o),n=n.baseClass;return t===n&&r===o},$e.prototype.clone=function(){if(this.$$.ptr||je(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e,t=Qe(Object.create(Object.getPrototypeOf(this),{$$:{value:(e=this.$$,{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType})}}));return t.$$.count.value+=1,t.$$.deleteScheduled=!1,t},$e.prototype.delete=function(){this.$$.ptr||je(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&xe("Object already scheduled for deletion"),He(this),Ce(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)},$e.prototype.isDeleted=function(){return!this.$$.ptr},$e.prototype.deleteLater=function(){return this.$$.ptr||je(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&xe("Object already scheduled for deletion"),ke.push(this),1===ke.length&&Le&&Le(We),this.$$.deleteScheduled=!0,this},rt.prototype.getPointee=function(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e},rt.prototype.destructor=function(e){this.rawDestructor&&this.rawDestructor(e)},rt.prototype.argPackAdvance=8,rt.prototype.readValueFromPointer=he,rt.prototype.deleteObject=function(e){null!==e&&e.delete()},rt.prototype.fromWireType=function(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var r=function(e,t){return t=function(e,t){for(void 0===t&&xe("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}(e,t),et[t]}(this.registeredClass,t);if(void 0!==r){if(0===r.$$.count.value)return r.$$.ptr=t,r.$$.smartPtr=e,r.clone();var n=r.clone();return this.destructor(e),n}function o(){return this.isSmartPointer?tt(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):tt(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var i,a=this.registeredClass.getActualType(t),s=Ge[a];if(!s)return o.call(this);i=this.isConst?s.constPointerType:s.pointerType;var c=Ke(t,this.registeredClass,i.registeredClass);return null===c?o.call(this):this.isSmartPointer?tt(i.registeredClass.instancePrototype,{ptrType:i,ptr:c,smartPtrType:this,smartPtr:e}):tt(i.registeredClass.instancePrototype,{ptrType:i,ptr:c})},o.getInheritedInstanceCount=function(){return Object.keys(et).length},o.getLiveInheritedInstances=function(){var e=[];for(var t in et)et.hasOwnProperty(t)&&e.push(et[t]);return e},o.flushPendingDeletes=We,o.setDelayFunction=function(e){Le=e,ke.length&&Le&&Le(We)},it=o.UnboundTypeError=be(Error,"UnboundTypeError"),o.count_emval_handles=function(){for(var e=0,t=5;t<ut.length;++t)void 0!==ut[t]&&++e;return e},o.get_first_emval=function(){for(var e=5;e<ut.length;++e)if(void 0!==ut[e])return ut[e];return null},B.push({func:function(){yt()}});var gt,Rt={__cxa_allocate_exception:function(e){return Pt(e+Ee)+Ee},__cxa_throw:function(e,t,r){throw new Te(e).init(t,r),"uncaught_exception"in Oe?Oe.uncaught_exceptions++:Oe.uncaught_exceptions=1,e+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."},_embind_finalize_value_object:function(e){var t=_e[e];delete _e[e];var r=t.rawConstructor,n=t.rawDestructor,o=t.fields;Se([e],o.map((function(e){return e.getterReturnType})).concat(o.map((function(e){return e.setterArgumentType}))),(function(e){var i={};return o.forEach((function(t,r){var n=t.fieldName,a=e[r],s=t.getter,c=t.getterContext,d=e[r+o.length],p=t.setter,u=t.setterContext;i[n]={read:function(e){return a.fromWireType(s(c,e))},write:function(e,t){var r=[];p(u,e,d.toWireType(r,t)),fe(r)}}})),[{name:t.name,fromWireType:function(e){var t={};for(var r in i)t[r]=i[r].read(e);return n(e),t},toWireType:function(e,t){for(var o in i)if(!(o in t))throw new TypeError('Missing field:  "'+o+'"');var a=r();for(o in i)i[o].write(a,t[o]);return null!==e&&e.push(n,a),a},argPackAdvance:8,readValueFromPointer:he,destructorFunction:n}]}))},_embind_register_bool:function(e,t,r,n,o){var i=ve(r);Ue(e,{name:t=Ie(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:o},argPackAdvance:8,readValueFromPointer:function(e){var n;if(1===r)n=F;else if(2===r)n=X;else{if(4!==r)throw new TypeError("Unknown boolean type size: "+t);n=U}return this.fromWireType(n[e>>i])},destructorFunction:null})},_embind_register_class:function(e,t,r,n,o,i,a,s,c,d,p,u,l){p=Ie(p),i=ot(o,i),s&&(s=ot(a,s)),d&&(d=ot(c,d)),l=ot(u,l);var E=Pe(p);ze(E,(function(){st("Cannot construct "+p+" due to unbound types",[n])})),Se([e,t,r],n?[n]:[],(function(t){var r,o;t=t[0],o=n?(r=t.registeredClass).instancePrototype:$e.prototype;var a=Ae(E,(function(){if(Object.getPrototypeOf(this)!==c)throw new Xe("Use 'new' to construct "+p);if(void 0===u.constructor_body)throw new Xe(p+" has no accessible constructor");var e=u.constructor_body[arguments.length];if(void 0===e)throw new Xe("Tried to invoke ctor of "+p+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(u.constructor_body).toString()+") parameters instead!");return e.apply(this,arguments)})),c=Object.create(o,{constructor:{value:a}});a.prototype=c;var u=new Ye(p,a,c,l,r,i,s,d),T=new rt(p,u,!0,!1,!1),O=new rt(p+"*",u,!1,!1,!1),_=new rt(p+" const*",u,!1,!0,!1);return Ge[e]={pointerType:O,constPointerType:_},nt(E,a),[T,O,_]}))},_embind_register_class_constructor:function(e,t,r,n,o,i){g(t>0);var a=ct(t,r);o=ot(n,o);var s=[i],c=[];Se([],[e],(function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.registeredClass.constructor_body&&(e.registeredClass.constructor_body=[]),void 0!==e.registeredClass.constructor_body[t-1])throw new Xe("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.registeredClass.constructor_body[t-1]=function(){st("Cannot construct "+e.name+" due to unbound types",a)},Se([],a,(function(n){return e.registeredClass.constructor_body[t-1]=function(){arguments.length!==t-1&&xe(r+" called with "+arguments.length+" arguments, expected "+(t-1)),c.length=0,s.length=t;for(var e=1;e<t;++e)s[e]=n[e].toWireType(c,arguments[e-1]);var i=o.apply(null,s);return fe(c),n[0].fromWireType(i)},[]})),[]}))},_embind_register_class_function:function(e,t,r,n,o,i,a,s){var c=ct(r,n);t=Ie(t),i=ot(o,i),Se([],[e],(function(e){var n=(e=e[0]).name+"."+t;function o(){st("Cannot call "+n+" due to unbound types",c)}s&&e.registeredClass.pureVirtualFunctions.push(t);var d=e.registeredClass.instancePrototype,p=d[t];return void 0===p||void 0===p.overloadTable&&p.className!==e.name&&p.argCount===r-2?(o.argCount=r-2,o.className=e.name,d[t]=o):(Be(d,t,n),d[t].overloadTable[r-2]=o),Se([],c,(function(o){var s=dt(n,o,e,i,a);return void 0===d[t].overloadTable?(s.argCount=r-2,d[t]=s):d[t].overloadTable[r-2]=s,[]})),[]}))},_embind_register_emval:function(e,t){Ue(e,{name:t=Ie(t),fromWireType:function(e){var t=ut[e].value;return lt(e),t},toWireType:function(e,t){return Et(t)},argPackAdvance:8,readValueFromPointer:he,destructorFunction:null})},_embind_register_float:function(e,t,r){var n=ve(r);Ue(e,{name:t=Ie(t),fromWireType:function(e){return e},toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Tt(t)+'" to '+this.name);return t},argPackAdvance:8,readValueFromPointer:Ot(t,n),destructorFunction:null})},_embind_register_function:function(e,t,r,n,o,i){var a=ct(t,r);e=Ie(e),o=ot(n,o),ze(e,(function(){st("Cannot call "+e+" due to unbound types",a)}),t-1),Se([],a,(function(r){var n=[r[0],null].concat(r.slice(1));return nt(e,dt(e,n,null,o,i),t-1),[]}))},_embind_register_integer:function(e,t,r,n,o){t=Ie(t),-1===o&&(o=4294967295);var i=ve(r),a=function(e){return e};if(0===n){var s=32-8*r;a=function(e){return e<<s>>>s}}var c=-1!=t.indexOf("unsigned");Ue(e,{name:t,fromWireType:a,toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Tt(r)+'" to '+this.name);if(r<n||r>o)throw new TypeError('Passing a number "'+Tt(r)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+n+", "+o+"]!");return c?r>>>0:0|r},argPackAdvance:8,readValueFromPointer:_t(t,i,0!==n),destructorFunction:null})},_embind_register_memory_view:function(e,t,r){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];function o(e){var t=j,r=t[e>>=2],o=t[e+1];return new n(v,o,r)}Ue(e,{name:r=Ie(r),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},_embind_register_std_string:function(e,t){var r="std::string"===(t=Ie(t));Ue(e,{name:t,fromWireType:function(e){var t,n=j[e>>2];if(r)for(var o=e+4,i=0;i<=n;++i){var a=e+4+i;if(i==n||0==I[a]){var s=D(o,a-o);void 0===t?t=s:(t+=String.fromCharCode(0),t+=s),o=a+1}}else{var c=new Array(n);for(i=0;i<n;++i)c[i]=String.fromCharCode(I[e+4+i]);t=c.join("")}return Dt(e),t},toWireType:function(e,t){var n;t instanceof ArrayBuffer&&(t=new Uint8Array(t));var o="string"==typeof t;o||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||xe("Cannot pass non-string to std::string"),n=r&&o?function(){return function(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++r)),n<=127?++t:t+=n<=2047?2:n<=65535?3:4}return t}(t)}:function(){return t.length};var i,a,s,c=n(),d=Pt(4+c+1);if(j[d>>2]=c,r&&o)i=t,a=d+4,g("number"==typeof(s=c+1),"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),function(e,t,r,n){if(!(n>0))return 0;for(var o=r,i=r+n-1,a=0;a<e.length;++a){var s=e.charCodeAt(a);if(s>=55296&&s<=57343&&(s=65536+((1023&s)<<10)|1023&e.charCodeAt(++a)),s<=127){if(r>=i)break;t[r++]=s}else if(s<=2047){if(r+1>=i)break;t[r++]=192|s>>6,t[r++]=128|63&s}else if(s<=65535){if(r+2>=i)break;t[r++]=224|s>>12,t[r++]=128|s>>6&63,t[r++]=128|63&s}else{if(r+3>=i)break;s>=2097152&&f("Invalid Unicode code point 0x"+s.toString(16)+" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF)."),t[r++]=240|s>>18,t[r++]=128|s>>12&63,t[r++]=128|s>>6&63,t[r++]=128|63&s}}t[r]=0}(i,I,a,s);else if(o)for(var p=0;p<c;++p){var u=t.charCodeAt(p);u>255&&(Dt(d),xe("String has UTF-16 code units that do not fit in 8 bits")),I[d+4+p]=u}else for(p=0;p<c;++p)I[d+4+p]=t[p];return null!==e&&e.push(Dt,d),d},argPackAdvance:8,readValueFromPointer:he,destructorFunction:function(e){Dt(e)}})},_embind_register_std_wstring:function(e,t,r){var n,o,i,a,s;r=Ie(r),2===t?(n=P,o=A,a=b,i=function(){return x},s=1):4===t&&(n=m,o=M,a=S,i=function(){return j},s=2),Ue(e,{name:r,fromWireType:function(e){for(var r,o=j[e>>2],a=i(),c=e+4,d=0;d<=o;++d){var p=e+4+d*t;if(d==o||0==a[p>>s]){var u=n(c,p-c);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),c=p+t}}return Dt(e),r},toWireType:function(e,n){"string"!=typeof n&&xe("Cannot pass non-string to C++ string type "+r);var i=a(n),c=Pt(4+i+t);return j[c>>2]=i>>s,o(n,c+4,i+t),null!==e&&e.push(Dt,c),c},argPackAdvance:8,readValueFromPointer:he,destructorFunction:function(e){Dt(e)}})},_embind_register_value_object:function(e,t,r,n,o,i){_e[e]={name:Ie(t),rawConstructor:ot(r,n),rawDestructor:ot(o,i),fields:[]}},_embind_register_value_object_field:function(e,t,r,n,o,i,a,s,c,d){_e[e].fields.push({fieldName:Ie(t),getterReturnType:r,getter:ot(n,o),getterContext:i,setterArgumentType:a,setter:ot(s,c),setterContext:d})},_embind_register_void:function(e,t){Ue(e,{isVoid:!0,name:t=Ie(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,t){}})},_emval_decref:lt,_emval_incref:function(e){e>4&&(ut[e].refcount+=1)},_emval_take_value:function(e,t){var r,n;return void 0===(n=Re[r=e])&&xe("_emval_take_value has unknown type "+at(r)),Et((e=n).readValueFromPointer(t))},abort:function(){ee()},emscripten_memcpy_big:function(e,t,r){I.copyWithin(e,t,t+r)},emscripten_resize_heap:function(e){e>>>=0;var t=I.length;g(e>t);var r=2147483648;if(e>r)return _("Cannot enlarge memory, asked to go up to "+e+" bytes, but the limit is "+r+" bytes!"),!1;for(var n,o=1;o<=4;o*=2){var i=t*(1+.2/o);i=Math.min(i,e+100663296);var a=Math.min(r,((n=Math.max(16777216,e,i))%65536>0&&(n+=65536-n%65536),n));if(ft(a))return!0}return _("Failed to grow the heap from "+t+" bytes to "+a+" bytes, not enough memory!"),!1},fd_write:function(e,t,r,n){for(var o=0,i=0;i<r;i++){for(var a=U[t+8*i>>2],s=U[t+(8*i+4)>>2],c=0;c<s;c++)ht.printChar(e,I[a+c]);o+=s}return U[n>>2]=o,0},memory:E,onAtlasProgress:function(e,t){o.onAtlasProgress&&o.onAtlasProgress(e,t)},setTempRet0:function(e){}},yt=(function(){var e,t={env:Rt,wasi_snapshot_preview1:Rt};function r(e,t){var r=e.exports;o.asm=r,g(T=o.asm.__indirect_function_table,"table not found in wasm exports"),function(e){if(Z--,o.monitorRunDependencies&&o.monitorRunDependencies(Z),e?(g(K[e]),delete K[e]):_("warning: run dependency removed without ID"),0==Z&&(null!==q&&(clearInterval(q),q=null),J)){var t=J;J=null,t()}}("wasm-instantiate")}e="wasm-instantiate",Z++,o.monitorRunDependencies&&o.monitorRunDependencies(Z),e?(g(!K[e]),K[e]=1,null===q&&"undefined"!=typeof setInterval&&(q=setInterval((function(){if(h)return clearInterval(q),void(q=null);var e=!1;for(var t in K)e||(e=!0,_("still waiting on run dependencies:")),_("dependency: "+t);e&&_("(end of list)")}),1e4))):_("warning: run dependency added without ID");var n=o;function i(e){g(o===n,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),n=null,r(e.instance)}function a(e){return(l||!s&&!c||"function"!=typeof fetch?Promise.resolve().then(ae):fetch(ie,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+ie+"'";return e.arrayBuffer()})).catch((function(){return ae()}))).then((function(e){return WebAssembly.instantiate(e,t)})).then(e,(function(e){_("failed to asynchronously prepare wasm: "+e),ee(e)}))}if(o.instantiateWasm)try{return o.instantiateWasm(t,r)}catch(e){return _("Module.instantiateWasm callback failed with error: "+e),!1}!function(){if(l||"function"!=typeof WebAssembly.instantiateStreaming||re(ie)||"function"!=typeof fetch)return a(i);fetch(ie,{credentials:"same-origin"}).then((function(e){return WebAssembly.instantiateStreaming(e,t).then(i,(function(e){return _("wasm streaming compile failed: "+e),_("falling back to ArrayBuffer instantiation"),a(i)}))}))}()}(),o.___wasm_call_ctors=ne("__wasm_call_ctors")),Dt=o._free=ne("free"),wt=o.___getTypeName=ne("__getTypeName"),Pt=(o.___embind_register_native_and_builtin_types=ne("__embind_register_native_and_builtin_types"),o.___errno_location=ne("__errno_location"),o._malloc=ne("malloc"));function At(e){function t(){gt||(gt=!0,o.calledRun=!0,h||($(),g(!V),V=!0,se(B),$(),se(z),r(o),o.onRuntimeInitialized&&o.onRuntimeInitialized(),g(!o._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),function(){if($(),o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)e=o.postRun.shift(),Y.unshift(e);var e;se(Y)}()))}e=e||d,Z>0||(W(),function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)e=o.preRun.shift(),G.unshift(e);var e;se(G)}(),Z>0||(o.setStatus?(o.setStatus("Running..."),setTimeout((function(){setTimeout((function(){o.setStatus("")}),1),t()}),1)):t(),$()))}if(o._fflush=ne("fflush"),o.stackSave=ne("stackSave"),o.stackRestore=ne("stackRestore"),o.stackAlloc=ne("stackAlloc"),o._setThrew=ne("setThrew"),o._sbrk=ne("sbrk"),o.dynCall_jiji=ne("dynCall_jiji"),Object.getOwnPropertyDescriptor(o,"intArrayFromString")||(o.intArrayFromString=function(){ee("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"intArrayToString")||(o.intArrayToString=function(){ee("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ccall")||(o.ccall=function(){ee("'ccall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"cwrap")||(o.cwrap=function(){ee("'cwrap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"setValue")||(o.setValue=function(){ee("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getValue")||(o.getValue=function(){ee("'getValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"allocate")||(o.allocate=function(){ee("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"UTF8ArrayToString")||(o.UTF8ArrayToString=function(){ee("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"UTF8ToString")||(o.UTF8ToString=function(){ee("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stringToUTF8Array")||(o.stringToUTF8Array=function(){ee("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stringToUTF8")||(o.stringToUTF8=function(){ee("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"lengthBytesUTF8")||(o.lengthBytesUTF8=function(){ee("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stackTrace")||(o.stackTrace=function(){ee("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addOnPreRun")||(o.addOnPreRun=function(){ee("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addOnInit")||(o.addOnInit=function(){ee("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addOnPreMain")||(o.addOnPreMain=function(){ee("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addOnExit")||(o.addOnExit=function(){ee("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addOnPostRun")||(o.addOnPostRun=function(){ee("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeStringToMemory")||(o.writeStringToMemory=function(){ee("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeArrayToMemory")||(o.writeArrayToMemory=function(){ee("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeAsciiToMemory")||(o.writeAsciiToMemory=function(){ee("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addRunDependency")||(o.addRunDependency=function(){ee("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"removeRunDependency")||(o.removeRunDependency=function(){ee("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"FS_createFolder")||(o.FS_createFolder=function(){ee("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"FS_createPath")||(o.FS_createPath=function(){ee("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"FS_createDataFile")||(o.FS_createDataFile=function(){ee("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"FS_createPreloadedFile")||(o.FS_createPreloadedFile=function(){ee("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"FS_createLazyFile")||(o.FS_createLazyFile=function(){ee("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"FS_createLink")||(o.FS_createLink=function(){ee("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"FS_createDevice")||(o.FS_createDevice=function(){ee("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"FS_unlink")||(o.FS_unlink=function(){ee("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(o,"getLEB")||(o.getLEB=function(){ee("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getFunctionTables")||(o.getFunctionTables=function(){ee("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"alignFunctionTables")||(o.alignFunctionTables=function(){ee("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"registerFunctions")||(o.registerFunctions=function(){ee("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"addFunction")||(o.addFunction=function(){ee("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"removeFunction")||(o.removeFunction=function(){ee("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getFuncWrapper")||(o.getFuncWrapper=function(){ee("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"prettyPrint")||(o.prettyPrint=function(){ee("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"makeBigInt")||(o.makeBigInt=function(){ee("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"dynCall")||(o.dynCall=function(){ee("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getCompilerSetting")||(o.getCompilerSetting=function(){ee("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"print")||(o.print=function(){ee("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"printErr")||(o.printErr=function(){ee("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getTempRet0")||(o.getTempRet0=function(){ee("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"setTempRet0")||(o.setTempRet0=function(){ee("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"callMain")||(o.callMain=function(){ee("'callMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"abort")||(o.abort=function(){ee("'abort' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stringToNewUTF8")||(o.stringToNewUTF8=function(){ee("'stringToNewUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emscripten_realloc_buffer")||(o.emscripten_realloc_buffer=function(){ee("'emscripten_realloc_buffer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ENV")||(o.ENV=function(){ee("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ERRNO_CODES")||(o.ERRNO_CODES=function(){ee("'ERRNO_CODES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ERRNO_MESSAGES")||(o.ERRNO_MESSAGES=function(){ee("'ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"setErrNo")||(o.setErrNo=function(){ee("'setErrNo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"DNS")||(o.DNS=function(){ee("'DNS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getHostByName")||(o.getHostByName=function(){ee("'getHostByName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"GAI_ERRNO_MESSAGES")||(o.GAI_ERRNO_MESSAGES=function(){ee("'GAI_ERRNO_MESSAGES' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"Protocols")||(o.Protocols=function(){ee("'Protocols' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"Sockets")||(o.Sockets=function(){ee("'Sockets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getRandomDevice")||(o.getRandomDevice=function(){ee("'getRandomDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"traverseStack")||(o.traverseStack=function(){ee("'traverseStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"UNWIND_CACHE")||(o.UNWIND_CACHE=function(){ee("'UNWIND_CACHE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"withBuiltinMalloc")||(o.withBuiltinMalloc=function(){ee("'withBuiltinMalloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"readAsmConstArgsArray")||(o.readAsmConstArgsArray=function(){ee("'readAsmConstArgsArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"readAsmConstArgs")||(o.readAsmConstArgs=function(){ee("'readAsmConstArgs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"mainThreadEM_ASM")||(o.mainThreadEM_ASM=function(){ee("'mainThreadEM_ASM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"jstoi_q")||(o.jstoi_q=function(){ee("'jstoi_q' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"jstoi_s")||(o.jstoi_s=function(){ee("'jstoi_s' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getExecutableName")||(o.getExecutableName=function(){ee("'getExecutableName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"listenOnce")||(o.listenOnce=function(){ee("'listenOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"autoResumeAudioContext")||(o.autoResumeAudioContext=function(){ee("'autoResumeAudioContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"dynCallLegacy")||(o.dynCallLegacy=function(){ee("'dynCallLegacy' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getDynCaller")||(o.getDynCaller=function(){ee("'getDynCaller' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"dynCall")||(o.dynCall=function(){ee("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"callRuntimeCallbacks")||(o.callRuntimeCallbacks=function(){ee("'callRuntimeCallbacks' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"abortStackOverflow")||(o.abortStackOverflow=function(){ee("'abortStackOverflow' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"reallyNegative")||(o.reallyNegative=function(){ee("'reallyNegative' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"unSign")||(o.unSign=function(){ee("'unSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"reSign")||(o.reSign=function(){ee("'reSign' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"formatString")||(o.formatString=function(){ee("'formatString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"PATH")||(o.PATH=function(){ee("'PATH' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"PATH_FS")||(o.PATH_FS=function(){ee("'PATH_FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SYSCALLS")||(o.SYSCALLS=function(){ee("'SYSCALLS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"syscallMmap2")||(o.syscallMmap2=function(){ee("'syscallMmap2' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"syscallMunmap")||(o.syscallMunmap=function(){ee("'syscallMunmap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"flush_NO_FILESYSTEM")||(o.flush_NO_FILESYSTEM=function(){ee("'flush_NO_FILESYSTEM' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"JSEvents")||(o.JSEvents=function(){ee("'JSEvents' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"specialHTMLTargets")||(o.specialHTMLTargets=function(){ee("'specialHTMLTargets' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"maybeCStringToJsString")||(o.maybeCStringToJsString=function(){ee("'maybeCStringToJsString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"findEventTarget")||(o.findEventTarget=function(){ee("'findEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"findCanvasEventTarget")||(o.findCanvasEventTarget=function(){ee("'findCanvasEventTarget' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"polyfillSetImmediate")||(o.polyfillSetImmediate=function(){ee("'polyfillSetImmediate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"demangle")||(o.demangle=function(){ee("'demangle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"demangleAll")||(o.demangleAll=function(){ee("'demangleAll' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"jsStackTrace")||(o.jsStackTrace=function(){ee("'jsStackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stackTrace")||(o.stackTrace=function(){ee("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getEnvStrings")||(o.getEnvStrings=function(){ee("'getEnvStrings' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"checkWasiClock")||(o.checkWasiClock=function(){ee("'checkWasiClock' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeI53ToI64")||(o.writeI53ToI64=function(){ee("'writeI53ToI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeI53ToI64Clamped")||(o.writeI53ToI64Clamped=function(){ee("'writeI53ToI64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeI53ToI64Signaling")||(o.writeI53ToI64Signaling=function(){ee("'writeI53ToI64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeI53ToU64Clamped")||(o.writeI53ToU64Clamped=function(){ee("'writeI53ToU64Clamped' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeI53ToU64Signaling")||(o.writeI53ToU64Signaling=function(){ee("'writeI53ToU64Signaling' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"readI53FromI64")||(o.readI53FromI64=function(){ee("'readI53FromI64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"readI53FromU64")||(o.readI53FromU64=function(){ee("'readI53FromU64' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"convertI32PairToI53")||(o.convertI32PairToI53=function(){ee("'convertI32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"convertU32PairToI53")||(o.convertU32PairToI53=function(){ee("'convertU32PairToI53' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"exceptionLast")||(o.exceptionLast=function(){ee("'exceptionLast' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"exceptionCaught")||(o.exceptionCaught=function(){ee("'exceptionCaught' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ExceptionInfoAttrs")||(o.ExceptionInfoAttrs=function(){ee("'ExceptionInfoAttrs' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ExceptionInfo")||(o.ExceptionInfo=function(){ee("'ExceptionInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"CatchInfo")||(o.CatchInfo=function(){ee("'CatchInfo' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"exception_addRef")||(o.exception_addRef=function(){ee("'exception_addRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"exception_decRef")||(o.exception_decRef=function(){ee("'exception_decRef' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"Browser")||(o.Browser=function(){ee("'Browser' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"funcWrappers")||(o.funcWrappers=function(){ee("'funcWrappers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getFuncWrapper")||(o.getFuncWrapper=function(){ee("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"setMainLoop")||(o.setMainLoop=function(){ee("'setMainLoop' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"FS")||(o.FS=function(){ee("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"mmapAlloc")||(o.mmapAlloc=function(){ee("'mmapAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"MEMFS")||(o.MEMFS=function(){ee("'MEMFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"TTY")||(o.TTY=function(){ee("'TTY' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"PIPEFS")||(o.PIPEFS=function(){ee("'PIPEFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SOCKFS")||(o.SOCKFS=function(){ee("'SOCKFS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"tempFixedLengthArray")||(o.tempFixedLengthArray=function(){ee("'tempFixedLengthArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"miniTempWebGLFloatBuffers")||(o.miniTempWebGLFloatBuffers=function(){ee("'miniTempWebGLFloatBuffers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"heapObjectForWebGLType")||(o.heapObjectForWebGLType=function(){ee("'heapObjectForWebGLType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"heapAccessShiftForWebGLHeap")||(o.heapAccessShiftForWebGLHeap=function(){ee("'heapAccessShiftForWebGLHeap' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"GL")||(o.GL=function(){ee("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emscriptenWebGLGet")||(o.emscriptenWebGLGet=function(){ee("'emscriptenWebGLGet' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"computeUnpackAlignedImageSize")||(o.computeUnpackAlignedImageSize=function(){ee("'computeUnpackAlignedImageSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emscriptenWebGLGetTexPixelData")||(o.emscriptenWebGLGetTexPixelData=function(){ee("'emscriptenWebGLGetTexPixelData' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emscriptenWebGLGetUniform")||(o.emscriptenWebGLGetUniform=function(){ee("'emscriptenWebGLGetUniform' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emscriptenWebGLGetVertexAttrib")||(o.emscriptenWebGLGetVertexAttrib=function(){ee("'emscriptenWebGLGetVertexAttrib' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"writeGLArray")||(o.writeGLArray=function(){ee("'writeGLArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"AL")||(o.AL=function(){ee("'AL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SDL_unicode")||(o.SDL_unicode=function(){ee("'SDL_unicode' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SDL_ttfContext")||(o.SDL_ttfContext=function(){ee("'SDL_ttfContext' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SDL_audio")||(o.SDL_audio=function(){ee("'SDL_audio' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SDL")||(o.SDL=function(){ee("'SDL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"SDL_gfx")||(o.SDL_gfx=function(){ee("'SDL_gfx' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"GLUT")||(o.GLUT=function(){ee("'GLUT' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"EGL")||(o.EGL=function(){ee("'EGL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"GLFW_Window")||(o.GLFW_Window=function(){ee("'GLFW_Window' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"GLFW")||(o.GLFW=function(){ee("'GLFW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"GLEW")||(o.GLEW=function(){ee("'GLEW' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"IDBStore")||(o.IDBStore=function(){ee("'IDBStore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"runAndAbortIfError")||(o.runAndAbortIfError=function(){ee("'runAndAbortIfError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emval_handle_array")||(o.emval_handle_array=function(){ee("'emval_handle_array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emval_free_list")||(o.emval_free_list=function(){ee("'emval_free_list' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emval_symbols")||(o.emval_symbols=function(){ee("'emval_symbols' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"init_emval")||(o.init_emval=function(){ee("'init_emval' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"count_emval_handles")||(o.count_emval_handles=function(){ee("'count_emval_handles' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"get_first_emval")||(o.get_first_emval=function(){ee("'get_first_emval' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getStringOrSymbol")||(o.getStringOrSymbol=function(){ee("'getStringOrSymbol' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"requireHandle")||(o.requireHandle=function(){ee("'requireHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emval_newers")||(o.emval_newers=function(){ee("'emval_newers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"craftEmvalAllocator")||(o.craftEmvalAllocator=function(){ee("'craftEmvalAllocator' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emval_get_global")||(o.emval_get_global=function(){ee("'emval_get_global' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"emval_methodCallers")||(o.emval_methodCallers=function(){ee("'emval_methodCallers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"InternalError")||(o.InternalError=function(){ee("'InternalError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"BindingError")||(o.BindingError=function(){ee("'BindingError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"UnboundTypeError")||(o.UnboundTypeError=function(){ee("'UnboundTypeError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"PureVirtualError")||(o.PureVirtualError=function(){ee("'PureVirtualError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"init_embind")||(o.init_embind=function(){ee("'init_embind' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"throwInternalError")||(o.throwInternalError=function(){ee("'throwInternalError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"throwBindingError")||(o.throwBindingError=function(){ee("'throwBindingError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"throwUnboundTypeError")||(o.throwUnboundTypeError=function(){ee("'throwUnboundTypeError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ensureOverloadTable")||(o.ensureOverloadTable=function(){ee("'ensureOverloadTable' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"exposePublicSymbol")||(o.exposePublicSymbol=function(){ee("'exposePublicSymbol' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"replacePublicSymbol")||(o.replacePublicSymbol=function(){ee("'replacePublicSymbol' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"extendError")||(o.extendError=function(){ee("'extendError' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"createNamedFunction")||(o.createNamedFunction=function(){ee("'createNamedFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"registeredInstances")||(o.registeredInstances=function(){ee("'registeredInstances' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getBasestPointer")||(o.getBasestPointer=function(){ee("'getBasestPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"registerInheritedInstance")||(o.registerInheritedInstance=function(){ee("'registerInheritedInstance' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"unregisterInheritedInstance")||(o.unregisterInheritedInstance=function(){ee("'unregisterInheritedInstance' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getInheritedInstance")||(o.getInheritedInstance=function(){ee("'getInheritedInstance' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getInheritedInstanceCount")||(o.getInheritedInstanceCount=function(){ee("'getInheritedInstanceCount' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getLiveInheritedInstances")||(o.getLiveInheritedInstances=function(){ee("'getLiveInheritedInstances' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"registeredTypes")||(o.registeredTypes=function(){ee("'registeredTypes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"awaitingDependencies")||(o.awaitingDependencies=function(){ee("'awaitingDependencies' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"typeDependencies")||(o.typeDependencies=function(){ee("'typeDependencies' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"registeredPointers")||(o.registeredPointers=function(){ee("'registeredPointers' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"registerType")||(o.registerType=function(){ee("'registerType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"whenDependentTypesAreResolved")||(o.whenDependentTypesAreResolved=function(){ee("'whenDependentTypesAreResolved' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"embind_charCodes")||(o.embind_charCodes=function(){ee("'embind_charCodes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"embind_init_charCodes")||(o.embind_init_charCodes=function(){ee("'embind_init_charCodes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"readLatin1String")||(o.readLatin1String=function(){ee("'readLatin1String' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getTypeName")||(o.getTypeName=function(){ee("'getTypeName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"heap32VectorToArray")||(o.heap32VectorToArray=function(){ee("'heap32VectorToArray' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"requireRegisteredType")||(o.requireRegisteredType=function(){ee("'requireRegisteredType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"getShiftFromSize")||(o.getShiftFromSize=function(){ee("'getShiftFromSize' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"integerReadValueFromPointer")||(o.integerReadValueFromPointer=function(){ee("'integerReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"enumReadValueFromPointer")||(o.enumReadValueFromPointer=function(){ee("'enumReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"floatReadValueFromPointer")||(o.floatReadValueFromPointer=function(){ee("'floatReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"simpleReadValueFromPointer")||(o.simpleReadValueFromPointer=function(){ee("'simpleReadValueFromPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"runDestructors")||(o.runDestructors=function(){ee("'runDestructors' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"new_")||(o.new_=function(){ee("'new_' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"craftInvokerFunction")||(o.craftInvokerFunction=function(){ee("'craftInvokerFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"embind__requireFunction")||(o.embind__requireFunction=function(){ee("'embind__requireFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"tupleRegistrations")||(o.tupleRegistrations=function(){ee("'tupleRegistrations' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"structRegistrations")||(o.structRegistrations=function(){ee("'structRegistrations' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"genericPointerToWireType")||(o.genericPointerToWireType=function(){ee("'genericPointerToWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"constNoSmartPtrRawPointerToWireType")||(o.constNoSmartPtrRawPointerToWireType=function(){ee("'constNoSmartPtrRawPointerToWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"nonConstNoSmartPtrRawPointerToWireType")||(o.nonConstNoSmartPtrRawPointerToWireType=function(){ee("'nonConstNoSmartPtrRawPointerToWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"init_RegisteredPointer")||(o.init_RegisteredPointer=function(){ee("'init_RegisteredPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"RegisteredPointer")||(o.RegisteredPointer=function(){ee("'RegisteredPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"RegisteredPointer_getPointee")||(o.RegisteredPointer_getPointee=function(){ee("'RegisteredPointer_getPointee' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"RegisteredPointer_destructor")||(o.RegisteredPointer_destructor=function(){ee("'RegisteredPointer_destructor' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"RegisteredPointer_deleteObject")||(o.RegisteredPointer_deleteObject=function(){ee("'RegisteredPointer_deleteObject' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"RegisteredPointer_fromWireType")||(o.RegisteredPointer_fromWireType=function(){ee("'RegisteredPointer_fromWireType' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"runDestructor")||(o.runDestructor=function(){ee("'runDestructor' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"releaseClassHandle")||(o.releaseClassHandle=function(){ee("'releaseClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"finalizationGroup")||(o.finalizationGroup=function(){ee("'finalizationGroup' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"detachFinalizer_deps")||(o.detachFinalizer_deps=function(){ee("'detachFinalizer_deps' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"detachFinalizer")||(o.detachFinalizer=function(){ee("'detachFinalizer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"attachFinalizer")||(o.attachFinalizer=function(){ee("'attachFinalizer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"makeClassHandle")||(o.makeClassHandle=function(){ee("'makeClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"init_ClassHandle")||(o.init_ClassHandle=function(){ee("'init_ClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ClassHandle")||(o.ClassHandle=function(){ee("'ClassHandle' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ClassHandle_isAliasOf")||(o.ClassHandle_isAliasOf=function(){ee("'ClassHandle_isAliasOf' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"throwInstanceAlreadyDeleted")||(o.throwInstanceAlreadyDeleted=function(){ee("'throwInstanceAlreadyDeleted' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ClassHandle_clone")||(o.ClassHandle_clone=function(){ee("'ClassHandle_clone' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ClassHandle_delete")||(o.ClassHandle_delete=function(){ee("'ClassHandle_delete' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"deletionQueue")||(o.deletionQueue=function(){ee("'deletionQueue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ClassHandle_isDeleted")||(o.ClassHandle_isDeleted=function(){ee("'ClassHandle_isDeleted' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"ClassHandle_deleteLater")||(o.ClassHandle_deleteLater=function(){ee("'ClassHandle_deleteLater' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"flushPendingDeletes")||(o.flushPendingDeletes=function(){ee("'flushPendingDeletes' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"delayFunction")||(o.delayFunction=function(){ee("'delayFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"setDelayFunction")||(o.setDelayFunction=function(){ee("'setDelayFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"RegisteredClass")||(o.RegisteredClass=function(){ee("'RegisteredClass' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"shallowCopyInternalPointer")||(o.shallowCopyInternalPointer=function(){ee("'shallowCopyInternalPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"downcastPointer")||(o.downcastPointer=function(){ee("'downcastPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"upcastPointer")||(o.upcastPointer=function(){ee("'upcastPointer' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"validateThis")||(o.validateThis=function(){ee("'validateThis' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"char_0")||(o.char_0=function(){ee("'char_0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"char_9")||(o.char_9=function(){ee("'char_9' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"makeLegalFunctionName")||(o.makeLegalFunctionName=function(){ee("'makeLegalFunctionName' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"warnOnce")||(o.warnOnce=function(){ee("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stackSave")||(o.stackSave=function(){ee("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stackRestore")||(o.stackRestore=function(){ee("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stackAlloc")||(o.stackAlloc=function(){ee("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"AsciiToString")||(o.AsciiToString=function(){ee("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stringToAscii")||(o.stringToAscii=function(){ee("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"UTF16ToString")||(o.UTF16ToString=function(){ee("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stringToUTF16")||(o.stringToUTF16=function(){ee("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"lengthBytesUTF16")||(o.lengthBytesUTF16=function(){ee("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"UTF32ToString")||(o.UTF32ToString=function(){ee("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"stringToUTF32")||(o.stringToUTF32=function(){ee("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"lengthBytesUTF32")||(o.lengthBytesUTF32=function(){ee("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"allocateUTF8")||(o.allocateUTF8=function(){ee("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(o,"allocateUTF8OnStack")||(o.allocateUTF8OnStack=function(){ee("'allocateUTF8OnStack' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),o.writeStackCookie=W,o.checkStackCookie=$,Object.getOwnPropertyDescriptor(o,"ALLOC_NORMAL")||Object.defineProperty(o,"ALLOC_NORMAL",{configurable:!0,get:function(){ee("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),Object.getOwnPropertyDescriptor(o,"ALLOC_STACK")||Object.defineProperty(o,"ALLOC_STACK",{configurable:!0,get:function(){ee("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),J=function e(){gt||At(),gt||(J=e)},o.run=At,o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();return At(),e.ready});e.exports=r}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";const e=Symbol("Comlink.proxy"),t=Symbol("Comlink.endpoint"),n=Symbol("Comlink.releaseProxy"),o=Symbol("Comlink.finalizer"),i=Symbol("Comlink.thrown"),a=e=>"object"==typeof e&&null!==e||"function"==typeof e,s=new Map([["proxy",{canHandle:t=>a(t)&&t[e],serialize(e){const{port1:t,port2:r}=new MessageChannel;return c(e,t),[r,[r]]},deserialize:e=>(e.start(),T(e,[],undefined))}],["throw",{canHandle:e=>a(e)&&i in e,serialize({value:e}){let t;return t=e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[t,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error(e.value.message),e.value);throw e.value}}]]);function c(t,r=globalThis,n=["*"]){r.addEventListener("message",(function a(s){if(!s||!s.data)return;if(!function(e,t){for(const r of e){if(t===r||"*"===r)return!0;if(r instanceof RegExp&&r.test(t))return!0}return!1}(n,s.origin))return void console.warn(`Invalid origin '${s.origin}' for comlink proxy`);const{id:p,type:u,path:l}=Object.assign({path:[]},s.data),E=(s.data.argumentList||[]).map(h);let T;try{const r=l.slice(0,-1).reduce(((e,t)=>e[t]),t),n=l.reduce(((e,t)=>e[t]),t);switch(u){case"GET":T=n;break;case"SET":r[l.slice(-1)[0]]=h(s.data.value),T=!0;break;case"APPLY":T=n.apply(r,E);break;case"CONSTRUCT":T=function(t){return Object.assign(t,{[e]:!0})}(new n(...E));break;case"ENDPOINT":{const{port1:e,port2:r}=new MessageChannel;c(t,r),T=function(e,t){return _.set(e,t),e}(e,[e])}break;case"RELEASE":T=void 0;break;default:return}}catch(e){T={value:e,[i]:0}}Promise.resolve(T).catch((e=>({value:e,[i]:0}))).then((e=>{const[n,i]=f(e);r.postMessage(Object.assign(Object.assign({},n),{id:p}),i),"RELEASE"===u&&(r.removeEventListener("message",a),d(r),o in t&&"function"==typeof t[o]&&t[o]())})).catch((e=>{const[t,n]=f({value:new TypeError("Unserializable return value"),[i]:0});r.postMessage(Object.assign(Object.assign({},t),{id:p}),n)}))})),r.start&&r.start()}function d(e){(function(e){return"MessagePort"===e.constructor.name})(e)&&e.close()}function p(e){if(e)throw new Error("Proxy has been released and is not useable")}function u(e){return g(e,{type:"RELEASE"}).then((()=>{d(e)}))}const l=new WeakMap,E="FinalizationRegistry"in globalThis&&new FinalizationRegistry((e=>{const t=(l.get(e)||0)-1;l.set(e,t),0===t&&u(e)}));function T(e,r=[],o=function(){}){let i=!1;const a=new Proxy(o,{get(t,o){if(p(i),o===n)return()=>{!function(e){E&&E.unregister(e)}(a),u(e),i=!0};if("then"===o){if(0===r.length)return{then:()=>a};const t=g(e,{type:"GET",path:r.map((e=>e.toString()))}).then(h);return t.then.bind(t)}return T(e,[...r,o])},set(t,n,o){p(i);const[a,s]=f(o);return g(e,{type:"SET",path:[...r,n].map((e=>e.toString())),value:a},s).then(h)},apply(n,o,a){p(i);const s=r[r.length-1];if(s===t)return g(e,{type:"ENDPOINT"}).then(h);if("bind"===s)return T(e,r.slice(0,-1));const[c,d]=O(a);return g(e,{type:"APPLY",path:r.map((e=>e.toString())),argumentList:c},d).then(h)},construct(t,n){p(i);const[o,a]=O(n);return g(e,{type:"CONSTRUCT",path:r.map((e=>e.toString())),argumentList:o},a).then(h)}});return function(e,t){const r=(l.get(t)||0)+1;l.set(t,r),E&&E.register(e,t,e)}(a,e),a}function O(e){const t=e.map(f);return[t.map((e=>e[0])),(r=t.map((e=>e[1])),Array.prototype.concat.apply([],r))];var r}const _=new WeakMap;function f(e){for(const[t,r]of s)if(r.canHandle(e)){const[n,o]=r.serialize(e);return[{type:"HANDLER",name:t,value:n},o]}return[{type:"RAW",value:e},_.get(e)||[]]}function h(e){switch(e.type){case"HANDLER":return s.get(e.name).deserialize(e.value);case"RAW":return e.value}}function g(e,t,r){return new Promise((n=>{const o=new Array(4).fill(0).map((()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16))).join("-");e.addEventListener("message",(function t(r){r.data&&r.data.id&&r.data.id===o&&(e.removeEventListener("message",t),n(r.data))})),e.start&&e.start(),e.postMessage(Object.assign({id:o},t),r)}))}var R=r(954);c((e=>{let t=()=>{};return class{constructor(r,n,o){this.xatlas=null,this.loaded=!1,t=r||(()=>{}),this.atlasCreated=!1,this.meshes=[];let i={};o&&(i={...i,onAtlasProgress:o});const a=t=>{i={...i,locateFile:(e,r)=>t&&"xatlas.wasm"===e?t:r+e},e(i).then((e=>{this.moduleLoaded(e)}))};if(n){const e=n("xatlas.wasm","");e&&e.then?e.then(a):a(e)}else a()}moduleLoaded(e){this.xatlas=e,this.loaded=!0,t&&t()}createAtlas(){this.xatlas.createAtlas(),this.meshes=[],this.atlasCreated=!0}addMesh(e,t,r=null,n=null,o=void 0,i=!1,a=!1,s=1){if(!this.loaded||!this.atlasCreated)throw"Create atlas first";const c=this.xatlas.createMesh(t.length/3,e.length,null!=r&&i,null!=n&&a);this.xatlas.HEAPU32.set(e,c.indexOffset/4);const d=new Float32Array([...t]);if(1!==s){"number"==typeof s&&(s=[s,s,s]);for(let e=0,t=d.length;e<t;e+=3)d[e]*=s[0],d[e+1]*=s[1],d[e+2]*=s[2]}this.xatlas.HEAPF32.set(d,c.positionOffset/4),null!=r&&i&&this.xatlas.HEAPF32.set(r,c.normalOffset/4),null!=n&&a&&this.xatlas.HEAPF32.set(n,c.uvOffset/4);const p=this.xatlas.addMesh();if(0!==p)return console.log("Error adding mesh: ",p),null;const u={meshId:c.meshId,meshObj:o,vertices:t,normals:r||null,indexes:e||null,coords:n||null};return this.meshes.push(u),u}createMesh(e,t,r,n){return this.xatlas.createMesh(e,t,r,n)}generateAtlas(e,t,r=!0){if(!this.loaded||!this.atlasCreated)throw"Create atlas first";if(this.meshes.length<1)throw"Add meshes first";return e={...this.defaultChartOptions(),...e},t={...this.defaultPackOptions(),...t},this.xatlas.generateAtlas(e,t),r?this.getAtlas():[]}getAtlas(){const e=[];if(!this.loaded||!this.atlasCreated)throw"Create atlas first";const t=this.xatlas.getAtlas(),r={width:t.width,height:t.height,atlasCount:t.atlasCount,meshCount:t.meshCount,texelsPerUnit:t.texelsPerUnit};for(const{meshId:t,meshObj:r,vertices:n,normals:o,coords:i}of this.meshes){const a=this.getMeshData(t),s=a.newVertexCount,c=new Uint32Array(this.xatlas.HEAPU32.subarray(a.indexOffset/4,a.indexOffset/4+a.newIndexCount)),d=new Uint32Array(this.xatlas.HEAPU32.subarray(a.originalIndexOffset/4,a.originalIndexOffset/4+s)),p=new Float32Array(this.xatlas.HEAPF32.subarray(a.uvOffset/4,a.uvOffset/4+2*s)),u=[];for(let e=0,t=a.subMeshes.size();e<t;e++)u.push(a.subMeshes.get(e));this.xatlas.destroyMeshData(a),a.subMeshes.delete();const l={};l.vertices=new Float32Array(3*s),l.coords1=p,o&&(l.normals=new Float32Array(3*s)),l.coords=i?new Float32Array(2*s):l.coords1;for(let e=0,t=s;e<t;e++){const t=d[e];l.vertices[3*e+0]=n[3*t+0],l.vertices[3*e+1]=n[3*t+1],l.vertices[3*e+2]=n[3*t+2],l.normals&&o&&(l.normals[3*e+0]=o[3*t+0],l.normals[3*e+1]=o[3*t+1],l.normals[3*e+2]=o[3*t+2]),l.coords&&i&&(l.coords[2*e+0]=i[2*t+0],l.coords[2*e+1]=i[2*t+1])}e.push({index:c,vertex:l,mesh:r,vertexCount:s,oldIndexes:d,subMeshes:u})}return{...r,meshes:e}}defaultChartOptions(){return{fixWinding:!1,maxBoundaryLength:0,maxChartArea:0,maxCost:2,maxIterations:1,normalDeviationWeight:2,normalSeamWeight:4,roundnessWeight:.009999999776482582,straightnessWeight:6,textureSeamWeight:.5,useInputMeshUvs:!1}}defaultPackOptions(){return{bilinear:!0,blockAlign:!1,bruteForce:!1,createImage:!1,maxChartSize:0,padding:0,resolution:0,rotateCharts:!0,rotateChartsToAxis:!0,texelsPerUnit:0}}setProgressLogging(e){this.xatlas.setProgressLogging(e)}getMeshData(e){return this.xatlas.getMeshData(e)}destroyMeshData(e){this.xatlas.destroyMeshData(e)}destroyAtlas(){this.atlasCreated=!1,this.xatlas.destroyAtlas(),this.meshes=[],this.xatlas.doLeakCheck()}}})(r.n(R)()))})(),self.XAtlas={}})();
//# sourceMappingURL=xatlas.js.map