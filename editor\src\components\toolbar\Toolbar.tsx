/**
 * 工具栏组件 - 参考原项目ir-engine-dev的简洁设计
 */
import React from 'react';
import { <PERSON><PERSON>, Tooltip } from 'antd';
import {
  UndoOutlined,
  RedoOutlined,
  SelectOutlined,
  RotateLeftOutlined,
  ExpandOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  BorderOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined,
  AimOutlined,
  GlobalOutlined,
  HomeOutlined,
  CompressOutlined,
  DragOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  undo,
  redo,
  setTransformMode,
  setTransformSpace,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace
} from '../../store/editor/editorSlice';
import { toggleFullscreen } from '../../store/ui/uiSlice';
import ThreeRenderService from '../../services/ThreeRenderService';
import './Toolbar.less';

const Toolbar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  // 移除未使用的 useParams

  // 获取编辑器状态
  const transformMode = useAppSelector(state => state.editor.transformMode);
  const transformSpace = useAppSelector(state => state.editor.transformSpace);
  const showGrid = useAppSelector(state => state.editor.showGrid);
  const showAxes = useAppSelector(state => state.editor.showAxes);
  const isPlaying = useAppSelector(state => state.editor.isPlaying);
  const fullscreen = useAppSelector(state => state.ui.fullscreen);

  // 移除未使用的变量和函数

  // 处理变换模式切换
  const handleTransformModeChange = (mode: TransformMode) => {
    dispatch(setTransformMode(mode));
    const renderService = ThreeRenderService.getInstance();
    renderService.setTransformMode(mode);
  };

  // 处理变换空间切换
  const handleTransformSpaceChange = (space: TransformSpace) => {
    dispatch(setTransformSpace(space));
    const renderService = ThreeRenderService.getInstance();
    renderService.setTransformSpace(space);
  };

  // 处理网格显示切换
  const handleToggleGrid = () => {
    dispatch(setShowGrid(!showGrid));
  };

  // 处理坐标轴显示切换
  const handleToggleAxes = () => {
    dispatch(setShowAxes(!showAxes));
  };

  // 处理播放/暂停切换
  const handleTogglePlay = () => {
    dispatch(setIsPlaying(!isPlaying));
  };

  // 处理全屏切换
  const handleToggleFullscreen = () => {
    dispatch(toggleFullscreen());
  };

  // 处理撤销
  const handleUndo = () => {
    dispatch(undo());
  };

  // 处理重做
  const handleRedo = () => {
    dispatch(redo());
  };

  // 处理视图重置
  const handleResetView = () => {
    const renderService = ThreeRenderService.getInstance();
    // 临时实现：重置相机位置
    const camera = renderService.getCamera();
    if (camera) {
      camera.position.set(5, 5, 5);
      camera.lookAt(0, 0, 0);
    }
  };

  // 处理聚焦选中对象
  const handleFocusSelected = () => {
    const renderService = ThreeRenderService.getInstance();
    // 临时实现：聚焦到原点
    const camera = renderService.getCamera();
    if (camera) {
      camera.lookAt(0, 0, 0);
    }
  };

  return (
    <div className="editor-toolbar">
      {/* 左侧：工具按钮 */}
      <div className="toolbar-section tools-section">

        {/* 编辑工具 */}
        <div className="toolbar-group">
          <Tooltip title={t('editor.undo') || '撤销'}>
            <Button
              size="small"
              type="text"
              icon={<UndoOutlined />}
              onClick={handleUndo}
            />
          </Tooltip>

          <Tooltip title={t('editor.redo') || '重做'}>
            <Button
              size="small"
              type="text"
              icon={<RedoOutlined />}
              onClick={handleRedo}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 变换工具 */}
        <div className="toolbar-group toolbar-button-group">
          <span className="toolbar-label">变换</span>
          <Tooltip title={t('editor.select') || '选择'}>
            <Button
              size="small"
              type="text"
              icon={<SelectOutlined />}
              className={transformMode === TransformMode.SELECT ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.SELECT)}
            />
          </Tooltip>

          <Tooltip title={t('editor.translate') || '移动'}>
            <Button
              size="small"
              type="text"
              icon={<DragOutlined />}
              className={transformMode === TransformMode.TRANSLATE ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.TRANSLATE)}
            />
          </Tooltip>

          <Tooltip title={t('editor.rotate') || '旋转'}>
            <Button
              size="small"
              type="text"
              icon={<RotateLeftOutlined />}
              className={transformMode === TransformMode.ROTATE ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.ROTATE)}
            />
          </Tooltip>

          <Tooltip title={t('editor.scale') || '缩放'}>
            <Button
              size="small"
              type="text"
              icon={<ExpandOutlined />}
              className={transformMode === TransformMode.SCALE ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformModeChange(TransformMode.SCALE)}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 变换空间 */}
        <div className="toolbar-group toolbar-button-group">
          <span className="toolbar-label">空间</span>
          <Tooltip title={t('editor.worldSpace') || '世界空间'}>
            <Button
              size="small"
              type="text"
              icon={<GlobalOutlined />}
              className={transformSpace === TransformSpace.WORLD ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformSpaceChange(TransformSpace.WORLD)}
            />
          </Tooltip>

          <Tooltip title={t('editor.localSpace') || '本地空间'}>
            <Button
              size="small"
              type="text"
              icon={<HomeOutlined />}
              className={transformSpace === TransformSpace.LOCAL ? 'tool-button selected' : 'tool-button'}
              onClick={() => handleTransformSpaceChange(TransformSpace.LOCAL)}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 视图控制 */}
        <div className="toolbar-group">
          <Tooltip title={t('editor.resetView') || '重置视图'}>
            <Button
              size="small"
              type="text"
              icon={<AimOutlined />}
              onClick={handleResetView}
            />
          </Tooltip>

          <Tooltip title={t('editor.focusSelected') || '聚焦选中'}>
            <Button
              size="small"
              type="text"
              icon={<CompressOutlined />}
              onClick={handleFocusSelected}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 显示选项 */}
        <div className="toolbar-group">
          <Tooltip title={showGrid ? (t('editor.hideGrid') || '隐藏网格') : (t('editor.showGrid') || '显示网格')}>
            <Button
              size="small"
              type="text"
              icon={<BorderOutlined />}
              className={showGrid ? 'tool-button selected' : 'tool-button'}
              onClick={handleToggleGrid}
            />
          </Tooltip>

          <Tooltip title={showAxes ? (t('editor.hideAxes') || '隐藏坐标轴') : (t('editor.showAxes') || '显示坐标轴')}>
            <Button
              size="small"
              type="text"
              icon={showAxes ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              className={showAxes ? 'tool-button selected' : 'tool-button'}
              onClick={handleToggleAxes}
            />
          </Tooltip>
        </div>

        <div className="toolbar-divider" />

        {/* 播放控制 */}
        <div className="toolbar-group">
          <Tooltip title={isPlaying ? (t('editor.pause') || '暂停') : (t('editor.play') || '播放')}>
            <Button
              size="small"
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              className={isPlaying ? 'tool-button selected' : 'tool-button'}
              onClick={handleTogglePlay}
            />
          </Tooltip>
        </div>
      </div>

      {/* 右侧：设置按钮 */}
      <div className="toolbar-section actions-section">
        {/* 全屏切换 */}
        <Tooltip title={fullscreen ? (t('editor.exitFullscreen') || '退出全屏') : (t('editor.fullscreen') || '全屏')}>
          <Button
            size="small"
            type="text"
            icon={fullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={handleToggleFullscreen}
          />
        </Tooltip>

        {/* 设置 */}
        <Tooltip title={t('editor.settings') || '设置'}>
          <Button
            size="small"
            type="text"
            icon={<SettingOutlined />}
          />
        </Tooltip>
      </div>
    </div>
  );
};

export default Toolbar;
