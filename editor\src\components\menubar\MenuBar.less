/**
 * 编辑器菜单栏样式
 */
.editor-menubar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  padding: 0 8px;
  background: #2a2a2a;
  border-bottom: 1px solid #3a3a3a;
  user-select: none;
  font-size: 13px;

  .menubar-left {
    display: flex;
    align-items: center;

    .menu-button {
      height: 28px;
      padding: 0 8px;
      border: none;
      border-radius: 4px;
      color: #ccc;
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      &:focus {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      .anticon {
        font-size: 12px;
      }
    }
  }

  .menubar-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;

    .project-path {
      display: flex;
      align-items: center;
      color: #888;
      font-size: 13px;
      gap: 6px;

      .path-icon {
        font-size: 12px;
        color: #666;
      }

      .path-text {
        color: #ccc;
      }
    }
  }

  .menubar-right {
    display: flex;
    align-items: center;

    .user-button {
      height: 28px;
      width: 28px;
      border: none;
      border-radius: 4px;
      color: #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }
    }

    .publish-button {
      height: 28px;
      padding: 0 12px;
      background: #1890ff;
      border-color: #1890ff;
      border-radius: 4px;
      font-size: 12px;
      
      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }

      .anticon {
        font-size: 12px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .menubar-center {
      display: none;
    }
  }

  @media (max-width: 768px) {
    padding: 0 4px;
    
    .menubar-left {
      .menu-button {
        padding: 0 6px;
        font-size: 12px;
      }
    }

    .menubar-right {
      .publish-button {
        padding: 0 8px;
        font-size: 11px;
      }
    }
  }
}

// 菜单下拉样式
.menubar-dropdown {
  .ant-dropdown-menu {
    background-color: #2a2a2a;
    border: 1px solid #444;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 180px;

    .ant-dropdown-menu-item {
      color: #ccc;
      padding: 8px 12px;
      font-size: 13px;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
      }

      .ant-dropdown-menu-item-icon {
        color: #888;
        margin-right: 8px;
        font-size: 12px;
      }
    }

    .ant-dropdown-menu-item-divider {
      background-color: #444;
      margin: 4px 0;
    }

    .ant-dropdown-menu-item-disabled {
      color: #555;
      
      &:hover {
        background-color: transparent;
        color: #555;
      }
    }
  }
}

// 菜单项状态
.menu-item-active {
  background-color: rgba(24, 144, 255, 0.1) !important;
  color: #1890ff !important;
}

// 动画效果
@keyframes menubarSlideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.editor-menubar {
  animation: menubarSlideDown 0.3s ease-out;
}

// 菜单按钮激活状态
.menu-button-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}

// 工具提示样式
.menubar-tooltip {
  .ant-tooltip-inner {
    background-color: #2a2a2a;
    color: #fff;
    border: 1px solid #444;
    font-size: 12px;
  }

  .ant-tooltip-arrow::before {
    background-color: #2a2a2a;
    border: 1px solid #444;
  }
}

// 菜单栏分隔线
.menubar-divider {
  width: 1px;
  height: 20px;
  background-color: #444;
  margin: 0 8px;
}

// 菜单栏标签
.menubar-label {
  color: #888;
  font-size: 11px;
  margin-right: 4px;
  white-space: nowrap;
}

// 快捷键显示
.menu-shortcut {
  color: #666;
  font-size: 11px;
  margin-left: auto;
  padding-left: 16px;
}

// 菜单项图标
.menu-item-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  
  .anticon {
    font-size: 12px;
  }
}

// 菜单项文本
.menu-item-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 子菜单箭头
.menu-submenu-arrow {
  margin-left: auto;
  color: #666;
  font-size: 10px;
}

// 菜单项状态指示器
.menu-item-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
  
  &.active {
    background-color: #52c41a;
  }
  
  &.inactive {
    background-color: #666;
  }
  
  &.warning {
    background-color: #faad14;
  }
  
  &.error {
    background-color: #ff4d4f;
  }
}
