<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
  <circle cx="30" cy="30" r="28.5" stroke="url(#paint0_radial_2205_29412)" stroke-opacity="0.9" stroke-width="3"/>
  <foreignObject x="-86.2" y="-86.2" width="232.4" height="232.4"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(52.1px);clip-path:url(#bgblur_0_2205_29412_clip_path);height:100%;width:100%"></div></foreignObject>
<defs>
    <filter id="filter0_dii_2205_29412" x="-86.2" y="-86.2" width="232.4" height="232.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="8.34"/>
      <feGaussianBlur stdDeviation="3.125"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2205_29412"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2205_29412" result="shape"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="-1.04"/>
      <feGaussianBlur stdDeviation="0.52"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.1 0"/>
      <feBlend mode="normal" in2="shape" result="effect2_innerShadow_2205_29412"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="1.04"/>
      <feGaussianBlur stdDeviation="0.52"/>
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
      <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="effect2_innerShadow_2205_29412" result="effect3_innerShadow_2205_29412"/>
    </filter>
  <clipPath id="bgblur_0_2205_29412_clip_path" transform="translate(86.2 86.2)"><circle cx="30" cy="30" r="12"/>
  </clipPath>
<radialGradient id="paint0_radial_2205_29412" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(15.6546 15.4443) rotate(45.773) scale(53.1517 56.5163)">
  <stop offset="0.192708" stop-color="white" stop-opacity="0.801323"/>
  <stop offset="0.2" stop-color="white"/>
  <stop offset="0.404591" stop-color="white" stop-opacity="0.595409"/>
  <stop offset="0.552083" stop-color="white"/>
  <stop offset="0.898086" stop-color="white"/>
  <stop offset="0.9" stop-color="white"/>
  <stop offset="0.961468" stop-color="white"/>
</radialGradient>
<radialGradient id="paint1_radial_2205_29412" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24.2618 24.1777) rotate(45.773) scale(21.2607 22.6065)">
  <stop offset="0.192708" stop-color="white" stop-opacity="0.801323"/>
  <stop offset="0.2" stop-color="white"/>
  <stop offset="0.404591" stop-color="white" stop-opacity="0.595409"/>
  <stop offset="0.552083" stop-color="white"/>
  <stop offset="0.898086" stop-color="white"/>
  <stop offset="0.9" stop-color="white"/>
  <stop offset="0.961468" stop-color="white"/>
</radialGradient>
</defs>
</svg>