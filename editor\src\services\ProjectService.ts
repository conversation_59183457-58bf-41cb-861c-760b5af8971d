/**
 * 项目服务
 * 处理项目的加载、保存和管理
 */

export interface Project {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  path: string;
  scenes: Scene[];
  assets: Asset[];
  settings: ProjectSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface Scene {
  id: string;
  name: string;
  path: string;
  thumbnail: string;
  description?: string;
  isDefault?: boolean;
}

export interface Asset {
  id: string;
  name: string;
  type: 'model' | 'texture' | 'audio' | 'video' | 'material' | 'prefab';
  path: string;
  thumbnail?: string;
  tags?: string[];
  size?: number;
}

export interface ProjectSettings {
  rendering: {
    shadows: boolean;
    postProcessing: boolean;
    antiAliasing: boolean;
  };
  physics: {
    enabled: boolean;
    gravity: number;
  };
  audio: {
    enabled: boolean;
    volume: number;
  };
}

class ProjectService {
  private static instance: ProjectService;
  private projects: Map<string, Project> = new Map();

  public static getInstance(): ProjectService {
    if (!ProjectService.instance) {
      ProjectService.instance = new ProjectService();
    }
    return ProjectService.instance;
  }

  /**
   * 加载默认项目
   */
  public async loadDefaultProject(): Promise<Project> {
    const defaultProject: Project = {
      id: 'default-project',
      name: 'DL Engine 默认项目',
      description: 'DL (Digital Learning) Engine 的默认示例项目',
      thumbnail: '/projects/default-project/public/scenes/default.thumbnail.jpg',
      path: '/projects/default-project',
      scenes: [
        {
          id: 'default',
          name: '默认场景',
          path: '/projects/default-project/public/scenes/default.gltf',
          thumbnail: '/projects/default-project/public/scenes/default.thumbnail.jpg',
          description: '基础演示场景，包含基本的几何体和光照',
          isDefault: true
        },
        {
          id: 'apartment',
          name: '公寓场景',
          path: '/projects/default-project/public/scenes/apartment.gltf',
          thumbnail: '/projects/default-project/public/scenes/apartment.thumbnail.jpg',
          description: '室内公寓场景，展示室内环境和家具'
        },
        {
          id: 'sky-station',
          name: '天空站',
          path: '/projects/default-project/public/scenes/sky-station.gltf',
          thumbnail: '/projects/default-project/public/scenes/sky-station.thumbnail.jpg',
          description: '科幻风格的天空站场景'
        },
        {
          id: 'sponza',
          name: 'Sponza 场景',
          path: '/projects/default-project/public/scenes/sponza.gltf',
          thumbnail: '/projects/default-project/public/scenes/sponza.thumbnail.jpg',
          description: '经典的Sponza测试场景，用于光照和渲染测试'
        }
      ],
      assets: [
        {
          id: 'sample-audio',
          name: '示例音频',
          type: 'audio',
          path: '/projects/default-project/assets/SampleAudio.mp3',
          tags: ['demo', 'audio']
        },
        {
          id: 'sample-video',
          name: '示例视频',
          type: 'video',
          path: '/projects/default-project/assets/SampleVideo.mp4',
          tags: ['demo', 'video']
        },
        {
          id: 'apartment-model',
          name: '公寓模型',
          type: 'model',
          path: '/projects/default-project/assets/apartment.glb',
          tags: ['architecture', 'interior']
        },
        {
          id: 'platform-model',
          name: '平台模型',
          type: 'model',
          path: '/projects/default-project/assets/platform.glb',
          tags: ['geometry', 'basic']
        }
      ],
      settings: {
        rendering: {
          shadows: true,
          postProcessing: true,
          antiAliasing: true
        },
        physics: {
          enabled: true,
          gravity: -9.81
        },
        audio: {
          enabled: true,
          volume: 0.8
        }
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.projects.set(defaultProject.id, defaultProject);
    return defaultProject;
  }

  /**
   * 获取项目列表
   */
  public async getProjects(): Promise<Project[]> {
    if (this.projects.size === 0) {
      await this.loadDefaultProject();
    }
    return Array.from(this.projects.values());
  }

  /**
   * 根据ID获取项目
   */
  public async getProject(id: string): Promise<Project | null> {
    if (this.projects.size === 0) {
      await this.loadDefaultProject();
    }
    return this.projects.get(id) || null;
  }

  /**
   * 获取项目的场景列表
   */
  public async getProjectScenes(projectId: string): Promise<Scene[]> {
    const project = await this.getProject(projectId);
    return project ? project.scenes : [];
  }

  /**
   * 根据ID获取场景
   */
  public async getScene(projectId: string, sceneId: string): Promise<Scene | null> {
    const project = await this.getProject(projectId);
    if (!project) return null;
    
    return project.scenes.find(scene => scene.id === sceneId) || null;
  }

  /**
   * 获取项目的资源列表
   */
  public async getProjectAssets(projectId: string): Promise<Asset[]> {
    const project = await this.getProject(projectId);
    return project ? project.assets : [];
  }

  /**
   * 创建新项目
   */
  public async createProject(projectData: Partial<Project>): Promise<Project> {
    const newProject: Project = {
      id: projectData.id || `project-${Date.now()}`,
      name: projectData.name || '新项目',
      description: projectData.description || '',
      thumbnail: projectData.thumbnail || '',
      path: projectData.path || '',
      scenes: projectData.scenes || [],
      assets: projectData.assets || [],
      settings: projectData.settings || {
        rendering: { shadows: true, postProcessing: true, antiAliasing: true },
        physics: { enabled: true, gravity: -9.81 },
        audio: { enabled: true, volume: 0.8 }
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.projects.set(newProject.id, newProject);
    return newProject;
  }

  /**
   * 更新项目
   */
  public async updateProject(id: string, updates: Partial<Project>): Promise<Project | null> {
    const project = this.projects.get(id);
    if (!project) return null;

    const updatedProject = {
      ...project,
      ...updates,
      updatedAt: new Date()
    };

    this.projects.set(id, updatedProject);
    return updatedProject;
  }

  /**
   * 删除项目
   */
  public async deleteProject(id: string): Promise<boolean> {
    return this.projects.delete(id);
  }
}

export default ProjectService;
