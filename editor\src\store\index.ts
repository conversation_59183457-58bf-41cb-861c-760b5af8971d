/**
 * Redux存储配置
 */
import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

// 导入核心状态切片
import authReducer from './auth/authSlice';
import editorReducer from './editor/editorSlice';
import uiReducer from './ui/uiSlice';
import layoutReducer from './ui/layoutSlice';
import sceneReducer from './scene/sceneSlice';

// 导入协作相关切片
import conflictReducer from './collaboration/conflictSlice';
import aiResolverReducer from './collaboration/aiResolverSlice';
import collaborationReducer from './collaboration/collaborationSlice';
import conflictPredictionReducer from './collaboration/conflictPredictionSlice';
import conflictVisualizationReducer from './collaboration/conflictVisualizationSlice';
import connectionReducer from './collaboration/connectionSlice';
import editingZonesReducer from './collaboration/editingZonesSlice';
import lockReducer from './collaboration/lockSlice';
import permissionReducer from './collaboration/permissionSlice';
import versionReducer from './collaboration/versionSlice';

// 导入其他切片
import gitReducer from './git/gitSlice';
import achievementsReducer from './achievements/achievementsSlice';
import animationsReducer from './animations/animationsSlice';
import blendSpaceReducer from './animations/blendSpaceSlice';
import stateMachineReducer from './animations/stateMachineSlice';
import userTestingReducer from './testing/userTestingSlice';
import resourceVersionReducer from './resources/resourceVersionSlice';
import resourceHotUpdateReducer from './resources/resourceHotUpdateSlice';
import physicsReducer from './physics/physicsSlice';
import waterReducer from './physics/waterSlice';
import terrainReducer from './terrain/terrainSlice';
import particlesReducer from './particles/particlesSlice';
import debugReducer from './debug/debugSlice';
import undergroundLightingReducer from './rendering/undergroundLightingSlice';
import waterMaterialReducer from './rendering/waterMaterialSlice';

// 尝试导入其他切片，如果失败则使用默认值
let projectReducer, assetReducer, materialsReducer;
try {
  projectReducer = require('./project/projectSlice').default;
} catch {
  projectReducer = (state = {}) => state;
}

try {
  assetReducer = require('./asset/assetSlice').default;
} catch {
  assetReducer = (state = {}) => state;
}

try {
  materialsReducer = require('./materials/materialsSlice').default;
} catch {
  materialsReducer = (state = {}) => state;
}

// 创建Redux存储
export const store = configureStore({
  reducer: {
    auth: authReducer,
    project: projectReducer,
    editor: editorReducer,
    asset: assetReducer,
    scene: sceneReducer,
    ui: uiReducer,
    layout: layoutReducer,
    materials: materialsReducer,
    // 协作相关
    conflict: conflictReducer,
    aiResolver: aiResolverReducer,
    collaboration: collaborationReducer,
    conflictPrediction: conflictPredictionReducer,
    conflictVisualization: conflictVisualizationReducer,
    connection: connectionReducer,
    editingZones: editingZonesReducer,
    lock: lockReducer,
    permission: permissionReducer,
    version: versionReducer,
    // 其他功能
    git: gitReducer,
    achievements: achievementsReducer,
    animations: animationsReducer,
    blendSpace: blendSpaceReducer,
    stateMachine: stateMachineReducer,
    userTesting: userTestingReducer,
    resourceVersion: resourceVersionReducer,
    resourceHotUpdate: resourceHotUpdateReducer,
    physics: physicsReducer,
    water: waterReducer,
    terrain: terrainReducer,
    particles: particlesReducer,
    debug: debugReducer,
    undergroundLighting: undergroundLightingReducer,
    waterMaterial: waterMaterialReducer
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略某些非可序列化的值
        ignoredActions: ['editor/setActiveCamera', 'editor/setSelectedObject'],
        ignoredPaths: ['editor.activeCamera', 'editor.selectedObject']
      }
    })
});

// 导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出钩子
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
