{"components": {"cancel": "Cancel", "confirm": "Confirm", "back": "Back", "configure": "Configure", "continue": "Continue", "select": "Select", "enter": "Enter", "processing": "Processing", "retry": "Retry", "close": "Close", "submit": "Submit", "search": "Search", "copyText": "Copy Text", "edit": "Edit", "save": "Save", "none": "None", "update": "Update", "clear": "Clear", "embedCodeCopied": "Embed code copied to clipboard!", "embed": "Embed", "copyEmbedCode": "Copy Embed Code", "publishSceneFirstMessage": "You need to publish this scene first to get an embed code."}, "tooltip": {"pressKey": "Press {{tip}} to {{message}}"}, "interactables": {"link": "Click to follow"}, "toast": {"joined": "joined", "left": "left"}, "unsupportedDevice": {"title": "Unsupported device type!", "description": "Please use a desktop running the latest version of Google Chrome for the best experience with the Napster 3D Studio."}, "unsupportedBrowser": {"title": "Browser Not Supported", "description": "Please use Google Chrome for the best experience with the Napster 3D Studio.", "downloadChrome": "Download Chrome", "continue": "Continue Anyway"}, "loader": {"connecting": "Connecting...", "loadingClient": "Loading client...", "authenticating": "Authenticating...", "starting": "Starting...", "loadingApp": "Loading app...", "loadingLocation": "Loading Spaces...", "loadingProjects": "Loading Projects...", "loadingEngine": "Loading Engine...", "loadingEditor": "Loading Editor...", "loadingStudio": "Loading Studio...", "loadingRoutes": "Loading Routes...", "loadingRoute": "Loading Route...", "loadingAuth": "Loading Auth Routes...", "loadingAdmin": "Loading Admin Routes...", "loadingCustom": "Loading Custom Routes...", "loadingAllowed": "Loading Allowed Routes...", "loadingXRSystems": "Loading Immersive Session...", "connectingToWorld": "Connecting to Space...", "needToVerifyAge": "You must verify your age to access the chat.", "needToLogIn": "You must log in to access the chat.", "connectingToMedia": "Connecting to Media...", "entering": "Entering Space...", "loading": "Loading...", "loadingDynamic": "Loading {{name}}", "objectRemaining": "{{count}} object remaining", "objectRemainingPlural": "{{count}} objects remaining", "loadingObjects": "Loading Objects", "loadingComplete": "Loading Complete", "joiningWorld": "Joining Space", "editor": "Loading Editor", "offline": "Loading Offline", "auth": "Loading <PERSON><PERSON>"}, "alert": {"eventWasSuccess": "The event was successful", "errorWasEncountered": "An error ocurred", "eventInProgress": "The event is in progress", "seconds": "seconds", "cancelCountdown": "Closing this window will cancel the countdown."}, "debug": {"refresh": "Refresh", "stats": "Stats", "downloadState": "Download State Snapshot", "debugOptions": "Debug Options", "debug": "Debug Helpers", "preSystems": "Pre Systems", "simulation": "Simulation Group", "subSystems": "Sub System", "postSystems": "Post Systems", "nodeHelperDebug": "<PERSON><PERSON> Helper", "scenes": "Scenes", "erroredEntities": "Errored <PERSON>", "gridDebug": "Grid", "tick": "Tick", "state": "State", "api": "API", "systems": "Systems", "resources": "Resources", "avgDuration": "Avg Duration: ", "entities": "Entities", "networks": "Networks", "actionsHistory": "Actions History", "actionsCached": "Actions Cached", "physicsDebug": "Physics Debug", "bvhDebug": "BVH Debug", "avatarDebug": "Avatar Debug", "respawn": "Respawn", "close": "Close Debug"}, "recording": {"videoRecorder": "Video recorder", "waitingForPermissions": "Waiting for permissions", "selectVideoSource": "Select video source", "screen": "Screen", "camera": "Camera", "microphone": "Microphone", "startRecording": "Start recording", "pauseRecording": "Pause recording", "resumeRecording": "Resume recording", "stopRecording": "Stop recording"}, "typing": "Typing...", "instanceServer": {"browserError": "Browser Error", "browserErrorMessage": "Your browser does not support storage in private browsing mode. Either try another browser, or exit private browsing mode.", "noAvailableServers": "No Available Servers", "noAvailableServersMessage": "There are not any servers available for you to connect to. Attempting to re-connect in", "worldDisconnected": "World disconnected", "worldDisconnectedMessage": "You have lost your connection to the world. We will attempt to reconnect before the timer runs out; otherwise, you will be redirected to a different instance.", "webGLNotEnabled": "WebGL not enabled", "webGLNotEnabledMessage": "Your browser does not support WebGL, or it is disabled. Please enable WebGL or consider upgrading to the latest version of your browser.", "youKickedFromWorld": "You have been kicked from the world", "youKickedFromWorldMessage": "You were kicked from this world for the following reason", "invalidLocation": "Invalid location", "cantFindLocation": "We can't find the location", "notAuthorizedAtLocationTitle": "Not Authorized at this location", "notAuthorizedAtLocation": "You are not authorized to be at this location", "misspelledOrNotExist": "It may be misspelled, or it may not exist.", "loading": "loading", "low-frame-error": "Poor performance detected. Consider enabling hardware acceleration in your browser and relaunching it", "low-frame-title": "Enable hardware acceleration for a better experience!"}, "ar": {"placeScene": "Place Scene", "auto": "Auto", "doneTrigger": "Hold Trigger To Confirm", "done": "Done"}, "error": {"loading-error": "Unable to load user data", "validation-error": "Enter a valid {{type}}", "login-error": "Login attempt failed", "expiredToken": "The token has expired."}, "table": {"refetching": "Refetching Data...", "noData": "Data is Empty", "pagination": {"prev": "Prev", "next": "Next"}}, "multiEmailInput": {"placeholder": "Type or paste email addresses, then press 'Enter'.", "alreadyAdded": "{{email}} has already been added!", "invalidEmail": "{{email}} is not a valid email address!"}, "exploreRedirect": "Explore Worlds", "select": {"fetching": "Fetching options...", "filter": "Filter options", "selectOption": "Select Option", "selectOptions": "Select Options"}, "timeAgo": "{{time}} ago"}