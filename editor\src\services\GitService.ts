/**
 * Git服务
 * 负责管理Git版本控制功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
// import axios from 'axios';
import { httpClient } from '../utils/httpClient';
import { store } from '../store';
import {
  setGitStatus,
  setGitBranches,
  setCurrentBranch,
  setCommitHistory,
  setUnstagedFiles,
  setStagedFiles,
  setConflictFiles,
  setIsLoading,
  setRemotes,
  setCurrentRemote
} from '../store/git/gitSlice';

// Git事件类型
export enum GitServiceEventType {
  STATUS_CHANGED = 'statusChanged',
  BRANCH_CHANGED = 'branchChanged',
  COMMIT_CREATED = 'commitCreated',
  PUSH_COMPLETED = 'pushCompleted',
  PULL_COMPLETED = 'pullCompleted',
  MERGE_STARTED = 'mergeStarted',
  MERGE_COMPLETED = 'mergeCompleted',
  MERGE_CONFLICT = 'mergeConflict',
  CONFLICT_RESOLVED = 'conflictResolved',
  ERROR = 'error'
}

// Git状态接口
export interface GitStatus {
  isRepo: boolean;
  branch: string;
  ahead: number;
  behind: number;
  staged: number;
  unstaged: number;
  untracked: number;
  conflicted: number;
  files?: GitFileStatus[];
}

// Git分支接口
export interface GitBranch {
  name: string;
  current: boolean;
  remote: boolean;
  remoteName?: string;
  remoteTrackingBranch?: string;
  isDetached: boolean;
}

// Git提交接口
export interface GitCommit {
  hash: string;
  shortHash: string;
  date: string;
  message: string;
  author: string;
  email: string;
}

// Git文件状态接口
export interface GitFileStatus {
  path: string;
  status: string;
  staged: boolean;
}

// Git冲突接口
export interface GitConflict {
  path: string;
  content: string;
  ourContent: string;
  theirContent: string;
  baseContent: string;
}

// Git远程仓库接口
export interface GitRemote {
  name: string;
  url: string;
}

// Git服务配置接口
export interface GitServiceConfig {
  apiUrl: string;
  debug?: boolean;
  autoFetch?: boolean;
  fetchInterval?: number;
}

/**
 * Git服务类
 */
class GitService extends EventEmitter {
  private static instance: GitService;
  private config: GitServiceConfig;
  private fetchTimer: NodeJS.Timeout | null = null;
  private consecutiveErrors: number = 0;
  private maxConsecutiveErrors: number = 3;

  /**
   * 获取Git服务实例
   * @param config 配置
   * @returns Git服务实例
   */
  public static getInstance(config?: Partial<GitServiceConfig>): GitService {
    if (!GitService.instance) {
      GitService.instance = new GitService(config);
    }
    return GitService.instance;
  }

  /**
   * 创建Git服务实例
   * @param config 配置
   */
  private constructor(config?: Partial<GitServiceConfig>) {
    super();

    // 默认配置
    this.config = {
      apiUrl: '/api/git',
      debug: false,
      autoFetch: true,
      fetchInterval: 60000, // 1分钟
      ...config
    };

    // 初始化
    this.initialize();
  }

  /**
   * 初始化
   */
  private initialize(): void {
    if (this.config.debug) {
      console.log('Git服务初始化');
    }

    // 不在初始化时立即获取状态，等待用户认证后再开始
    // 也不启动自动获取，避免在用户未认证时发送API请求
  }

  /**
   * 设置自动获取
   */
  private setupAutoFetch(): void {
    if (this.fetchTimer) {
      clearInterval(this.fetchTimer);
    }

    this.fetchTimer = setInterval(() => {
      this.fetchStatus();
    }, this.config.fetchInterval);
  }

  /**
   * 停止自动获取
   */
  public stopAutoFetch(): void {
    if (this.fetchTimer) {
      clearInterval(this.fetchTimer);
      this.fetchTimer = null;
    }
  }

  /**
   * 启动自动获取
   */
  public startAutoFetch(): void {
    if (this.config.autoFetch) {
      this.setupAutoFetch();
    }
  }

  /**
   * 启动Git服务（在用户认证后调用）
   */
  public start(): void {
    // 设置自动获取
    if (this.config.autoFetch) {
      this.setupAutoFetch();
    }

    // 初始获取状态
    this.fetchStatus();
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    this.stopAutoFetch();
    this.removeAllListeners();
  }

  /**
   * 获取Git状态
   */
  public async fetchStatus(): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      const response = await httpClient.get('/git/status', {
        timeout: 5000
      });
      const status: GitStatus = response.data;

      // 重置错误计数器
      this.consecutiveErrors = 0;

      store.dispatch(setGitStatus(status));
      this.emit(GitServiceEventType.STATUS_CHANGED, status);

      // 获取分支信息
      await this.fetchBranches();

      // 获取文件状态
      await this.fetchFiles();

      if (this.config.debug) {
        console.log('Git状态获取成功', status);
      }
    } catch (error: any) {
      // 增加错误计数器
      this.consecutiveErrors++;

      // 如果是 404 或 401 错误，说明 Git API 不可用或未授权，静默处理
      if (error.response?.status === 404 || error.response?.status === 401) {
        if (this.config.debug) {
          console.warn(`Git API 不可用 (${error.response?.status})，跳过状态获取`);
        }

        // 如果连续错误次数过多，停止自动获取
        if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
          if (this.config.debug) {
            console.warn('Git API 连续不可用，停止自动获取');
          }
          this.stopAutoFetch();
        }

        // 设置默认状态表示 Git 不可用
        const defaultStatus: GitStatus = {
          isRepo: false,
          branch: '',
          ahead: 0,
          behind: 0,
          staged: 0,
          unstaged: 0,
          untracked: 0,
          conflicted: 0
        };
        store.dispatch(setGitStatus(defaultStatus));
        return;
      }

      console.error('获取Git状态失败', error);
      this.emit(GitServiceEventType.ERROR, error);

      // 只在非 404/401 错误时显示错误消息
      if (error.response?.status !== 404 && error.response?.status !== 401) {
        message.error('获取Git状态失败');
      }
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }

  /**
   * 获取分支信息
   */
  public async fetchBranches(): Promise<void> {
    try {
      const response = await httpClient.get('/git/branches');
      const branches: GitBranch[] = response.data?.branches || [];
      const currentBranch = branches.find(branch => branch.current)?.name || '';

      store.dispatch(setGitBranches(branches));
      store.dispatch(setCurrentBranch(currentBranch));

      if (this.config.debug) {
        console.log('Git分支获取成功', branches);
      }
    } catch (error: any) {
      // 如果是 404 或 401 错误，静默处理
      if (error.response?.status === 404 || error.response?.status === 401) {
        if (this.config.debug) {
          console.warn(`Git 分支 API 不可用 (${error.response?.status})`);
        }
        // 设置默认空分支列表
        store.dispatch(setGitBranches([]));
        store.dispatch(setCurrentBranch(''));
        return;
      }

      console.error('获取Git分支失败', error);
      this.emit(GitServiceEventType.ERROR, error);

      // 设置默认空分支列表
      store.dispatch(setGitBranches([]));
      store.dispatch(setCurrentBranch(''));
    }
  }

  /**
   * 获取远程仓库信息
   */
  public async fetchRemotes(): Promise<void> {
    try {
      const response = await httpClient.get('/git/remotes');
      const remotes: GitRemote[] = response.data?.remotes || [];
      const currentRemote = remotes.length > 0 ? remotes[0].name : '';

      store.dispatch(setRemotes(remotes));
      store.dispatch(setCurrentRemote(currentRemote));

      if (this.config.debug) {
        console.log('Git远程仓库获取成功', remotes);
      }
    } catch (error: any) {
      // 如果是 404 或 401 错误，静默处理
      if (error.response?.status === 404 || error.response?.status === 401) {
        if (this.config.debug) {
          console.warn(`Git 远程仓库 API 不可用 (${error.response?.status})`);
        }
        // 设置默认空远程仓库列表
        store.dispatch(setRemotes([]));
        store.dispatch(setCurrentRemote(''));
        return;
      }

      console.error('获取Git远程仓库失败', error);
      this.emit(GitServiceEventType.ERROR, error);

      // 设置默认空远程仓库列表
      store.dispatch(setRemotes([]));
      store.dispatch(setCurrentRemote(''));
    }
  }

  /**
   * 获取文件状态
   */
  public async fetchFiles(): Promise<void> {
    try {
      const response = await httpClient.get('/git/files');
      const { unstaged = [], staged = [], conflicted = [] } = response.data || {};

      store.dispatch(setUnstagedFiles(unstaged));
      store.dispatch(setStagedFiles(staged));
      store.dispatch(setConflictFiles(conflicted));

      if (this.config.debug) {
        console.log('Git文件状态获取成功', { unstaged, staged, conflicted });
      }
    } catch (error: any) {
      // 如果是 404 或 401 错误，静默处理
      if (error.response?.status === 404 || error.response?.status === 401) {
        if (this.config.debug) {
          console.warn(`Git 文件 API 不可用 (${error.response?.status})`);
        }
        // 设置默认空文件列表
        store.dispatch(setUnstagedFiles([]));
        store.dispatch(setStagedFiles([]));
        store.dispatch(setConflictFiles([]));
        return;
      }

      console.error('获取Git文件状态失败', error);
      this.emit(GitServiceEventType.ERROR, error);

      // 设置默认空文件列表
      store.dispatch(setUnstagedFiles([]));
      store.dispatch(setStagedFiles([]));
      store.dispatch(setConflictFiles([]));
    }
  }

  /**
   * 获取提交历史
   * @param limit 限制数量
   * @param skip 跳过数量
   */
  public async fetchCommitHistory(limit: number = 50, skip: number = 0): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      const response = await httpClient.get('/git/log', {
        params: { limit, skip }
      });
      const commits: GitCommit[] = response.data?.commits || [];

      store.dispatch(setCommitHistory(commits));

      if (this.config.debug) {
        console.log('Git提交历史获取成功', commits);
      }
    } catch (error: any) {
      // 如果是 404 或 401 错误，静默处理
      if (error.response?.status === 404 || error.response?.status === 401) {
        if (this.config.debug) {
          console.warn(`Git 提交历史 API 不可用 (${error.response?.status})`);
        }
        // 设置默认空提交历史
        store.dispatch(setCommitHistory([]));
        return;
      }

      console.error('获取Git提交历史失败', error);
      this.emit(GitServiceEventType.ERROR, error);

      // 只在非 404/401 错误时显示错误消息
      if (error.response?.status !== 404 && error.response?.status !== 401) {
        message.error('获取Git提交历史失败');
      }

      // 设置默认空提交历史
      store.dispatch(setCommitHistory([]));
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }

  /**
   * 添加文件到暂存区
   * @param files 文件路径列表
   */
  public async addFiles(files: string[]): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      await httpClient.post('/git/add', { files });

      // 重新获取文件状态
      await this.fetchFiles();

      if (this.config.debug) {
        console.log('文件添加到暂存区成功', files);
      }
    } catch (error) {
      console.error('添加文件到暂存区失败', error);
      this.emit(GitServiceEventType.ERROR, error);
      message.error('添加文件到暂存区失败');
      throw error;
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }

  /**
   * 提交更改
   * @param commitMessage 提交消息
   */
  public async commit(commitMessage: string): Promise<void> {
    try {
      store.dispatch(setIsLoading(true));

      const response = await httpClient.post('/git/commit', { message: commitMessage });
      const commit: GitCommit = response.data?.commit;

      // 重新获取状态
      await this.fetchStatus();

      if (commit) {
        this.emit(GitServiceEventType.COMMIT_CREATED, commit);
      }

      if (this.config.debug) {
        console.log('提交成功', commit);
      }
    } catch (error: any) {
      console.error('提交失败', error);
      this.emit(GitServiceEventType.ERROR, error);

      // 只在非 404/401 错误时显示错误消息
      if (error.response?.status !== 404 && error.response?.status !== 401) {
        message.error('提交失败');
      }
      throw error;
    } finally {
      store.dispatch(setIsLoading(false));
    }
  }
}

// 导出Git服务类，不立即实例化
export default GitService;

// 导出获取Git服务实例的函数，延迟实例化
export const getGitService = () => GitService.getInstance();
