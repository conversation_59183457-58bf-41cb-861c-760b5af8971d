/**
 * 菜单栏组件测试
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import MenuBar from '../MenuBar';
import editorReducer, { TransformMode, TransformSpace, SnapMode } from '../../../store/editor/editorSlice';
import uiReducer, { ViewportMode, RenderMode } from '../../../store/ui/uiSlice';

// 创建测试用的store
const createTestStore = (_initialState = {}) => {
  return configureStore({
    reducer: {
      editor: editorReducer,
      ui: uiReducer,
    },
    preloadedState: {
      editor: {
        isLoading: false,
        error: null,
        activeCamera: null,
        selectedObject: null,
        selectedObjects: [],
        transformMode: TransformMode.TRANSLATE,
        transformSpace: TransformSpace.LOCAL,
        snapMode: SnapMode.DISABLED,
        gridSize: 1,
        showGrid: true,
        showAxes: true,
        showStats: false,
        undoStack: [],
        redoStack: [],
        isPlaying: false,
        viewportSize: { width: 0, height: 0 },
        sceneGraph: [],
        // ...initialState.editor, // 暂时注释掉，因为 initialState 没有 editor 属性
      },
      ui: {
        panels: [],
        dialogs: [],
        theme: 'dark',
        language: 'zh-CN',
        sidebarCollapsed: false,
        fullscreen: false,
        menuVisible: true,
        viewportMode: ViewportMode.SELECT,
        renderMode: RenderMode.TEXTURED,
        contextMenu: {
          isOpen: false,
          x: 0,
          y: 0,
          items: [],
        },
        notifications: [],
        layout: null,
        savedLayouts: {},
        activeLayout: 'default',
        // ...initialState.ui, // 暂时注释掉，因为 initialState 没有 ui 属性
      },
    },
  });
};

// 测试组件包装器
const TestWrapper: React.FC<{ children: React.ReactNode; store?: any }> = ({ 
  children, 
  store = createTestStore() 
}) => (
  <Provider store={store}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('MenuBar Component', () => {
  beforeEach(() => {
    // 模拟 ThreeRenderService
    jest.mock('../../../services/ThreeRenderService', () => ({
      getInstance: () => ({
        setTransformMode: jest.fn(),
        setTransformSpace: jest.fn(),
        resetCamera: jest.fn(),
        focusOnSelected: jest.fn(),
        getScene: () => ({}),
      }),
    }));
  });

  test('renders menubar with all main menu items', () => {
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 检查主要菜单项是否存在
    expect(screen.getByText(/文件/i)).toBeInTheDocument();
    expect(screen.getByText(/编辑/i)).toBeInTheDocument();
    expect(screen.getByText(/视图/i)).toBeInTheDocument();
    expect(screen.getByText(/工具/i)).toBeInTheDocument();
    expect(screen.getByText(/帮助/i)).toBeInTheDocument();
  });

  test('displays project path correctly', () => {
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 检查项目路径是否显示
    expect(screen.getByText('Demo Project /')).toBeInTheDocument();
  });

  test('displays publish button', () => {
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 检查发布按钮是否存在
    expect(screen.getByRole('button', { name: /editor\.publish/i })).toBeInTheDocument();
  });

  test('file menu dropdown works correctly', () => {
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 点击文件菜单
    const fileMenu = screen.getByText(/文件/i);
    fireEvent.click(fileMenu);

    // 检查文件菜单项是否出现
    // 注意：由于使用了Dropdown组件，菜单项可能需要特殊的测试方法
  });

  test('edit menu actions work correctly', () => {
    const store = createTestStore();
    render(
      <TestWrapper store={store}>
        <MenuBar />
      </TestWrapper>
    );

    // 这里可以测试编辑菜单的功能
    // 由于菜单项在下拉菜单中，需要先打开菜单再测试
  });

  test('view menu toggles work correctly', () => {
    const store = createTestStore({
      editor: { showGrid: true, showAxes: true }
    });
    
    render(
      <TestWrapper store={store}>
        <MenuBar />
      </TestWrapper>
    );

    // 测试视图菜单的切换功能
  });

  test('tools menu works correctly', () => {
    const store = createTestStore();
    render(
      <TestWrapper store={store}>
        <MenuBar />
      </TestWrapper>
    );

    // 测试工具菜单的功能
  });

  test('publish button click works', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
    
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 点击发布按钮
    const publishButton = screen.getByRole('button', { name: /editor\.publish/i });
    fireEvent.click(publishButton);

    // 检查是否调用了发布功能
    expect(consoleSpy).toHaveBeenCalledWith('Publish scene');
    
    consoleSpy.mockRestore();
  });

  test('user button is present', () => {
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 检查用户按钮是否存在
    const userButtons = screen.getAllByRole('button');
    const userButton = userButtons.find(button => 
      button.querySelector('.anticon-user')
    );
    expect(userButton).toBeInTheDocument();
  });

  test('responsive design hides project path on small screens', () => {
    // 这个测试需要模拟屏幕尺寸变化
    // 可以通过CSS媒体查询测试或者使用特定的测试工具
  });

  test('menu items have correct icons', () => {
    render(
      <TestWrapper>
        <MenuBar />
      </TestWrapper>
    );

    // 检查菜单项是否有正确的图标
    // 这需要检查每个菜单按钮是否包含预期的图标类名
  });
});
