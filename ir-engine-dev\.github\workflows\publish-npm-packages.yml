name: publish-npm

on:
  release:
    types: [created]

jobs:
  secrets-gate:
    runs-on: ubuntu-latest
    outputs:
      ok: ${{ steps.check-secrets.outputs.ok }}
    steps:
      - name: check for secrets needed to run workflows
        id: check-secrets
        run: |
          if [ ${{ secrets.PUBLISH_NPM_PACKAGES_ENABLED }} == 'true' ]; then
            echo "ok=enabled" >> $GITHUB_OUTPUT
          fi
  publish-npm:
    needs:
      - secrets-gate
    if: ${{ needs.secrets-gate.outputs.ok == 'enabled' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 22.x
          scope: '@ir-engine'
      - name: Config npm registry token
        run: npm config set '//npm.pkg.github.com/:_authToken' "${NPM_TOKEN}"
        env:
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Set git username
        run: git config user.name "<PERSON><PERSON> Bot"
      - name: Set git email
        run: git config user.email <EMAIL>
      - name: Set pr_branch_name environment variable
        run: echo pr_branch_name=version-increment-${{ github.event.release.tag_name }} >> $GITHUB_ENV
      - name: Switch to branch ${{ env.pr_branch_name }}
        run: git switch -c ${{ env.pr_branch_name }}
      - name: Lerna increment version
        run: npm run version-increment-no-tag ${{ github.event.release.tag_name }}
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
      - name: Move root package.json
        run: mv package.json package.json.altered
      - name: Install dependencies for running bump-project-versions
        run: npm install cross-env@7.0.3 ts-node@10.9.2 cli@1.0.1 app-root-path@3.0.0
      - name: Move root package.json back
        run: mv package.json.altered package.json
      - name: Bump default and template project version
        run: node scripts/bump-project-versions.js
      - name: Run git add
        run: git add .
      - name: Commit changes
        run: git commit -m ${{ github.event.release.tag_name }}
      - name: Push changes to new branch
        run: git push --no-verify origin ${{ env.pr_branch_name }}
      - name: Create version bump PR
        uses: repo-sync/pull-request@v2
        with:
          destination_branch: ${{ github.event.release.target_commitish }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          source_branch: ${{ env.pr_branch_name }}
          pr_title: ${{ github.event.release.tag_name }}
          pr_body: Bump version to ${{ github.event.release.tag_name }}
      - name: Publish to GitHub Packages
        run: npm run publish-github
        env:
          NPM_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{secrets.GITHUB_TOKEN}}
      - name: Move npmrc file for publishing to npm
        run: mv .npmrc .mpmrc
      - name: Run git add
        run: git add .
      - name: Commit changes
        run: git commit -m 'GH Packages'
      - name: Config npm registry token
        run: npm config set '//registry.npmjs.org/:_authToken' "${NPM_TOKEN}"
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Publish to npm
        run: npm run publish-npm
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}