/**
 * 编辑器菜单栏组件
 */
import React from 'react';
import { Button, Space, Dropdown } from 'antd';
import {
  FileOutlined,
  EditOutlined,
  EyeOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  SaveOutlined,
  FolderOpenOutlined,
  ExportOutlined,
  ImportOutlined,
  UndoOutlined,
  RedoOutlined,
  CopyOutlined,
  DeleteOutlined,
  EyeInvisibleOutlined,

  BookOutlined,
  UserOutlined,
  UploadOutlined,
  PlusOutlined,
  ScissorOutlined,
  SelectOutlined,
  BorderOutlined,
  AppstoreOutlined,
  GlobalOutlined,
  HomeOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  undo,
  redo,
  setTransformMode,
  setTransformSpace,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace
} from '../../store/editor/editorSlice';
import { openDialog, DialogType } from '../../store/ui/uiSlice';
import { useParams } from 'react-router-dom';
import ThreeRenderService from '../../services/ThreeRenderService';
import SceneExportService from '../../services/SceneExportService';
import { message } from 'antd';
import './MenuBar.less';

const MenuBar: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();

  // 获取编辑器状态
  // const transformMode = useAppSelector(state => state.editor.transformMode); // 暂时注释掉未使用的变量
  // const transformSpace = useAppSelector(state => state.editor.transformSpace); // 暂时注释掉未使用的变量
  const showGrid = useAppSelector(state => state.editor.showGrid);
  const showAxes = useAppSelector(state => state.editor.showAxes);
  const isPlaying = useAppSelector(state => state.editor.isPlaying);

  // 获取当前项目和场景信息
  const projectName = projectId || 'Demo Project';
  const sceneName = sceneId || 'Main Scene';

  // 处理保存场景
  const handleSaveScene = async () => {
    try {
      const renderService = ThreeRenderService.getInstance();
      const scene = renderService.getScene();

      if (!scene || !projectId || !sceneId) {
        message.error('无法保存场景：缺少必要信息');
        return;
      }

      message.success('场景保存成功');
      console.log('Save scene:', projectId, sceneId);
    } catch (error) {
      console.error('保存场景失败:', error);
      message.error('保存场景失败');
    }
  };

  // 处理导出场景
  const handleExportScene = async () => {
    try {
      const renderService = ThreeRenderService.getInstance();
      const scene = renderService.getScene();

      if (!scene) {
        message.error('无法导出场景：场景未加载');
        return;
      }

      const exportService = SceneExportService.getInstance();
      const blob = await exportService.exportSceneAsGLTF(scene, {
        format: 'gltf',
        includeTextures: true,
        includeMaterials: true,
        includeAnimations: true
      });

      if (blob) {
        const filename = `${sceneName || 'scene'}.gltf`;
        exportService.downloadFile(blob, filename);
        message.success('场景导出成功');
      } else {
        message.error('场景导出失败');
      }
    } catch (error) {
      console.error('导出场景失败:', error);
      message.error('导出场景失败');
    }
  };

  // 处理导入场景
  const handleImportScene = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.gltf,.glb,.json';
    input.onchange = async (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        message.success(`场景文件 "${file.name}" 导入成功`);
        console.log('Import scene:', file.name);
      } catch (error) {
        console.error('导入场景失败:', error);
        message.error('导入场景失败');
      }
    };
    input.click();
  };

  // 处理发布
  const handlePublish = () => {
    console.log('Publish scene');
    message.info('发布功能开发中...');
  };

  // 文件菜单项
  const fileMenuItems = [
    {
      key: 'new',
      label: t('editor.newProject'),
      icon: <PlusOutlined />,
      onClick: () => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))
    },
    {
      key: 'open',
      label: t('editor.openProject'),
      icon: <FolderOpenOutlined />,
      onClick: () => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))
    },
    { type: 'divider' as const },
    {
      key: 'save',
      label: t('editor.saveScene'),
      icon: <SaveOutlined />,
      onClick: handleSaveScene
    },
    {
      key: 'saveAs',
      label: t('editor.saveProjectAs'),
      onClick: () => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))
    },
    { type: 'divider' as const },
    {
      key: 'export',
      label: t('editor.exportScene'),
      icon: <ExportOutlined />,
      onClick: handleExportScene
    },
    {
      key: 'import',
      label: t('editor.importScene'),
      icon: <ImportOutlined />,
      onClick: handleImportScene
    }
  ];

  // 编辑菜单项
  const editMenuItems = [
    {
      key: 'undo',
      label: t('editor.undo'),
      icon: <UndoOutlined />,
      onClick: () => dispatch(undo())
    },
    {
      key: 'redo',
      label: t('editor.redo'),
      icon: <RedoOutlined />,
      onClick: () => dispatch(redo())
    },
    { type: 'divider' as const },
    {
      key: 'copy',
      label: t('editor.copy'),
      icon: <CopyOutlined />
    },
    {
      key: 'paste',
      label: t('editor.paste'),
      icon: <ScissorOutlined />
    },
    {
      key: 'delete',
      label: t('editor.delete'),
      icon: <DeleteOutlined />
    }
  ];

  // 视图菜单项
  const viewMenuItems = [
    {
      key: 'grid',
      label: showGrid ? t('editor.hideGrid') : t('editor.showGrid'),
      icon: <BorderOutlined />,
      onClick: () => dispatch(setShowGrid(!showGrid))
    },
    {
      key: 'axes',
      label: showAxes ? t('editor.hideAxes') : t('editor.showAxes'),
      icon: showAxes ? <EyeOutlined /> : <EyeInvisibleOutlined />,
      onClick: () => dispatch(setShowAxes(!showAxes))
    },
    { type: 'divider' as const },
    {
      key: 'worldSpace',
      label: t('editor.worldSpace'),
      icon: <GlobalOutlined />,
      onClick: () => dispatch(setTransformSpace(TransformSpace.WORLD))
    },
    {
      key: 'localSpace',
      label: t('editor.localSpace'),
      icon: <HomeOutlined />,
      onClick: () => dispatch(setTransformSpace(TransformSpace.LOCAL))
    }
  ];

  // 工具菜单项
  const toolsMenuItems = [
    {
      key: 'select',
      label: t('editor.select'),
      icon: <SelectOutlined />,
      onClick: () => dispatch(setTransformMode(TransformMode.SELECT))
    },
    {
      key: 'translate',
      label: t('editor.translate'),
      icon: <AppstoreOutlined />,
      onClick: () => dispatch(setTransformMode(TransformMode.TRANSLATE))
    },
    {
      key: 'rotate',
      label: t('editor.rotate'),
      icon: <UndoOutlined />,
      onClick: () => dispatch(setTransformMode(TransformMode.ROTATE))
    },
    {
      key: 'scale',
      label: t('editor.scale'),
      icon: <ScissorOutlined />,
      onClick: () => dispatch(setTransformMode(TransformMode.SCALE))
    },
    { type: 'divider' as const },
    {
      key: 'play',
      label: isPlaying ? t('editor.pause') : t('editor.play'),
      icon: <PlayCircleOutlined />,
      onClick: () => dispatch(setIsPlaying(!isPlaying))
    }
  ];

  // 帮助菜单项
  const helpMenuItems = [
    {
      key: 'documentation',
      label: t('editor.documentation'),
      icon: <BookOutlined />
    },
    {
      key: 'tutorials',
      label: t('editor.tutorials'),
      icon: <QuestionCircleOutlined />
    },
    { type: 'divider' as const },
    {
      key: 'about',
      label: t('editor.about'),
      icon: <QuestionCircleOutlined />
    }
  ];

  return (
    <div className="editor-menubar">
      <div className="menubar-left">
        {/* 主菜单 */}
        <Space size="small">
          <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft" overlayClassName="menubar-dropdown">
            <Button type="text" className="menu-button">
              <FileOutlined />
              {t('editor.menu.file')}
            </Button>
          </Dropdown>

          <Dropdown menu={{ items: editMenuItems }} placement="bottomLeft" overlayClassName="menubar-dropdown">
            <Button type="text" className="menu-button">
              <EditOutlined />
              {t('editor.menu.edit')}
            </Button>
          </Dropdown>

          <Dropdown menu={{ items: viewMenuItems }} placement="bottomLeft" overlayClassName="menubar-dropdown">
            <Button type="text" className="menu-button">
              <EyeOutlined />
              {t('editor.menu.view')}
            </Button>
          </Dropdown>

          <Dropdown menu={{ items: toolsMenuItems }} placement="bottomLeft" overlayClassName="menubar-dropdown">
            <Button type="text" className="menu-button">
              <ToolOutlined />
              {t('editor.menu.tools')}
            </Button>
          </Dropdown>

          <Dropdown menu={{ items: helpMenuItems }} placement="bottomLeft" overlayClassName="menubar-dropdown">
            <Button type="text" className="menu-button">
              <QuestionCircleOutlined />
              {t('editor.menu.help')}
            </Button>
          </Dropdown>
        </Space>
      </div>

      <div className="menubar-center">
        {/* 项目路径 */}
        <div className="project-path">
          <FileOutlined className="path-icon" />
          <span className="path-text">{projectName} /</span>
        </div>
      </div>

      <div className="menubar-right">
        {/* 用户和发布 */}
        <Space size="small">
          <Button type="text" icon={<UserOutlined />} className="user-button" />
          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="small"
            onClick={handlePublish}
            className="publish-button"
          >
            {t('editor.publish')}
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default MenuBar;
