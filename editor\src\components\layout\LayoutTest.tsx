/**
 * 布局测试组件
 * 用于验证根据图片要求修改的布局是否正确工作
 * 右侧面板区域采用左图3D场景的布局设计
 */
import React from 'react';
import { Card, Typography, Space, Tag } from 'antd';
// import { useTranslation } from 'react-i18next';

const { Title, Text } = Typography;

const LayoutTest: React.FC = () => {
  // const { t } = useTranslation(); // 暂时注释掉未使用的翻译函数

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>布局修改验证</Title>
      
      <Card title="布局变更说明" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" size="middle">
          <Text>
            <Tag color="blue">修改前</Tag>
            右侧面板区域采用垂直布局，所有面板垂直排列
          </Text>
          <Text>
            <Tag color="green">修改后</Tag>
            右侧面板区域采用水平布局，模拟左侧3D场景的布局结构
          </Text>
          <Text>
            <Tag color="orange">布局结构</Tag>
            右侧区域分为左右两个子区域，每个子区域内部采用垂直布局
          </Text>
        </Space>
      </Card>

      <Card title="具体变更内容">
        <Space direction="vertical" size="small">
          <Text>• 右侧面板区域从 <code>mode: 'vertical'</code> 改为 <code>mode: 'horizontal'</code></Text>
          <Text>• 右侧面板区域的 <code>size</code> 从 3 增加到 4，以容纳新的布局结构</Text>
          <Text>• 在右侧面板区域内创建两个子区域，每个子区域 <code>size: 2</code></Text>
          <Text>• 左侧子区域包含：层级面板、场景面板、材质面板</Text>
          <Text>• 右侧子区域包含：属性面板、检查器面板</Text>
          <Text>• 同时更新了所有预定义布局（default、debug、coding）以保持一致性</Text>
        </Space>
      </Card>

      <Card title="文件修改列表" style={{ marginTop: '20px' }}>
        <Space direction="vertical" size="small">
          <Text>1. <code>editor/src/store/ui/layoutSlice.ts</code> - 更新默认布局和预定义布局</Text>
          <Text>2. <code>editor/src/components/layout/EditorLayout.tsx</code> - 更新布局创建函数</Text>
          <Text>3. <code>editor/src/components/MainLayout.tsx</code> - 更新主布局配置</Text>
          <Text>4. <code>editor/src/i18n/locales/zh-CN.json</code> - 添加相关中文说明</Text>
        </Space>
      </Card>

      <Card title="预期效果" style={{ marginTop: '20px' }}>
        <Text>
          右侧面板区域现在采用了类似左侧3D场景的布局设计，
          将原本垂直排列的面板重新组织为水平分布的两个区域，
          每个区域内部仍然保持垂直布局，这样既保持了面板的功能性，
          又实现了与3D场景布局的视觉一致性。
        </Text>
      </Card>
    </div>
  );
};

export default LayoutTest;
