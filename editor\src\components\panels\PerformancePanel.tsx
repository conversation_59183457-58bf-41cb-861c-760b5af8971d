/**
 * 性能监控面板组件
 * 显示Three.js场景的性能统计和优化建议
 */
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Statistic, 
  Row, 
  Col, 
  Progress, 
  List, 
  Switch, 
  Divider,
  Alert,
  Tag,
  Space,

} from 'antd';
import {
  DashboardOutlined,
  ThunderboltOutlined,
  EyeOutlined,

  WarningOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next'; // 暂时注释掉未使用的导入
import ThreeRenderService from '../../services/ThreeRenderService';
import { ThreePerformanceStats, ThreeOptimizationSettings } from '../../services/ThreePerformanceService';

const PerformancePanel: React.FC = () => {
  // const { t } = useTranslation(); // 暂时注释掉未使用的翻译函数
  
  const [stats, setStats] = useState<ThreePerformanceStats | null>(null);
  const [settings, setSettings] = useState<ThreeOptimizationSettings | null>(null);
  const [recommendations, setRecommendations] = useState<string[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(true);

  // 初始化性能监控
  useEffect(() => {
    const renderService = ThreeRenderService.getInstance();
    const performanceService = renderService.getPerformanceService();
    
    if (performanceService) {
      // 获取初始设置
      setSettings(performanceService.getSettings());
      
      // 监听性能统计更新
      const handleStatsUpdate = (newStats: ThreePerformanceStats) => {
        if (isMonitoring) {
          setStats(newStats);
          setRecommendations(renderService.getPerformanceRecommendations());
        }
      };
      
      performanceService.addStatsUpdateListener(handleStatsUpdate);
      
      // 清理函数
      return () => {
        performanceService.removeStatsUpdateListener(handleStatsUpdate);
      };
    }
  }, [isMonitoring]);

  // 更新优化设置
  const updateOptimizationSetting = (key: keyof ThreeOptimizationSettings, value: any) => {
    const renderService = ThreeRenderService.getInstance();
    const performanceService = renderService.getPerformanceService();
    
    if (performanceService && settings) {
      const newSettings = { ...settings, [key]: value };
      performanceService.updateSettings(newSettings);
      setSettings(newSettings);
    }
  };

  // 获取性能等级颜色
  const getPerformanceColor = (fps: number) => {
    if (fps >= 50) return '#52c41a'; // 绿色 - 优秀
    if (fps >= 30) return '#faad14'; // 黄色 - 良好
    return '#ff4d4f'; // 红色 - 需要优化
  };

  // 获取性能等级文本
  const getPerformanceLevel = (fps: number) => {
    if (fps >= 50) return '优秀';
    if (fps >= 30) return '良好';
    return '需要优化';
  };

  // 获取内存使用状态
  const getMemoryStatus = (usage: number) => {
    if (usage < 50) return { color: '#52c41a', text: '正常' };
    if (usage < 100) return { color: '#faad14', text: '中等' };
    return { color: '#ff4d4f', text: '偏高' };
  };

  if (!stats || !settings) {
    return (
      <div style={{ padding: '16px', textAlign: 'center' }}>
        <Alert
          message="性能监控未启用"
          description="请确保Three.js渲染器已初始化"
          type="info"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '8px', height: '100%', overflow: 'auto' }}>
      {/* 监控开关 */}
      <div style={{ marginBottom: '16px', textAlign: 'right' }}>
        <Space>
          <span>性能监控:</span>
          <Switch
            checked={isMonitoring}
            onChange={setIsMonitoring}
            checkedChildren="开启"
            unCheckedChildren="关闭"
          />
        </Space>
      </div>

      {/* 性能统计 */}
      <Card title="性能统计" size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="FPS"
              value={stats.fps}
              suffix="fps"
              valueStyle={{ color: getPerformanceColor(stats.fps) }}
              prefix={<DashboardOutlined />}
            />
            <div style={{ marginTop: '4px' }}>
              <Tag color={getPerformanceColor(stats.fps)}>
                {getPerformanceLevel(stats.fps)}
              </Tag>
            </div>
          </Col>
          <Col span={12}>
            <Statistic
              title="帧时间"
              value={stats.frameTime.toFixed(2)}
              suffix="ms"
              prefix={<ThunderboltOutlined />}
            />
          </Col>
        </Row>
        
        <Divider />
        
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="绘制调用"
              value={stats.drawCalls}
              prefix={<EyeOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="三角形"
              value={stats.triangles}
              formatter={(value) => `${(Number(value) / 1000).toFixed(1)}K`}
            />
          </Col>
        </Row>
      </Card>

      {/* 内存使用 */}
      <Card title="内存使用" size="small" style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '8px' }}>
          <span>几何体: {stats.memoryUsage.geometries}</span>
          <span style={{ float: 'right' }}>纹理: {stats.memoryUsage.textures}</span>
        </div>
        <Progress
          percent={Math.min(100, (stats.memoryUsage.total / 200) * 100)}
          strokeColor={getMemoryStatus(stats.memoryUsage.total).color}
          format={() => getMemoryStatus(stats.memoryUsage.total).text}
        />
      </Card>

      {/* 优化设置 */}
      <Card title="优化设置" size="small" style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '8px' }}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <span>LOD优化</span>
            <Switch
              size="small"
              checked={settings.enableLOD}
              onChange={(checked) => updateOptimizationSetting('enableLOD', checked)}
            />
          </Space>
        </div>
        
        <div style={{ marginBottom: '8px' }}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <span>视锥剔除</span>
            <Switch
              size="small"
              checked={settings.enableFrustumCulling}
              onChange={(checked) => updateOptimizationSetting('enableFrustumCulling', checked)}
            />
          </Space>
        </div>
        
        <div style={{ marginBottom: '8px' }}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <span>实例化</span>
            <Switch
              size="small"
              checked={settings.enableInstancing}
              onChange={(checked) => updateOptimizationSetting('enableInstancing', checked)}
            />
          </Space>
        </div>
        
        <div style={{ marginBottom: '8px' }}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <span>批处理</span>
            <Switch
              size="small"
              checked={settings.enableBatching}
              onChange={(checked) => updateOptimizationSetting('enableBatching', checked)}
            />
          </Space>
        </div>
        
        <div>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <span>自动优化</span>
            <Switch
              size="small"
              checked={settings.autoOptimize}
              onChange={(checked) => updateOptimizationSetting('autoOptimize', checked)}
            />
          </Space>
        </div>
      </Card>

      {/* 性能建议 */}
      {recommendations.length > 0 && (
        <Card 
          title={
            <Space>
              <WarningOutlined style={{ color: '#faad14' }} />
              性能建议
            </Space>
          } 
          size="small"
        >
          <List
            size="small"
            dataSource={recommendations}
            renderItem={(item) => (
              <List.Item>
                <Space>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                  <span style={{ fontSize: '12px' }}>{item}</span>
                </Space>
              </List.Item>
            )}
          />
        </Card>
      )}

      {/* 性能良好提示 */}
      {recommendations.length === 0 && stats.fps >= 50 && (
        <Alert
          message="性能表现良好"
          description="当前场景运行流畅，无需额外优化"
          type="success"
          icon={<CheckCircleOutlined />}
          showIcon
        />
      )}
    </div>
  );
};

export default PerformancePanel;
