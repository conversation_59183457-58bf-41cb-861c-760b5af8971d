/**
 * DL (Digital Learning) Engine 项目配置文件
 * 适配重构后的项目结构
 */

export interface ProjectConfigInterface {
  name: string;
  version: string;
  engineVersion: string;
  description: string;
  thumbnail: string;
  scenes: SceneConfig[];
  assets: AssetConfig[];
  settings: ProjectSettings;
}

export interface SceneConfig {
  id: string;
  name: string;
  path: string;
  thumbnail: string;
  description?: string;
  isDefault?: boolean;
}

export interface AssetConfig {
  id: string;
  name: string;
  type: 'model' | 'texture' | 'audio' | 'video' | 'material' | 'prefab';
  path: string;
  thumbnail?: string;
  tags?: string[];
}

export interface ProjectSettings {
  rendering: {
    shadows: boolean;
    postProcessing: boolean;
    antiAliasing: boolean;
  };
  physics: {
    enabled: boolean;
    gravity: number;
  };
  audio: {
    enabled: boolean;
    volume: number;
  };
}

const config: ProjectConfigInterface = {
  name: 'dl-engine/default-project',
  version: '0.0.0',
  engineVersion: '1.0.0',
  description: 'The default project for DL (Digital Learning) Engine',
  thumbnail: '/static/DL_thumbnail.jpg',
  
  scenes: [
    {
      id: 'default',
      name: '默认场景',
      path: '/public/scenes/default.gltf',
      thumbnail: '/public/scenes/default.thumbnail.jpg',
      description: '基础演示场景，包含基本的几何体和光照',
      isDefault: true
    },
    {
      id: 'apartment',
      name: '公寓场景',
      path: '/public/scenes/apartment.gltf',
      thumbnail: '/public/scenes/apartment.thumbnail.jpg',
      description: '室内公寓场景，展示室内环境和家具'
    },
    {
      id: 'sky-station',
      name: '天空站',
      path: '/public/scenes/sky-station.gltf',
      thumbnail: '/public/scenes/sky-station.thumbnail.jpg',
      description: '科幻风格的天空站场景'
    },
    {
      id: 'sponza',
      name: 'Sponza 场景',
      path: '/public/scenes/sponza.gltf',
      thumbnail: '/public/scenes/sponza.thumbnail.jpg',
      description: '经典的Sponza测试场景，用于光照和渲染测试'
    }
  ],
  
  assets: [
    {
      id: 'sample-audio',
      name: '示例音频',
      type: 'audio',
      path: '/assets/SampleAudio.mp3',
      tags: ['demo', 'audio']
    },
    {
      id: 'sample-video',
      name: '示例视频',
      type: 'video',
      path: '/assets/SampleVideo.mp4',
      tags: ['demo', 'video']
    },
    {
      id: 'apartment-model',
      name: '公寓模型',
      type: 'model',
      path: '/assets/apartment.glb',
      tags: ['architecture', 'interior']
    },
    {
      id: 'platform-model',
      name: '平台模型',
      type: 'model',
      path: '/assets/platform.glb',
      tags: ['geometry', 'basic']
    }
  ],
  
  settings: {
    rendering: {
      shadows: true,
      postProcessing: true,
      antiAliasing: true
    },
    physics: {
      enabled: true,
      gravity: -9.81
    },
    audio: {
      enabled: true,
      volume: 0.8
    }
  }
};

export default config;
