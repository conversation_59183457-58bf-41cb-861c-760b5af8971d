/**
 * 安全的翻译Hook
 * 确保在i18n未初始化时不会出错
 */
import { useTranslation } from 'react-i18next';

export const useSafeTranslation = () => {
  const { t, i18n, ready } = useTranslation();

  const safeT = (key: string, defaultValue?: string) => {
    try {
      if (!i18n.isInitialized || !ready) {
        return defaultValue || key;
      }
      const result = t(key);
      return result === key && defaultValue ? defaultValue : result;
    } catch (error) {
      console.warn('Translation error for key:', key, error);
      return defaultValue || key;
    }
  };

  return {
    t: safeT,
    i18n,
    ready: i18n.isInitialized && ready
  };
};
