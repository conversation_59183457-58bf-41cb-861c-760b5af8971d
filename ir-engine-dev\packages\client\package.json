{"name": "@ir-engine/client", "version": "1.0.3", "private": true, "repository": {"type": "git", "url": "git://github.com/ir-engine/ir-engine.git"}, "engines": {"node": ">= 22.11.0"}, "npmClient": "npm", "scripts": {"check-errors": "tsc --noemit && npx cycle-import-check src", "dev": "cross-env APP_ENV=development NODE_OPTIONS=--max_old_space_size=20480 vite", "build": "cross-env NODE_OPTIONS=--max_old_space_size=10240 vite build", "preview": "cross-env NODE_OPTIONS=--max_old_space_size=6144 vite preview", "start": "ts-node server.js", "validate": "npm run test", "test": "exit 0", "local": "npm run localbuild && npm run localstart", "localbuild": "cross-env APP_ENV=production VITE_LOCAL_BUILD=true npm run build && rm -rf ../server/upload/client && cp -r ./dist ../server/upload/client", "localstart": "cross-env APP_ENV=production VITE_LOCAL_BUILD=true npm run start"}, "resolutions": {"@types/react": "18.2.0", "react": "18.2.0"}, "peerDependencies": {"@types/react": "18.2.0", "react": "18.2.0"}, "dependencies": {"@ir-engine/client-core": "^1.0.3", "@ir-engine/common": "^1.0.3", "@ir-engine/editor": "^1.0.3", "@ir-engine/engine": "^1.0.3", "@ir-engine/hyperflux": "^1.0.3", "@ir-engine/projects": "^1.0.3", "@ir-engine/ui": "^1.0.3", "@originjs/vite-plugin-commonjs": "^1.0.3", "app-root-path": "3.1.0", "cli": "1.0.1", "history": "^5.3.0", "i18next": "21.6.16", "i18next-browser-languagedetector": "6.1.3", "knex": "2.4.2", "koa": "2.14.2", "koa-body": "6.0.1", "koa-qs": "3.0.0", "koa-send": "5.0.1", "koa-static": "5.0.0", "lodash": "4.17.21", "moment": "2.29.4", "notistack": "^3.0.1", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "11.16.6", "react-icons": "5.0.1", "react-json-tree": "^0.18.0", "react-router-dom": "6.9.0", "sass": "1.59.3", "tailwind-merge": "^1.13.2", "tailwindcss": "^3.3.2", "ts-node": "10.9.1", "vite": "5.4.8", "vite-plugin-compression2": "1.3.0", "vite-plugin-ejs": "^1.6.4", "vite-plugin-node-polyfills": "^0.9.0", "vite-plugin-pwa": "^0.14.7", "vite-plugin-svgr": "4.1.0"}, "devDependencies": {"@types/node": "18.15.5", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "autoprefixer": "^10.4.14", "cli": "1.0.1", "config": "3.3.9", "cross-env": "7.0.3", "node-fetch": "2.6.9", "postcss": "^8.4.23", "trace-unhandled": "2.0.1"}, "license": "ISC"}