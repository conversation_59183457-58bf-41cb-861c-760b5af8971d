/**
 * i18n调试页面
 */
import React from 'react';
import { Card, Typography, Space, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { useSafeTranslation } from '../hooks/useSafeTranslation';

const { Title, Text } = Typography;

export const I18nDebugPage: React.FC = () => {
  const { t: unsafeT, i18n } = useTranslation();
  const { t: safeT, ready } = useSafeTranslation();

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>i18n调试页面</Title>
      
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="i18n状态">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text>初始化状态: {i18n.isInitialized ? '已初始化' : '未初始化'}</Text>
            <Text>当前语言: {i18n.language}</Text>
            <Text>Ready状态: {ready ? '就绪' : '未就绪'}</Text>
            <Text>可用语言: {i18n.languages.join(', ')}</Text>
          </Space>
        </Card>

        <Card title="翻译测试">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>unsafe t('common.loading'): </Text>
              <Text>{unsafeT('common.loading')}</Text>
            </div>
            <div>
              <Text strong>safe t('common.loading'): </Text>
              <Text>{safeT('common.loading')}</Text>
            </div>
            <div>
              <Text strong>safe t('auth.loginTitle'): </Text>
              <Text>{safeT('auth.loginTitle')}</Text>
            </div>
            <div>
              <Text strong>safe t('projects.title'): </Text>
              <Text>{safeT('projects.title')}</Text>
            </div>
          </Space>
        </Card>

        <Card title="操作">
          <Space>
            <Button onClick={() => i18n.changeLanguage('zh-CN')}>
              切换到中文
            </Button>
            <Button onClick={() => i18n.changeLanguage('en-US')}>
              切换到英文
            </Button>
            <Button onClick={() => window.location.reload()}>
              刷新页面
            </Button>
          </Space>
        </Card>
      </Space>
    </div>
  );
};
