name: publish-gh-container
on:
  release:
    types: []
jobs:
  secrets-gate:
    runs-on: ubuntu-latest
    outputs:
      ok: ${{ steps.check-secrets.outputs.ok }}
    steps:
      - name: check for secrets needed to run workflows
        id: check-secrets
        run: |
          if [ ${{ secrets.PUBLISH_GH_CONTAINER_ENABLED }} == 'true' ]; then
            echo "ok=enabled" >> $GITHUB_OUTPUT
          fi
  dev-deploy:
    needs:
      - secrets-gate
    if: ${{ needs.secrets-gate.outputs.ok == 'enabled' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22.x
      - name: Setup AWS
        run: scripts/setup_aws.sh $EKS_AWS_ACCESS_KEY_ID $EKS_AWS_ACCESS_KEY_SECRET $STORAGE_REGION $CLUSTER_NAME
        env:
          EKS_AWS_ACCESS_KEY_ID: ${{ secrets.EKS_AWS_ACCESS_KEY_ID }}
          E<PERSON>_AWS_ACCESS_KEY_SECRET: ${{ secrets.EKS_AWS_ACCESS_KEY_SECRET }}
          STORAGE_REGION: ${{ secrets.STORAGE_REGION }}
          CLUSTER_NAME: ${{ secrets.CLUSTER_NAME }}
      - name: Build Docker Image
        run: bash scripts/build_docker.sh dev $PRIVATE_REPO #STORAGE_REGION
        env:
          REPO_NAME: ${{ secrets.DEV_REPO_NAME }}
          STORAGE_REGION: ${{ secrets.STORAGE_REGION }}
          REPO_URL: ${{ secrets.DEV_REPO_URL }}
          REPO_PROVIDER: ${{ secrets.REPO_PROVIDER }}
          PRIVATE_REPO: ${{ secrets.PRIVATE_REPO }}
      - name: Publish to GitHub Container Packages
        run: bash scripts/publish_gh_container.sh ${{ github.event.release.tag_name }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}