/**
 * 性能优化面板组件
 * 集成所有性能优化工具
 */
import React, { useState } from 'react';
import { Tabs, Card, Button, Space, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import {
  ThunderboltOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  EyeOutlined,
  PartitionOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import DynamicLODPanel from './DynamicLODPanel';
import OcclusionCullingPanel from './OcclusionCullingPanel';
import InstancedRenderingPanel from './InstancedRenderingPanel';
import ChunkedSceneLoadingPanel from './ChunkedSceneLoadingPanel';
import EngineService from '../../services/EngineService';

// 定义性能瓶颈类型枚举
enum PerformanceBottleneck {
  HIGH_RENDER_TIME = '高渲染时间',
  OVERDRAW = '过度绘制',
  GEOMETRY_COMPLEXITY = '几何复杂度',
  MEMORY_USAGE = '内存使用'
}
import { PerformanceMonitor } from '../../libs/dl-engine';
import './PerformanceOptimizationPanel.less';

/**
 * 性能优化面板组件
 */
const PerformanceOptimizationPanel: React.FC = () => {
  const { t } = useTranslation();
  
  // 状态
  const [activeTab, setActiveTab] = useState('lod');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  // 分析场景性能
  const analyzeScenePerformance = async () => {
    setIsAnalyzing(true);

    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }

      // 获取性能监控器
      const performanceMonitor = PerformanceMonitor.getInstance();

      // 开始性能监控
      performanceMonitor.start();

      // 等待一段时间收集数据
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 获取性能报告
      const report = performanceMonitor.getReport();

      // 根据性能报告切换到相应的标签页
      if (report.bottlenecks.includes(PerformanceBottleneck.HIGH_RENDER_TIME as any)) {
        setActiveTab('instancing');
      } else if (report.bottlenecks.includes(PerformanceBottleneck.OVERDRAW as any)) {
        setActiveTab('occlusion');
      } else if (report.bottlenecks.includes(PerformanceBottleneck.GEOMETRY_COMPLEXITY as any)) {
        setActiveTab('lod');
      } else if (report.bottlenecks.includes(PerformanceBottleneck.MEMORY_USAGE as any)) {
        setActiveTab('chunking');
      }

      // 停止监控
      performanceMonitor.stop();
    } catch (error) {
      console.error('分析场景性能失败:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // 应用自动优化
  const applyAutoOptimization = async () => {
    try {
      // 获取引擎
      const engine = EngineService.getEngine();
      if (!engine) {
        throw new Error('引擎未初始化');
      }
      
      // 获取性能优化服务
      const optimizationService = (engine as any).getOptimizationService?.();
      if (!optimizationService) {
        throw new Error('性能优化服务未初始化');
      }
      
      // 应用自动优化
      await optimizationService.applyAutoOptimization();
    } catch (error) {
      console.error('应用自动优化失败:', error);
    }
  };
  
  return (
    <div className="performance-optimization-panel">
      <Card 
        title={
          <Space>
            <ThunderboltOutlined />
            <span>{t('optimization.title')}</span>
          </Space>
        }
        extra={
          <Space>
            <Tooltip title={t('optimization.analyzePerformance')}>
              <Button 
                icon={<BarChartOutlined />} 
                loading={isAnalyzing}
                onClick={analyzeScenePerformance}
              >
                {t('optimization.analyze')}
              </Button>
            </Tooltip>
            <Tooltip title={t('optimization.applyAutoOptimization')}>
              <Button 
                type="primary" 
                icon={<ThunderboltOutlined />}
                onClick={applyAutoOptimization}
              >
                {t('optimization.autoOptimize')}
              </Button>
            </Tooltip>
          </Space>
        }
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'lod',
              label: (
                <span>
                  <SettingOutlined />
                  {t('optimization.dynamicLOD.tabTitle')}
                </span>
              ),
              children: <DynamicLODPanel />,
            },
            {
              key: 'occlusion',
              label: (
                <span>
                  <EyeOutlined />
                  {t('optimization.occlusionCulling.tabTitle')}
                </span>
              ),
              children: <OcclusionCullingPanel />,
            },
            {
              key: 'instancing',
              label: (
                <span>
                  <AppstoreOutlined />
                  {t('optimization.instancedRendering.tabTitle')}
                </span>
              ),
              children: <InstancedRenderingPanel />,
            },
            {
              key: 'chunking',
              label: (
                <span>
                  <PartitionOutlined />
                  {t('optimization.chunkedSceneLoading.tabTitle')}
                </span>
              ),
              children: <ChunkedSceneLoadingPanel />,
            },
          ]}
        />
      </Card>
    </div>
  );
};

export default PerformanceOptimizationPanel;
