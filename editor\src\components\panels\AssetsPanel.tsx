/**
 * 资产面板组件
 */
import React, { useState } from 'react';
import { Button, Dropdown, Tabs, Card, List, Empty, Tag, Space, Tooltip, Modal, Progress, Input, Upload } from 'antd';
import {
  SearchOutlined,
  FolderOutlined,
  FolderAddOutlined,
  UploadOutlined,
  DeleteOutlined,
  EditOutlined,
  FileImageOutlined,
  FileUnknownOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileExcelOutlined,
  FileMarkdownOutlined,
  AppstoreOutlined,
  BarsOutlined,
  SortAscendingOutlined,
  } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import { setSelectedAssets, setCurrentFolder, AssetType } from '../../store/asset/assetSlice';

const { Search } = Input;
const { TabPane } = Tabs;
const { Dragger } = Upload;

// 定义资产接口
interface Asset {
  id: string;
  name: string;
  type: AssetType;
  url: string;
  thumbnail?: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

// 定义文件夹接口
interface Folder {
  id: string;
  name: string;
  projectId: string;
  createdAt: string;
  updatedAt: string;
}

const AssetsPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  
  const { selectedAssets, isUploading, uploadProgress } = useAppSelector(
    (state) => state.asset as any // 临时类型断言，因为 asset 状态类型未知
  );
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'date'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);

  // 使用sortBy和sortOrder（避免未使用警告）
  console.debug('Current sort settings:', { sortBy, sortOrder });
  
  // 示例数据，实际应该从API获取
  const sampleFolders: Folder[] = [
    { id: 'models', name: 'Models', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
    { id: 'textures', name: 'Textures', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
    { id: 'materials', name: 'Materials', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
    { id: 'audio', name: 'Audio', projectId: '1', createdAt: '2023-01-01', updatedAt: '2023-01-01' },
  ];

  const sampleAssets: Asset[] = [
    {
      id: '1',
      name: 'Cube.glb',
      type: AssetType.MODEL,
      url: '/assets/models/cube.glb',
      thumbnail: '/assets/thumbnails/cube.png',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01'
    },
    {
      id: '2',
      name: 'Grass.jpg',
      type: AssetType.TEXTURE,
      url: '/assets/textures/grass.jpg',
      thumbnail: '/assets/thumbnails/grass.jpg',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01'
    },
    {
      id: '3',
      name: 'Metal.material',
      type: AssetType.MATERIAL,
      url: '/assets/materials/metal.material',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01'
    },
    {
      id: '4',
      name: 'Footstep.mp3',
      type: AssetType.AUDIO,
      url: '/assets/audio/footstep.mp3',
      projectId: '1',
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01'
    }
  ];
  
  // 处理搜索
  const handleSearch = (value: string) => {
    // TODO: 实现搜索功能
    console.log('搜索:', value);
  };
  
  // 处理文件夹点击
  const handleFolderClick = (folder: Folder) => {
    dispatch(setCurrentFolder(folder));
  };

  // 处理资产选择
  const handleAssetSelect = (asset: Asset) => {
    dispatch(setSelectedAssets([asset]));
  };

  // 处理资产双击
  const handleAssetDoubleClick = (asset: Asset) => {
    // 根据资产类型执行不同操作
    switch (asset.type) {
      case AssetType.MODEL:
        // 添加模型到场景
        break;
      case AssetType.TEXTURE:
        // 打开纹理预览
        break;
      case AssetType.MATERIAL:
        // 打开材质编辑器
        break;
      case AssetType.AUDIO:
        // 播放音频
        break;
      default:
        // 默认操作
        break;
    }
  };
  
  // 获取资产图标
  const getAssetIcon = (type: AssetType) => {
    switch (type) {
      case AssetType.MODEL:
        return <FileUnknownOutlined />;
      case AssetType.TEXTURE:
        return <FileImageOutlined />;
      case AssetType.MATERIAL:
        return <FilePdfOutlined />;
      case AssetType.AUDIO:
        return <FileTextOutlined />;
      case AssetType.SCRIPT:
        return <FileMarkdownOutlined />;
      case AssetType.PREFAB:
        return <FileZipOutlined />;
      case AssetType.SCENE:
        return <FileExcelOutlined />;
      default:
        return <FileUnknownOutlined />;
    }
  };
  
  // 获取资产类型标签颜色
  const getAssetTypeColor = (type: AssetType) => {
    switch (type) {
      case AssetType.MODEL:
        return 'blue';
      case AssetType.TEXTURE:
        return 'green';
      case AssetType.MATERIAL:
        return 'purple';
      case AssetType.AUDIO:
        return 'orange';
      case AssetType.SCRIPT:
        return 'red';
      case AssetType.PREFAB:
        return 'cyan';
      case AssetType.SCENE:
        return 'magenta';
      default:
        return 'default';
    }
  };
  
  // 文件夹菜单项
  const folderMenuItems = [
    {
      key: 'newFolder',
      icon: <FolderAddOutlined />,
      label: t('editor.newFolder'),
    },
    {
      key: 'upload',
      icon: <UploadOutlined />,
      label: t('editor.uploadAssets'),
      onClick: () => setUploadModalVisible(true),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'refresh',
      icon: <SearchOutlined />,
      label: t('editor.refresh'),
    },
  ];

  // 排序菜单项
  const sortMenuItems = [
    {
      key: 'name',
      label: t('editor.sortByName'),
      onClick: () => setSortBy('name'),
    },
    {
      key: 'type',
      label: t('editor.sortByType'),
      onClick: () => setSortBy('type'),
    },
    {
      key: 'date',
      label: t('editor.sortByDate'),
      onClick: () => setSortBy('date'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'asc',
      label: t('editor.ascending'),
      onClick: () => setSortOrder('asc'),
    },
    {
      key: 'desc',
      label: t('editor.descending'),
      onClick: () => setSortOrder('desc'),
    },
  ];
  
  // 渲染网格视图
  const renderGridView = () => {
    return (
      <div style={{ padding: '8px' }}>
        <List
          grid={{ gutter: 16, column: 4 }}
          dataSource={[...sampleFolders, ...sampleAssets]}
          renderItem={(item) => {
            if ('type' in item) {
              // 资产
              const asset = item as Asset;
              return (
                <List.Item>
                  <Card
                    hoverable
                    size="small"
                    cover={
                      asset.thumbnail ? (
                        <img alt={asset.name} src={asset.thumbnail} style={{ height: 80, objectFit: 'cover' }} />
                      ) : (
                        <div style={{ height: 80, display: 'flex', justifyContent: 'center', alignItems: 'center', background: '#f0f0f0' }}>
                          {getAssetIcon(asset.type)}
                        </div>
                      )
                    }
                    onClick={() => handleAssetSelect(asset)}
                    onDoubleClick={() => handleAssetDoubleClick(asset)}
                    style={{
                      border: selectedAssets.some((a: any) => a.id === asset.id) ? '2px solid #1890ff' : '1px solid #f0f0f0'}}
                  >
                    <Card.Meta
                      title={asset.name}
                      description={<Tag color={getAssetTypeColor(asset.type)}>{asset.type}</Tag>}
                    />
                  </Card>
                </List.Item>
              );
            } else {
              // 文件夹
              const folder = item as Folder;
              return (
                <List.Item>
                  <Card
                    hoverable
                    size="small"
                    onClick={() => handleFolderClick(folder)}
                    style={{ border: '1px solid #f0f0f0' }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <FolderOutlined style={{ fontSize: 24, marginRight: 8, color: '#faad14' }} />
                      <div>{folder.name}</div>
                    </div>
                  </Card>
                </List.Item>
              );
            }
          }}
        />
      </div>
    );
  };
  
  // 渲染列表视图
  const renderListView = () => {
    return (
      <List
        itemLayout="horizontal"
        dataSource={[...sampleFolders, ...sampleAssets]}
        renderItem={(item) => {
          if ('type' in item) {
            // 资产
            const asset = item as Asset;
            return (
              <List.Item
                actions={[
                  <Button key="edit" type="text" icon={<EditOutlined />} />,
                  <Button key="delete" type="text" icon={<DeleteOutlined />} />,
                ]}
                onClick={() => handleAssetSelect(asset)}
                onDoubleClick={() => handleAssetDoubleClick(asset)}
                style={{
                  background: selectedAssets.some((a: any) => a.id === asset.id) ? 'rgba(24, 144, 255, 0.1)' : 'transparent'}}
              >
                <List.Item.Meta
                  avatar={getAssetIcon(asset.type)}
                  title={asset.name}
                  description={<Tag color={getAssetTypeColor(asset.type)}>{asset.type}</Tag>}
                />
              </List.Item>
            );
          } else {
            // 文件夹
            const folder = item as Folder;
            return (
              <List.Item
                actions={[
                  <Button key="edit" type="text" icon={<EditOutlined />} />,
                  <Button key="delete" type="text" icon={<DeleteOutlined />} />,
                ]}
                onClick={() => handleFolderClick(folder)}
              >
                <List.Item.Meta
                  avatar={<FolderOutlined style={{ color: '#faad14' }} />}
                  title={folder.name}
                  description={t('editor.folder') as string}
                />
              </List.Item>
            );
          }
        }}
      />
    );
  };
  
  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Search
          placeholder={t('editor.searchAssets') as string}
          allowClear
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>
      
      <div style={{ display: 'flex', justifyContent: 'space-between', padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Space>
          <Dropdown menu={{ items: folderMenuItems }} placement="bottomLeft">
            <Button icon={<FolderAddOutlined />} size="small">
              {t('editor.new')}
            </Button>
          </Dropdown>
          <Button icon={<UploadOutlined />} size="small" onClick={() => setUploadModalVisible(true)}>
            {t('editor.upload')}
          </Button>
        </Space>
        
        <Space>
          <Tooltip title={t('editor.gridView')}>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              size="small"
              onClick={() => setViewMode('grid')}
            />
          </Tooltip>
          <Tooltip title={t('editor.listView')}>
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              size="small"
              onClick={() => setViewMode('list')}
            />
          </Tooltip>
          <Dropdown menu={{ items: sortMenuItems }} placement="bottomRight">
            <Button icon={<SortAscendingOutlined />} size="small" />
          </Dropdown>
        </Space>
      </div>
      
      <div style={{ flex: 1, overflow: 'auto' }}>
        <Tabs defaultActiveKey="all">
          <TabPane tab={t('editor.allAssets')} key="all">
            {viewMode === 'grid' ? renderGridView() : renderListView()}
          </TabPane>
          <TabPane tab={t('editor.models')} key="models">
            <Empty description={t('editor.noModels')} />
          </TabPane>
          <TabPane tab={t('editor.textures')} key="textures">
            <Empty description={t('editor.noTextures')} />
          </TabPane>
          <TabPane tab={t('editor.materials')} key="materials">
            <Empty description={t('editor.noMaterials')} />
          </TabPane>
        </Tabs>
      </div>
      
      <Modal
        title={t('editor.uploadAssets')}
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Dragger multiple>
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">{t('editor.clickOrDragToUpload')}</p>
          <p className="ant-upload-hint">{t('editor.supportedFormats')}</p>
        </Dragger>
        
        {isUploading && (
          <div style={{ marginTop: 16 }}>
            <Progress percent={uploadProgress} status="active" />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AssetsPanel;
