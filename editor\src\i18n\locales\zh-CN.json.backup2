{"common": {"save": "保存", "cancel": "取消", "confirm": "确认", "delete": "删除", "edit": "编辑", "create": "创建", "update": "更新", "search": "搜索", "loading": "加载", "success": "成功", "error": "错误", "warning": "警告", "info": "信息", "yes": "是", "no": "否", "back": "返回", "next": "下一步", "previous": "上一步", "submit": "提交", "reset": "重置", "upload": "上传", "download": "下载", "import": "导入", "export": "导出", "add": "添加", "remove": "移除", "copy": "复制", "paste": "粘贴", "cut": "剪切", "undo": "撤销", "redo": "重做", "select": "选择", "selectAll": "全选", "deselect": "取消选择", "rename": "重命名", "duplicate": "复制", "settings": "设置", "preferences": "首选项", "help": "帮助", "about": "关于", "profile": "个人资料", "noNotifications": "暂无通知", "github": "GitHub", "guest": "访客", "logout": "退出登录", "login": "登录", "register": "注册", "username": "用户名", "password": "密码", "email": "邮箱", "phone": "电话", "address": "地址", "description": "描述", "name": "名称", "title": "标题", "content": "内容", "type": "类型", "status": "状态", "date": "日期", "time": "时间", "size": "大小", "color": "颜色", "position": "位置", "rotation": "旋转", "scale": "缩放", "width": "宽度", "height": "高度", "depth": "深度", "radius": "半径", "opacity": "不透明度", "visible": "可见", "hidden": "隐藏", "enabled": "启用", "disabled": "禁用", "locked": "锁定", "unlocked": "解锁", "public": "公开", "private": "私有", "shared": "共享", "owner": "所有者", "creator": "创建者", "createdAt": "创建时间", "updatedAt": "更新时间", "version": "版本", "language": "语言", "theme": "主题", "light": "光源", "dark": "暗色", "auto": "自动", "file": "文件", "folder": "文件夹", "project": "项目", "projects": "项目", "editor": "编辑器", "scene": "场景", "asset": "资产", "material": "材质", "texture": "纹理", "model": "模型", "animation": "动画", "audio": "音频", "video": "视频", "script": "脚本", "shader": "着色器", "particle": "粒子", "physics": "物理", "camera": "相机", "environment": "环境", "skybox": "天空盒", "terrain": "地形", "water": "水面", "vegetation": "植被", "character": "角色", "vehicle": "载具", "weapon": "武器", "effect": "特效", "ui": "界面", "hud": "HUD", "menu": "菜单", "button": "按钮", "input": "输入", "slider": "滑块", "checkbox": "复选框", "radioButton": "单选按钮", "dropdown": "下拉菜单", "panel": "面板", "window": "窗口", "dialog": "对话框", "tooltip": "提示", "notification": "通知", "message": "消息", "alert": "警告", "prompt": "提示", "progress": "进度"}, "menu": {"file": "文件", "edit": "编辑", "view": "视图", "window": "窗口", "help": "帮助", "tutorials": "教程", "documentation": "文档", "about": "关于", "project": "项目", "settings": "设置"}, "editor": {"title": "DL（Digital Learning）引擎编辑器", "file": "文件", "edit": "编辑", "view": "视图", "tools": "工具", "help": "帮助", "publish": "发布", "menu": {"file": "文件", "edit": "编辑", "view": "视图", "tools": "工具", "help": "帮助"}, "collaborationView": "协作视图", "newProject": "新建项目", "openProject": "打开项目", "saveProject": "保存项目", "saveProjectAs": "项目另存为", "closeProject": "关闭项目", "importAsset": "导入资产", "exportScene": "导出场景", "publishProject": "发布项目", "saveScene": "保存场景", "importScene": "导入场景", "undo": "撤销", "redo": "重做", "copy": "复制", "paste": "粘贴", "delete": "删除", "showGrid": "显示网格", "hideGrid": "隐藏网格", "showAxes": "显示坐标轴", "hideAxes": "隐藏坐标轴", "worldSpace": "世界空间", "localSpace": "本地空间", "select": "选择工具", "translate": "平移", "rotate": "旋转", "scale": "缩放", "play": "播放", "pause": "暂停", "documentation": "文档", "tutorials": "教程", "about": "关于", "confirmDelete": "确认删除", "deleteObjectConfirm": "确定要删除对象 \"{{name}}\" 吗？", "objectDeleted": "对象已删除", "newObject": "新对象", "cube": "立方体", "sphere": "球体", "plane": "平面", "directionalLight": "平行光", "pointLight": "点光源", "spotLight": "聚光灯", "ambientLight": "环境光", "camera": "相机", "objectCreated": "对象已创建", "rename": "重命名", "duplicate": "复制", "cut": "剪切", "emptyObject": "空对象", "searchObjects": "搜索对象", "add": "添加", "noObjects": "暂无对象", "noObjectSelected": "未选择对象", "multipleObjectsSelected": "已选择 {count} 个对象", "transform": "变换", "position": "位置", "rotation": "旋转", "components": "组件", "basic": "基本信息", "name": "名称", "tag": "标签", "layer": "图层", "static": "静态", "visible": "可见", "meshRenderer": "网格渲染器", "material": "材质", "castShadows": "投射阴影", "receiveShadows": "接收阴影", "light": "光照", "color": "颜色", "intensity": "强度", "shadowResolution": "阴影分辨率", "addComponent": "添加组件", "materials": "材质", "materialsTabContent": "材质编辑功能开发中...", "viewport": {"select": "选择", "translate": "移动", "rotate": "旋转", "scale": "缩放", "focus": "聚焦", "screenshot": "截图", "fullscreen": "全屏", "renderModes": {"solid": "实体", "wireframe": "线框", "textured": "纹理"}, "selected": "已选择", "noSelection": "未选择"}, "hierarchy": "层级", "assets": {"upload": "上传资产", "search": "搜索资产", "all": "全部", "models": "模型", "textures": "纹理", "audio": "音频", "edit": "编辑", "download": "下载", "delete": "删除", "cancel": "取消", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除该资产吗？", "deleteSuccess": "删除成功", "uploadText": "将文件拖到此处，或点击上传", "uploadHint": "支持一次选择多个文件"}, "scene": {"add": "添加实体", "search": "搜索实体", "duplicate": "复制", "lock": "锁定", "unlock": "解锁", "delete": "删除"}, "inspector": "检查器", "properties": "属性", "console": "控制台", "visualScript": "可视化脚本", "hierarchyView": "层级视图", "inspectorView": "检查器视图", "assetView": "资产视图", "sceneView": "场景视图", "consoleView": "控制台视图", "animationView": "动画视图", "physicsView": "物理视图", "particleView": "粒子视图", "interactionView": "交互视图", "avatarView": "角色视图", "mocapView": "动作捕捉视图", "unknownView": "未知视图", "panel": {"close": "关闭", "maximize": "最大化", "restore": "还原", "pin": "固定", "unpin": "取消固定", "hide": "隐藏", "show": "显示", "unknownContent": "未知内容"}, "panels": {"hierarchy": "层级面板", "inspector": "属性面板", "assets": "资源面板", "scene": "场景视图", "console": "控制台", "animation": "动画面板", "physics": "物理面板", "particle": "粒子面板", "layers": "图层面板", "instances": "实例面板"}, "shaded": "着色", "wireframe": "线框", "rendered": "渲染", "perspective": "透视", "orthographic": "正交", "viewMode": "视图模式", "toggleGrid": "切换网格", "toggleAxes": "切换坐标轴", "performanceStats": "性能统计", "viewportSettings": "视口设置", "exitFullscreen": "退出全屏", "fullscreen": "全屏", "resetView": "重置视图", "focusSelected": "聚焦选中", "settings": "设置", "loadingEngine": "引擎加载中...", "instances": {"addTemplate": "添加模板", "addInstance": "添加实例", "edit": "编辑实例", "delete": "删除实例", "parameters": "参数设置", "search": "搜索模板和实例", "templates": "模板", "instances": "实例", "templateName": "模板名称", "templateNameRequired": "请输入模板名称", "templateDescription": "模板描述", "instanceName": "实例名称", "instanceNameRequired": "请输入实例名称", "template": "模板", "templateRequired": "请选择模板", "position": "位置", "rotation": "旋转", "scale": "缩放", "createTemplate": "创建模板", "createInstance": "创建实例", "editInstance": "编辑实例", "confirmDeleteTemplate": "确认删除模板", "confirmDeleteTemplateContent": "确定要删除模板 \"{name}\" 吗？", "confirmDeleteInstance": "确认删除实例", "confirmDeleteInstanceContent": "确定要删除实例 \"{name}\" 吗？", "cannotDeleteTemplate": "无法删除模板", "templateInUse": "该模板正在被实例使用，无法删除", "wallColor": "墙壁颜色", "floorColor": "地板颜色", "ceilingColor": "天花板颜色", "windowCount": "窗户数量", "deskColor": "桌子颜色", "chairColor": "椅子颜色", "hasComputer": "是否有电脑"}, "layers": {"add": "添加图层", "addGroup": "添加组", "edit": "编辑图层", "delete": "删除图层", "moveUp": "上移", "moveDown": "下移", "search": "搜索图层", "name": "图层名称", "nameRequired": "请输入图层名称", "groupName": "组名称", "groupNameRequired": "请输入组名称", "color": "图层颜色", "tags": "标签", "parent": "父图层", "selectParent": "选择父图层", "createLayer": "创建图层", "createGroup": "创建图层组", "editLayer": "编辑图层", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除图层 \"{name}\" 吗？", "addLayerToGroup": "添加图层到组", "addGroupToGroup": "添加子组"}, "physics": {"noPhysicsBody": "选中的实体没有物理体组件", "addPhysicsBody": "添加物理体", "physicsBody": "物理体", "noPhysicsCollider": "选中的实体没有物理碰撞器组件", "addPhysicsCollider": "添加物理碰撞器", "physicsCollider": "物理碰撞器", "noPhysicsConstraint": "选中的实体没有物理约束组件", "addPhysicsConstraint": "添加物理约束", "physicsConstraint": "物理约束", "noCharacterController": "选中的实体没有角色控制器组件", "addCharacterController": "添加角色控制器", "characterController": "角色控制器", "physicsDebugger": "物理调试器", "type": "类型", "typeTooltip": "物理体类型", "static": "静态", "dynamic": "动态", "kinematic": "运动学", "mass": "质量", "massTooltip": "物理体质量", "damping": "阻尼", "linearDamping": "线性阻尼", "linearDampingTooltip": "线性运动阻尼", "angularDamping": "角度阻尼", "angularDampingTooltip": "角度运动阻尼", "constraints": {"pointToPoint": "点对点", "hinge": "铰链", "distance": "距离", "lock": "锁定", "spring": "弹簧", "coneTwist": "锥形扭转", "slider": "滑块", "fixed": "固定", "wheel": "轮子"}, "fixedRotation": "固定旋转", "fixedRotationTooltip": "是否固定旋转", "linearFactor": "线性因子", "linearFactorTooltip": "线性运动因子", "angularFactor": "角度因子", "angularFactorTooltip": "角度运动因子", "sleep": "休眠", "allowSleep": "允许休眠", "allowSleepTooltip": "是否允许物理体休眠", "sleepSpeedLimit": "休眠速度限制", "sleepSpeedLimitTooltip": "休眠速度阈值", "sleepTimeLimit": "休眠时间限制", "sleepTimeLimitTooltip": "休眠时间阈值", "material": "材质", "materialType": "材质类型", "materialTypeTooltip": "物理材质类型", "materials": "材质", "colliderType": "碰撞器类型", "colliderTypeTooltip": "碰撞器形状类型", "colliders": {"box": "盒子", "sphere": "球体", "capsule": "胶囊", "cylinder": "圆柱", "plane": "平面"}, "isTrigger": "是触发器", "isTriggerTooltip": "是否为触发器", "transform": "变换", "offset": "偏移", "offsetTooltip": "碰撞器偏移", "rotation": "旋转", "rotationTooltip": "碰撞器旋转", "shapeParameters": "形状参数", "boxHalfExtents": "盒子半尺寸", "boxHalfExtentsTooltip": "盒子碰撞器的半尺寸", "sphereRadius": "球体半径", "sphereRadiusTooltip": "球体碰撞器的半径", "capsuleRadius": "胶囊半径", "capsuleRadiusTooltip": "胶囊碰撞器的半径", "capsuleHeight": "胶囊高度", "capsuleHeightTooltip": "胶囊碰撞器的高度", "cylinderRadius": "圆柱半径", "cylinderRadiusTooltip": "圆柱碰撞器的半径", "cylinderHeight": "圆柱高度", "cylinderHeightTooltip": "圆柱碰撞器的高度", "planeNormal": "平面法线", "planeNormalTooltip": "平面碰撞器的法线方向", "collisionFiltering": "碰撞过滤", "collisionGroup": "碰撞组", "collisionGroupTooltip": "碰撞组设置", "collisionMask": "碰撞掩码", "collisionMaskTooltip": "碰撞掩码设置", "constraintType": "约束类型", "constraintTypeTooltip": "物理约束类型", "targetEntity": "目标实体", "targetEntityTooltip": "约束的目标实体", "selectTargetEntity": "选择目标实体", "collideConnected": "连接碰撞", "collideConnectedTooltip": "连接的物体是否可以碰撞", "constraintParameters": "约束参数", "pivotA": "支点A", "pivotATooltip": "约束支点A的位置", "pivotB": "支点B", "pivotBTooltip": "约束支点B的位置", "axisA": "轴A", "axisATooltip": "约束轴A的方向", "axisB": "轴B", "axisBTooltip": "约束轴B的方向", "distance": "距离", "distanceTooltip": "约束距离", "stiffness": "刚度", "stiffnessTooltip": "弹簧刚度", "dampingTooltip": "阻尼系数", "twistAngle": "扭转角度", "twistAngleTooltip": "扭转角度限制", "lowerLimit": "下限", "lowerLimitTooltip": "约束下限", "upperLimit": "上限", "upperLimitTooltip": "约束上限", "suspensionStiffness": "悬挂刚度", "suspensionStiffnessTooltip": "悬挂系统刚度", "suspensionDamping": "悬挂阻尼", "suspensionDampingTooltip": "悬挂系统阻尼", "suspensionLength": "悬挂长度", "suspensionLengthTooltip": "悬挂系统长度", "maxForce": "最大力", "maxForceTooltip": "约束最大力", "enableDebugger": "启用调试器", "enableDebuggerTooltip": "启用物理调试器", "useEnhancedDebugger": "使用增强调试器", "useEnhancedDebuggerTooltip": "使用增强版物理调试器", "basicSettings": "基本设置", "bodyColor": "物体颜色", "bodyColorTooltip": "物理体显示颜色", "constraintColor": "约束颜色", "constraintColorTooltip": "约束显示颜色", "enhancedSettings": "增强设置", "showVelocities": "显示速度", "showVelocitiesTooltip": "显示物体速度向量", "showForces": "显示力", "showForcesTooltip": "显示作用力向量", "showCenterOfMass": "显示质心", "showCenterOfMassTooltip": "显示物体质心", "showSleepState": "显示休眠状态", "showSleepStateTooltip": "显示物体休眠状态", "showPerformanceStats": "显示性能统计", "showPerformanceStatsTooltip": "显示物理性能统计", "showContactNormals": "显示接触法线", "showContactNormalsTooltip": "显示碰撞接触法线", "showContactForces": "显示接触力", "showContactForcesTooltip": "显示碰撞接触力", "showFrictionForces": "显示摩擦力", "showFrictionForcesTooltip": "显示摩擦力向量", "colors": "颜色", "velocityColor": "速度颜色", "velocityColorTooltip": "速度向量显示颜色", "forceColor": "力颜色", "forceColorTooltip": "力向量显示颜色", "centerOfMassColor": "质心颜色", "centerOfMassColorTooltip": "质心显示颜色", "sleepStateColor": "休眠状态颜色", "sleepStateColorTooltip": "休眠状态显示颜色", "contactNormalColor": "接触法线颜色", "contactNormalColorTooltip": "接触法线显示颜色", "contactForceColor": "接触力颜色", "contactForceColorTooltip": "接触力显示颜色", "frictionForceColor": "摩擦力颜色", "frictionForceColorTooltip": "摩擦力显示颜色", "vectorScale": "向量缩放", "vectorScaleTooltip": "调试向量显示缩放", "characterOffsetTooltip": "角色控制器偏移", "slopeSettings": "坡度设置", "maxSlopeClimbAngle": "最大爬坡角度", "maxSlopeClimbAngleTooltip": "角色可以爬升的最大坡度角度", "minSlopeSlideAngle": "最小滑坡角度", "minSlopeSlideAngleTooltip": "角色开始滑坡的最小角度", "autoStepSettings": "自动台阶设置", "enableAutoStep": "启用自动台阶", "enableAutoStepTooltip": "是否启用自动台阶功能", "autoStepMaxHeight": "自动台阶最大高度", "autoStepMaxHeightTooltip": "自动台阶的最大高度", "autoStepMinWidth": "自动台阶最小宽度", "autoStepMinWidthTooltip": "自动台阶的最小宽度", "autoStepOverDynamic": "动态物体上的自动台阶", "autoStepOverDynamicTooltip": "是否在动态物体上启用自动台阶", "snapToGroundSettings": "贴地设置", "enableSnapToGround": "启用贴地", "enableSnapToGroundTooltip": "是否启用贴地功能", "snapToGroundDistance": "贴地距离", "snapToGroundDistanceTooltip": "贴地检测距离", "physicsEditor": "物理编辑器", "system": "系统", "components": "组件", "presets": "预设", "debugger": "调试器", "examples": {"basicPhysics": "基础物理", "constraints": "约束", "characterController": "角色控制器", "continuousCollisionDetection": "连续碰撞检测", "collisionVisualization": "碰撞可视化", "physicsPresets": "物理预设", "vehiclePhysics": "载具物理", "ragdoll": "布娃娃", "softBody": "软体"}, "physicsExamples": "物理示例", "import_export": "导入导出", "sceneImportExport": "场景导入导出", "exportScene": "导出场景", "importScene": "导入场景", "exportPresets": "导出预设", "importPresets": "导入预设"}, "particle": {"edit": "编辑", "create": "创建", "save": "保存", "cancel": "取消", "reset": "重置", "presets": {"fire": "火焰", "smoke": "烟雾", "snow": "雪花"}, "name": "名称", "emission": "发射", "maxParticles": "最大粒子数", "emissionRate": "发射率", "lifetime": "生命周期", "min": "最小值", "max": "最大值", "loop": "循环", "prewarm": "预热", "simulationSpace": {"local": "本地", "world": "世界"}, "shape": "形状", "particleShape": "粒子形状", "texture": "纹理", "emitterType": "发射器类型", "emitterRadius": "发射器半径", "emitterSize": "发射器尺寸", "appearance": "外观", "startSize": "起始尺寸", "endSize": "结束尺寸", "startColor": "起始颜色", "endColor": "结束颜色", "blendMode": "混合模式", "renderMode": {"3d": "3D", "billboard": "广告牌", "stretched": "拉伸"}, "motion": "运动", "startSpeed": "起始速度", "gravity": "重力", "updateSuccess": "更新成功", "createSuccess": "创建成功", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除这个粒子系统吗？", "deleteSuccess": "删除成功", "duplicateSuccess": "复制成功", "duplicate": "复制", "export": "导出", "delete": "删除", "library": "粒子库", "search": "搜索", "import": "导入", "noSearchResults": "无搜索结果", "noParticleSystems": "暂无粒子系统", "preview": "预览", "close": "关闭", "emitterTypes": {"point": "点", "circle": "圆形", "box": "盒子", "sphere": "球体"}, "shapes": {"circle": "圆形", "square": "方形", "triangle": "三角形", "custom": "自定义"}, "createdAt": "创建时间"}, "animation": {"library": "动画库", "search": "搜索", "import": "导入", "export": "导出", "create": "创建", "edit": "编辑", "delete": "删除", "duplicate": "复制", "preview": "预览", "play": "播放", "pause": "暂停", "stop": "停止", "loop": "循环", "speed": "速度", "duration": "持续时间", "name": "名称", "type": "类型", "tags": "标签", "description": "描述", "createdAt": "创建时间", "updatedAt": "更新时间", "noAnimations": "暂无动画", "noSearchResults": "无搜索结果", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除这个动画吗？", "deleteSuccess": "删除成功", "duplicateSuccess": "复制成功", "createSuccess": "创建成功", "updateSuccess": "更新成功", "importSuccess": "导入成功", "exportSuccess": "导出成功", "facial": {"editor": "面部动画编辑器", "noAvatar": "请先选择一个角色", "selectAvatar": "选择角色", "emotions": "情绪", "custom": "自定义", "intensity": "强度", "blend": "混合", "timeline": "时间轴", "keyframes": "关键帧", "addKeyframe": "添加关键帧", "deleteKeyframe": "删除关键帧", "currentTime": "当前时间", "totalTime": "总时间", "fps": "帧率", "record": "录制", "recording": "录制中", "stopRecording": "停止录制", "clear": "清除", "reset": "重置", "save": "保存", "load": "加载", "expressions": {"neutral": "中性", "happy": "开心", "sad": "悲伤", "angry": "愤怒", "surprised": "惊讶", "disgusted": "厌恶", "fearful": "恐惧", "contempt": "轻蔑"}, "phonemes": {"silence": "静音", "pp": "PP", "ff": "FF", "th": "TH", "dd": "DD", "kk": "KK", "ch": "CH", "ss": "SS", "nn": "NN", "rr": "RR", "aa": "AA", "e": "E", "ih": "IH", "oh": "OH", "ou": "OU"}, "aiGeneration": "AI生成", "generateFromText": "从文本生成", "generateFromAudio": "从音频生成", "inputText": "输入文本", "uploadAudio": "上传音频", "generate": "生成", "generating": "生成中...", "generationComplete": "生成完成", "generationFailed": "生成失败", "noTextProvided": "请输入文本", "noAudioProvided": "请上传音频文件", "supportedFormats": "支持的格式", "maxFileSize": "最大文件大小", "processingAudio": "处理音频中...", "audioProcessed": "音频处理完成", "audioProcessingFailed": "音频处理失败"}, "mask": {"basic": "基本设置", "bones": "骨骼列表", "presets": "预设模板", "advanced": "高级设置", "preview": "预览", "name": "遮罩名称", "type": "遮罩类型", "weightType": "权重类型", "include": "包含模式", "exclude": "排除模式", "hierarchy": "层级模式", "transform": "变换模式", "addBone": "添加骨骼", "removeBone": "移除骨骼", "selectAll": "全选", "selectNone": "全不选", "invertSelection": "反选", "weight": "权重", "influence": "影响力", "threshold": "阈值", "falloff": "衰减", "smooth": "平滑", "feather": "羽化", "showBones": "显示骨骼", "showWeights": "显示权重", "colorMode": "颜色模式", "heatmap": "热力图", "gradient": "渐变", "solid": "纯色", "resetView": "重置视图", "applyToModel": "应用到模型", "savePreset": "保存预设", "loadPreset": "加载预设", "deletePreset": "删除预设", "presetName": "预设名称", "presetDescription": "预设描述", "createPreset": "创建预设", "editPreset": "编辑预设", "duplicatePreset": "复制预设", "importPreset": "导入预设", "exportPreset": "导出预设", "noPresets": "暂无预设", "searchPresets": "搜索预设", "filterByType": "按类型筛选", "filterByBone": "按骨骼筛选", "sortByName": "按名称排序", "sortByDate": "按日期排序", "sortByUsage": "按使用频率排序"}}, "water": {"material": "水材质", "editor": "水材质编辑器", "noWaterMaterial": "选中的实体没有水材质组件", "addWaterMaterial": "添加水材质", "basic": "基本设置", "color": "颜色", "transparency": "透明度", "refraction": "折射", "reflection": "反射", "waves": "波浪", "waveHeight": "波浪高度", "waveSpeed": "波浪速度", "waveFrequency": "波浪频率", "waveDirection": "波浪方向", "foam": "泡沫", "foamColor": "泡沫颜色", "foamIntensity": "泡沫强度", "foamThreshold": "泡沫阈值", "caustics": "焦散", "causticsIntensity": "焦散强度", "causticsScale": "焦散缩放", "causticsSpeed": "焦散速度", "underwater": "水下效果", "underwaterColor": "水下颜色", "underwaterFogDensity": "水下雾密度", "underwaterDistortion": "水下扭曲", "advanced": "高级设置", "normalMap": "法线贴图", "normalMapIntensity": "法线强度", "normalMapScale": "法线缩放", "flowMap": "流动贴图", "flowSpeed": "流动速度", "flowDirection": "流动方向", "presets": {"ocean": "海洋", "lake": "湖泊", "river": "河流", "pool": "游泳池", "custom": "自定义"}}, "underground": {"lighting": "地下照明", "editor": "地下照明编辑器", "noUndergroundLighting": "选中的实体没有地下照明组件", "addUndergroundLighting": "添加地下照明", "ambient": "环境光", "ambientColor": "环境光颜色", "ambientIntensity": "环境光强度", "directional": "定向光", "directionalColor": "定向光颜色", "directionalIntensity": "定向光强度", "directionalDirection": "定向光方向", "shadows": "阴影", "shadowIntensity": "阴影强度", "shadowBias": "阴影偏移", "shadowRadius": "阴影半径", "volumetric": "体积光", "volumetricIntensity": "体积光强度", "volumetricDensity": "体积光密度", "volumetricScattering": "体积光散射", "fog": "雾效", "fogColor": "雾颜色", "fogDensity": "雾密度", "fogStart": "雾起始距离", "fogEnd": "雾结束距离"}, "materialLibrary": {"title": "材质库", "search": "搜索", "import": "导入", "export": "导出", "create": "创建", "edit": "编辑", "delete": "删除", "duplicate": "复制", "preview": "预览", "name": "名称", "type": "类型", "tags": "标签", "description": "描述", "createdAt": "创建时间", "updatedAt": "更新时间", "noMaterials": "暂无材质", "noSearchResults": "无搜索结果", "confirmDelete": "确认删除", "confirmDeleteContent": "确定要删除这个材质吗？", "deleteSuccess": "删除成功", "duplicateSuccess": "复制成功", "createSuccess": "创建成功", "updateSuccess": "更新成功", "importSuccess": "导入成功", "exportSuccess": "导出成功", "categories": {"all": "全部", "basic": "基础", "metal": "金属", "wood": "木材", "fabric": "织物", "glass": "玻璃", "plastic": "塑料", "stone": "石材", "organic": "有机", "custom": "自定义"}}, "editorPage": {"loading": "加载中...", "loadingProject": "加载项目中...", "projectLoaded": "项目加载完成", "projectLoadFailed": "项目加载失败", "savingProject": "保存项目中...", "projectSaved": "项目保存成功", "projectSaveFailed": "项目保存失败", "unsavedChanges": "有未保存的更改", "confirmLeave": "确定要离开吗？未保存的更改将丢失。", "autoSave": "自动保存", "autoSaveEnabled": "自动保存已启用", "autoSaveDisabled": "自动保存已禁用", "lastSaved": "上次保存", "never": "从未", "justNow": "刚刚", "minutesAgo": "{minutes} 分钟前", "hoursAgo": "{hours} 小时前", "daysAgo": "{days} 天前"}, "ai": {"facialAnimation": {"service": "AI面部动画服务", "generating": "生成中...", "generationComplete": "生成完成", "generationFailed": "生成失败", "noTextProvided": "请提供文本", "noAudioProvided": "请提供音频", "textToAnimation": "文本转动画", "audioToAnimation": "音频转动画", "inputText": "输入文本", "uploadAudio": "上传音频", "generate": "生成", "cancel": "取消", "retry": "重试", "download": "下载", "preview": "预览", "settings": "设置", "quality": "质量", "speed": "速度", "emotion": "情绪", "intensity": "强度", "language": "语言", "voice": "声音", "supportedFormats": "支持的格式", "maxFileSize": "最大文件大小", "processingTime": "预计处理时间", "credits": "积分", "creditsRemaining": "剩余积分", "creditsRequired": "所需积分", "insufficientCredits": "积分不足", "purchaseCredits": "购买积分", "freeCredits": "免费积分", "premiumFeatures": "高级功能", "upgradeAccount": "升级账户"}}, "terrain": {"terrainEditor": "地形编辑器", "noTerrainComponent": "选中的实体没有地形组件", "addTerrainComponent": "添加地形组件", "terrainComponentAdded": "地形组件添加成功", "addTerrainComponentFailed": "添加地形组件失败"}, "common": {"entities": "实体", "properties": "属性", "noEntitySelected": "未选择实体", "noEntities": "暂无实体", "searchEntities": "搜索实体", "addEntity": "添加实体", "noSearchResults": "无搜索结果", "hide": "隐藏", "show": "显示", "lock": "锁定", "unlock": "解锁", "basicInfo": "基本信息", "nameRequired": "名称为必填项", "enterName": "请输入名称", "transform": "变换", "components": "组件", "noComponents": "暂无组件", "addComponent": "添加组件", "componentConfigured": "组件已配置"}, "rendering": {"water": {"title": "水材质编辑器", "saveSuccess": "保存成功", "realtimePreviewEnabled": "实时预览已启用", "realtimePreviewDisabled": "实时预览已禁用", "presetApplied": "预设已应用", "type": "类型", "typeTooltip": "水体类型", "typeOcean": "海洋", "typeLake": "湖泊", "typeRiver": "河流", "typePool": "游泳池", "typePuddle": "水坑", "typeUnderground": "地下水", "color": "颜色", "colorTooltip": "水体颜色", "opacity": "不透明度", "opacityTooltip": "水体不透明度", "reflectivity": "反射率", "reflectivityTooltip": "水面反射强度", "refractionRatio": "折射率", "refractionRatioTooltip": "水体折射率", "waveStrength": "波浪强度", "waveStrengthTooltip": "波浪起伏强度", "waveSpeed": "波浪速度", "waveSpeedTooltip": "波浪运动速度", "waveScale": "波浪缩放", "waveScaleTooltip": "波浪纹理缩放", "waveDirection": "波浪方向", "waveDirectionTooltip": "波浪运动方向", "depth": "深度", "depthTooltip": "水体深度", "depthColor": "深水颜色", "depthColorTooltip": "深水区域颜色", "shallowColor": "浅水颜色", "shallowColorTooltip": "浅水区域颜色", "enableCaustics": "启用焦散", "enableCausticsTooltip": "是否启用水下焦散效果", "causticsIntensity": "焦散强度", "causticsIntensityTooltip": "焦散效果强度", "enableFoam": "启用泡沫", "enableFoamTooltip": "是否启用水面泡沫效果", "foamIntensity": "泡沫强度", "foamIntensityTooltip": "泡沫效果强度", "enableUnderwaterFog": "启用水下雾效", "enableUnderwaterFogTooltip": "是否启用水下雾效", "underwaterFogDensity": "水下雾密度", "underwaterFogDensityTooltip": "水下雾效密度", "enableUnderwaterDistortion": "启用水下扭曲", "enableUnderwaterDistortionTooltip": "是否启用水下视觉扭曲", "underwaterDistortionStrength": "水下扭曲强度", "underwaterDistortionStrengthTooltip": "水下扭曲效果强度", "normalMap": "法线贴图", "normalMapTooltip": "水面法线贴图", "reflectionMap": "反射贴图", "reflectionMapTooltip": "水面反射贴图", "refractionMap": "折射贴图", "refractionMapTooltip": "水体折射贴图", "depthMap": "深度贴图", "depthMapTooltip": "水体深度贴图", "causticsMap": "焦散贴图", "causticsMapTooltip": "焦散效果贴图", "foamMap": "泡沫贴图", "foamMapTooltip": "泡沫效果贴图", "presetTooltip": "选择水材质预设", "presets": "预设", "previewModeTooltip": "切换预览模式", "realtimePreview": "实时预览", "staticPreview": "静态预览", "togglePreviewTooltip": "切换预览模式", "basicTab": "基本设置", "wavesTab": "波浪设置", "depthTab": "深度设置", "effectsTab": "效果设置", "texturesTab": "纹理设置", "preview": "预览", "updatePreview": "更新预览", "selectWaterPreset": "选择水材质预设", "selectCategory": "选择分类", "selectTags": "选择标签", "noPresetsFound": "未找到预设", "noFavorites": "暂无收藏"}, "undergroundLighting": {"title": "地下照明编辑器", "enabled": "启用地下照明", "autoUpdate": "自动更新", "updateFrequency": "更新频率", "updateFrequencyTooltip": "照明更新频率设置", "features": "功能特性", "enableCaveLighting": "启用洞穴照明", "enableStalactiteReflection": "启用钟乳石反射", "enableWaterReflection": "启用水面反射", "enableVolumetricLight": "启用体积光", "enableVolumetricFog": "启用体积雾", "enableDebugVisualization": "启用调试可视化", "enablePerformanceMonitoring": "启用性能监控", "caveLights": "洞穴光源", "addCaveLight": "添加洞穴光源", "caveLight": "洞穴光源", "editCaveLight": "编辑洞穴光源", "position": "位置", "color": "颜色", "intensity": "强度", "size": "尺寸", "decay": "衰减", "castShadow": "投射阴影", "stalactiteReflections": "钟乳石反射", "addStalactiteReflection": "添加钟乳石反射", "stalactiteReflection": "钟乳石反射", "editStalactiteReflection": "编辑钟乳石反射", "flickerSpeed": "闪烁速度", "flickerIntensity": "闪烁强度", "waterReflections": "水面反射", "addWaterReflection": "添加水面反射", "waterReflection": "水面反射", "volumetricLights": "体积光", "addVolumetricLight": "添加体积光", "volumetricLight": "体积光", "volumetricFogs": "体积雾", "addVolumetricFog": "添加体积雾", "volumetricFog": "体积雾", "density": "密度", "systemTab": "系统设置", "caveLightingTab": "洞穴照明", "stalactiteReflectionTab": "钟乳石反射", "waterReflectionTab": "水面反射", "volumetricLightTab": "体积光", "volumetricFogTab": "体积雾"}}, "weatherView": "天气视图", "postProcessingView": "后处理视图", "uiView": "UI视图", "scriptView": "脚本视图", "shaderView": "着色器视图", "debugView": "调试视图", "profileView": "性能分析视图", "statisticsView": "统计视图", "historyView": "历史视图", "bookmarkView": "书签视图", "searchView": "搜索视图", "helpView": "帮助视图", "aboutView": "关于视图", "welcomeView": "欢迎视图", "loginView": "登录视图", "registerView": "注册视图", "teamView": "团队视图", "chatView": "聊天视图", "notificationView": "通知视图", "activityView": "活动视图", "settingsView": "设置视图", "preferencesView": "首选项视图", "themeView": "主题视图", "languageView": "语言视图", "shortcutView": "快捷键视图", "pluginView": "插件视图", "extensionView": "扩展视图", "marketplaceView": "市场视图", "storeView": "商店视图", "communityView": "社区视图", "forumView": "论坛视图", "documentationView": "文档视图", "tutorialView": "教程视图", "exampleView": "示例视图", "templateView": "模板视图", "assetStoreView": "资产商店视图", "pluginStoreView": "插件商店视图", "extensionStoreView": "扩展商店视图", "selectTool": "选择工具", "translateTool": "平移工具", "rotateTool": "旋转工具", "scaleTool": "缩放工具", "transformSpace": "变换空间", "snapMode": "捕捉模式", "snapDisabled": "禁用捕捉", "snapToVertex": "顶点捕捉", "local": "本地", "world": "世界", "save": "保存", "snapToGrid": "网格捕捉", "layout": {"manage": "管理布局", "reset": "重置布局", "save": "保存布局", "loadLayout": "加载布局", "saveLayout": "保存布局", "layoutName": "布局名称", "nameRequired": "请输入布局名称", "enterName": "请输入布局名称", "darkTheme": "切换到暗色主题", "lightTheme": "切换到亮色主题"}, "loading": "加载中...", "assetsView": "资产视图", "instancesView": "实例视图", "searchAssets": "搜索资产", "sortByName": "按名称", "sortByType": "按类型", "sortByDate": "按日期", "ascending": "升序", "descending": "降序", "new": "新建", "upload": "上传", "folder": "文件夹", "uploadAssets": "上传资产", "clickOrDragToUpload": "点击或拖拽上传", "supportedFormats": "支持的格式：模型、纹理、材质、音频等", "gridView": "网格视图", "listView": "列表视图", "allAssets": "全部资源", "models": "模型", "textures": "纹理", "noModels": "暂无模型", "noTextures": "暂无纹理", "noMaterials": "暂无材质", "newFolder": "新建文件夹", "refresh": "刷新", "info": "信息", "warning": "警告", "error": "错误", "debug": "调试", "toggleInfo": "切换信息日志", "toggleWarning": "切换警告日志", "toggleError": "切换错误日志", "toggleDebug": "切换调试日志", "clearConsole": "清空控制台", "copyAllLogs": "复制全部日志", "disableAutoScroll": "禁用自动滚动", "enableAutoScroll": "启用自动滚动", "searchLogs": "搜索日志"}, "feedback": {"title": {"general": "反馈", "animation": "动画系统反馈", "physics": "物理系统反馈", "rendering": "渲染系统反馈", "editor": "编辑器反馈"}, "button": {"general": "提交反馈", "animation": "动画反馈", "physics": "物理反馈", "rendering": "渲染反馈", "editor": "编辑器反馈"}, "tooltip": "提交反馈或建议", "form": {"feedbackType": "反馈类型", "severity": "严重程度", "title": "标题", "titlePlaceholder": "请简要描述您的反馈", "description": "详细描述", "descriptionPlaceholder": "请详细描述您的反馈内容", "reproductionSteps": "重现步骤", "reproductionStepsPlaceholder": "请描述如何重现这个问题", "satisfaction": "满意度评分", "suggestions": "改进建议", "suggestionsPlaceholder": "您对改进的建议", "screenshots": "截图", "uploadScreenshot": "上传截图", "allowContact": "允许我们联系您以获取更多信息", "submit": "提交反馈", "required": "此字段为必填项"}, "type": {"bug": "缺陷", "feature": "功能请求", "improvement": "改进建议", "performance": "性能问题", "usability": "可用性问题", "other": "其他"}, "severity": {"critical": "严重", "high": "高", "medium": "中", "low": "低"}, "success": {"title": "反馈提交成功", "message": "感谢您的反馈！我们将认真考虑您的意见。", "close": "关闭"}, "submitError": "提交反馈失败，请稍后再试", "animation": {"type": {"bug": "动画缺陷", "feature": "动画功能请求", "improvement": "动画改进建议", "performance": "动画性能问题", "usability": "动画可用性问题", "other": "其他动画问题"}, "subType": {"blend": "动画混合系统", "stateMachine": "动画状态机", "timeline": "动画时间轴", "retargeting": "动画重定向", "general": "一般动画问题"}, "blend": {"issueCategory": "问题类别", "selectIssueCategory": "请选择问题类别", "issue": {"weight": "权重问题", "mask": "遮罩问题", "transition": "过渡问题", "performance": "性能问题", "ui": "界面问题", "other": "其他问题"}}, "descriptionPlaceholder": "请详细描述您在使用动画系统时遇到的问题或建议", "reproductionStepsPlaceholder": "请描述如何重现这个动画问题", "suggestionsPlaceholder": "您对动画系统改进的建议", "includePerformanceData": "包含性能数据", "performanceDataTooltip": "这将帮助我们更好地分析和解决性能问题", "performanceDataPreview": "性能数据预览", "success": {"additionalMessage": "您的反馈将帮助我们改进动画混合系统。"}}, "analytics": {"title": "反馈分析", "loading": "加载反馈数据中...", "error": "加载反馈数据失败", "loadError": "无法加载反馈数据，请稍后再试", "noData": "暂无反馈数据", "noSubTypeData": "暂无子类型数据", "refresh": "刷新", "resetFilters": "重置筛选", "filter": {"type": "筛选类型", "subType": "筛选子类型", "feedbackType": "筛选反馈类型", "startDate": "开始日期", "endDate": "结束日期"}, "stats": {"total": "总反馈数", "bugs": "缺陷数", "features": "功能请求数", "satisfaction": "平均满意度"}, "charts": {"byType": "按类型统计", "bySubType": "按子类型统计"}, "tabs": {"overview": "概览", "recent": "最近反馈"}, "table": {"title": "标题", "type": "类型", "satisfaction": "满意度", "date": "日期"}}, "contextAware": {"title": "上下文感知反馈", "description": "根据当前操作上下文自动收集相关信息", "tooltip": "提交带有当前操作上下文的反馈", "contextData": "上下文数据", "automatic": "自动采集", "selectedEntity": "选中的实体", "activeTool": "当前工具", "activePanels": "当前面板", "recentOperations": "最近操作", "screenshotPreview": "截图预览", "screenshotError": "截图捕获失败", "additionalScreenshots": "额外截图", "contextTooltip": "此反馈将自动包含当前编辑器状态信息"}, "manager": {"title": "标题", "description": "管理和分析用户反馈", "loadError": "加载反馈数据失败", "exportSuccess": "导出反馈成功", "exportError": "导出反馈失败", "deleteSuccess": "删除反馈成功", "deleteError": "删除反馈失败", "updateSuccess": "更新反馈状态成功", "updateError": "更新反馈状态失败", "search": "搜索反馈", "filterType": "筛选类型", "filterFeedbackType": "筛选反馈类型", "filterStatus": "筛选状态", "startDate": "开始日期", "endDate": "结束日期", "refresh": "刷新", "resetFilters": "重置筛选", "id": "ID", "type": "类型", "feedbackType": "反馈类型", "satisfaction": "满意度", "status": "状态", "date": "日期", "actions": "操作", "view": "查看", "markInProgress": "标记为处理中", "markResolved": "标记为已解决", "delete": "删除", "confirmDelete": "确认删除", "noData": "暂无反馈数据", "exportExcel": "导出为Excel", "exportPDF": "导出为PDF", "exportJSON": "导出为JSON", "tabList": "反馈列表", "tabAnalytics": "反馈分析", "feedbackDetail": "反馈详情", "content": "反馈内容", "contextData": "上下文数据", "userContext": "用户上下文", "editorContext": "编辑器上下文", "recentOperations": "最近操作", "browser": "浏览器", "screenSize": "屏幕尺寸", "location": "位置", "selectedEntity": "选中的实体", "activeTool": "当前工具", "activePanels": "当前面板", "statusManagement": "状态管理", "currentStatus": "当前状态", "changeStatus": "更改状态", "statusNew": "新建", "statusInProgress": "处理中", "statusResolved": "已解决", "statusClosed": "已关闭", "comments": "评论", "noComments": "暂无评论", "addComment": "添加评论", "addCommentPlaceholder": "请输入您的评论...", "commentAdded": "评论已添加", "commentError": "添加评论失败", "staff": "工作人员"}, "statistics": {"title": "反馈统计", "loading": "加载反馈数据中...", "error": "加载反馈数据失败", "loadError": "无法加载反馈数据，请稍后再试", "noData": "暂无反馈数据", "refresh": "刷新", "resetFilters": "重置筛选", "filterType": "筛选类型", "filterFeedbackType": "筛选反馈类型", "startDate": "开始日期", "endDate": "结束日期", "chartType": "图表类型", "pieChart": "饼图", "barChart": "柱状图", "timeRange": "时间范围", "week": "周", "month": "月", "year": "年", "totalCount": "总数", "bugs": "缺陷", "features": "功能请求", "improvements": "改进建议", "stats": {"total": "总反馈数", "bugs": "缺陷数", "features": "功能请求数", "satisfaction": "平均满意度"}, "charts": {"byType": "按类型统计", "bySubType": "按子类型统计", "trend": "趋势分析"}, "tabs": {"overview": "概览", "trends": "趋势"}}}, "collaboration": {"title": "协作编辑", "enable": "启用协作", "disabled": "协作功能已禁用", "inviteOthers": "邀请其他人加入", "copyLink": "复制链接", "inviteLinkCopied": "邀请链接已复制到剪贴板", "you": "你", "noUsers": "暂无在线用户", "noOperations": "暂无操作历史", "settingsDescription": "协作设置可以控制权限和同步行为", "tabs": {"users": "用户", "history": "历史", "settings": "设置"}, "status": {"connected": "已连接", "connecting": "连接中", "disconnected": "未连接", "error": "连接错误", "active": "活跃", "inactive": "空闲"}, "roles": {"owner": "所有者", "admin": "管理员", "editor": "编辑者", "viewer": "查看者"}, "operations": {"entityCreate": "创建实体", "entityUpdate": "更新实体", "entityDelete": "删除实体", "componentAdd": "添加组件", "componentUpdate": "更新组件", "componentRemove": "移除组件", "sceneUpdate": "更新场景", "cursorMove": "移动光标", "selectionChange": "更改选择"}, "operationDescriptions": {"entityCreate": "创建了实体 \"{name}\"", "entityUpdate": "更新了实体 \"{name}\"", "entityDelete": "删除了实体 \"{name}\"", "componentAdd": "向实体 \"{entity}\" 添加了 \"{component}\" 组件", "componentUpdate": "更新了实体 \"{entity}\" 的 \"{component}\" 组件", "componentRemove": "从实体 \"{entity}\" 移除了 \"{component}\" 组件", "sceneUpdate": "更新了场景设置", "cursorMove": "移动了光标", "selectionChange": "选择了 {count} 个对象", "unknown": "未知操作"}, "errors": {"noProjectOrScene": "未选择项目或场景", "connectionFailed": "连接失败", "unauthorized": "未授权", "permissionDenied": "权限不足", "copyFailed": "复制失败"}}, "auth": {"login": "登录", "register": "注册", "logout": "退出登录", "forgotPassword": "忘记密码", "resetPassword": "重置密码", "changePassword": "修改密码", "username": "用户名", "password": "密码", "email": "邮箱", "confirmPassword": "确认密码", "rememberMe": "记住我", "loginSuccess": "登录成功", "loginFailed": "登录失败", "registerSuccess": "注册成功", "registerFailed": "注册失败", "logoutSuccess": "退出登录成功", "logoutFailed": "退出登录失败", "resetPasswordSuccess": "重置密码成功", "resetPasswordFailed": "重置密码失败", "changePasswordSuccess": "修改密码成功", "changePasswordFailed": "修改密码失败", "passwordNotMatch": "密码不匹配", "passwordTooShort": "密码太短", "passwordTooWeak": "密码太弱", "emailInvalid": "邮箱无效", "usernameInvalid": "用户名无效", "usernameTaken": "用户名已被占用", "emailTaken": "邮箱已被占用", "accountNotFound": "账号不存在", "accountLocked": "账号已锁定", "accountDisabled": "账号已禁用", "accountExpired": "账号已过期", "sessionExpired": "会话已过期", "unauthorized": "未授权", "forbidden": "禁止访问", "tooManyRequests": "请求过于频繁", "serverError": "服务器错误", "networkError": "网络错误", "unknownError": "未知错误", "loginTitle": "欢迎登录", "loginSubtitle": "请输入您的账户信息", "emailPlaceholder": "请输入邮箱", "passwordPlaceholder": "请输入密码", "emailRequired": "请输入邮箱", "passwordRequired": "请输入密码", "noAccount": "还没有账号？", "orLoginWith": "或使用以下方式登录", "orRegisterWith": "或使用以下方式注册", "registerTitle": "创建账户", "registerSubtitle": "填写信息以完成注册", "usernameRequired": "请输入用户名", "usernameTooShort": "用户名至少 3 个字符", "usernamePlaceholder": "请输入用户名", "confirmPasswordRequired": "请再次输入密码", "passwordMismatch": "两次输入的密码不一致", "confirmPasswordPlaceholder": "请再次输入密码", "agreementRequired": "请先勾选并同意条款", "agreement": "我已阅读并同意", "terms": "服务条款", "privacy": "隐私政策", "and": "和", "haveAccount": "已经有账号？"}, "projects": {"title": "项目", "allProjects": "所有项目", "myProjects": "我的项目", "shared": "共享项目", "new": "新建项目", "newProject": "新建项目", "newScene": "新建场景", "open": "打开", "edit": "", "delete": "删除", "search": "搜索项目", "gridView": "网格视图", "listView": "列表视图", "public": "公开", "private": "私有", "noProjects": "暂无项目", "noMyProjects": "暂无我的项目", "noSharedProjects": "暂无共享项目", "noSearchResults": "未找到匹配的项目", "createFirst": "创建第一个项目", "deleteSuccess": "项目删除成功", "deleteError": "项目删除失败", "createSuccess": "项目创建成功", "createError": "项目创建失败", "sceneCreateSuccess": "场景创建成功", "sceneCreateError": "场景创建失败", "noProjectsFound": "未找到项目", "name": "项目名称", "nameRequired": "请输入项目名称", "namePlaceholder": "请输入项目名称", "description": "项目描述", "descriptionPlaceholder": "请输入项目描述", "visibility": "可见性", "sceneName": "场景名称", "sceneNameRequired": "请输入场景名称", "sceneNamePlaceholder": "请输入场景名称", "sceneDescription": "场景描述", "sceneDescriptionPlaceholder": "请输入场景描述", "deleteConfirmTitle": "删除项目", "deleteConfirmMessage": "确定要删除项目 \"{{name}}\" 吗？", "deleteConfirmWarning": "此操作不可撤销，项目中的所有数据将被永久删除。", "favorites": "收藏", "recent": "最近", "popular": "热门", "searchPlaceholder": "搜索项目...", "categoryFilter": "分类筛选", "difficultyFilter": "难度筛选", "sortBy": "排序方式", "sortByName": "按名称", "sortByDate": "按日期", "sortByPopularity": "按热度", "importProject": "导入项目", "exportProject": "导出项目"}}