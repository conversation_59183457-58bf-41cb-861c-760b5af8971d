/**
 * 增强资源面板组件
 * 集成AssetManagerService的完整资源管理功能
 */
import React, { useState, useEffect } from 'react';
import {
  List,
  Button,
  Input,
  Upload,
  Modal,
  message,
  Space,
  Tag,
  Tabs,
  Empty,
  Image,
  Progress} from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  AppstoreOutlined,
  BarsOutlined,
  FileOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  CodeOutlined} from '@ant-design/icons';
// import { useTranslation } from 'react-i18next'; // 暂时注释掉未使用的导入
import { useParams } from 'react-router-dom';
import AssetManagerService, { Asset, AssetType, AssetCategory } from '../../services/AssetManagerService';

const { Search } = Input;
const { TabPane } = Tabs;
const { Dragger } = Upload;

const EnhancedAssetsPanel: React.FC = () => {
  // const { t } = useTranslation(); // 暂时注释掉未使用的翻译函数
  const { projectId } = useParams<{ projectId: string }>();

  const [assets, setAssets] = useState<Asset[]>([]);
  const [categories, setCategories] = useState<AssetCategory[]>([]);
  const [filteredAssets, setFilteredAssets] = useState<Asset[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  // const [selectedAssets, setSelectedAssets] = useState<string[]>([]); // 恢复使用的状态
  const [previewAsset, setPreviewAsset] = useState<Asset | null>(null);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // 初始化资源管理器
  useEffect(() => {
    const initializeAssets = async () => {
      if (!projectId) return;
      
      setIsLoading(true);
      try {
        const assetManager = AssetManagerService.getInstance();
        
        // 加载项目资源
        const projectAssets = await assetManager.loadProjectAssets(`/projects/${projectId}`);
        const assetCategories = assetManager.getCategories();

        // 添加一些示例资源（如果没有资源的话）
        const sampleAssets: Asset[] = projectAssets.length === 0 ? [
          {
            id: 'sample-1',
            name: 'Pencil Box',
            type: AssetType.MODEL,
            format: 'FBX & GLB',
            path: '/assets/models/pencil-box.glb',
            size: 1024 * 512, // 512KB
            tags: ['furniture', 'office'],
            thumbnail: '/assets/thumbnails/pencil-box.jpg',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'sample-2',
            name: 'Studio x5000 Headphones',
            type: AssetType.MODEL,
            format: 'FBX & GLB',
            path: '/assets/models/headphones.glb',
            size: 1024 * 1024 * 2, // 2MB
            tags: ['electronics', 'audio'],
            thumbnail: '/assets/thumbnails/headphones.jpg',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'sample-3',
            name: 'Synthesizer Keyboard',
            type: AssetType.MODEL,
            format: 'FBX & GLB',
            path: '/assets/models/keyboard.glb',
            size: 1024 * 1024 * 3, // 3MB
            tags: ['music', 'instrument'],
            thumbnail: '/assets/thumbnails/keyboard.jpg',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'sample-4',
            name: 'Laptop Computer',
            type: AssetType.MODEL,
            format: 'FBX & GLB',
            path: '/assets/models/laptop.glb',
            size: 1024 * 1024 * 1.5, // 1.5MB
            tags: ['electronics', 'computer'],
            thumbnail: '/assets/thumbnails/laptop.jpg',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ] : projectAssets;

        setAssets(sampleAssets);
        setCategories(assetCategories);
        setFilteredAssets(sampleAssets);

        console.log('资源面板初始化完成，资源数量:', sampleAssets.length);
      } catch (error) {
        console.error('初始化资源失败:', error);
        message.error('加载资源失败');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAssets();
  }, [projectId]);

  // 过滤资源
  useEffect(() => {
    let filtered = assets;
    
    // 按分类过滤
    if (selectedCategory !== 'all') {
      const category = categories.find(cat => cat.id === selectedCategory);
      if (category) {
        filtered = filtered.filter(asset => category.types.includes(asset.type));
      }
    }
    
    // 按搜索查询过滤
    if (searchQuery) {
      filtered = filtered.filter(asset => 
        asset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        asset.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }
    
    setFilteredAssets(filtered);
  }, [assets, selectedCategory, searchQuery, categories]);

  // 获取资源图标 - 适配深色主题和更大尺寸
  const getAssetIcon = (type: AssetType) => {
    const iconStyle = { fontSize: '32px', color: '#666' };
    switch (type) {
      case AssetType.MODEL:
        return <AppstoreOutlined style={{ ...iconStyle, color: '#52c41a' }} />;
      case AssetType.TEXTURE:
        return <PictureOutlined style={{ ...iconStyle, color: '#1890ff' }} />;
      case AssetType.AUDIO:
        return <AudioOutlined style={{ ...iconStyle, color: '#fa8c16' }} />;
      case AssetType.VIDEO:
        return <VideoCameraOutlined style={{ ...iconStyle, color: '#eb2f96' }} />;
      case AssetType.SCRIPT:
        return <CodeOutlined style={{ ...iconStyle, color: '#722ed1' }} />;
      default:
        return <FileOutlined style={iconStyle} />;
    }
  };

  // 获取文件大小显示
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 处理文件上传
  const handleUpload = async (file: File) => {
    try {
      setUploadProgress(0);
      const assetManager = AssetManagerService.getInstance();
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const newAsset = await assetManager.importAsset(file, {
        generateThumbnail: true,
        optimizeForWeb: true,
        compressionLevel: 0.8,
        generateMipmaps: true
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (newAsset) {
        setAssets(prev => [...prev, newAsset]);
        message.success(`资源 "${newAsset.name}" 导入成功`);
      } else {
        message.error('资源导入失败');
      }

      setTimeout(() => {
        setUploadProgress(0);
        setUploadModalVisible(false);
      }, 1000);

      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败');
      setUploadProgress(0);
      return false;
    }
  };

  // 删除资源
  const handleDeleteAsset = (assetId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个资源吗？此操作不可撤销。',
      onOk: () => {
        const assetManager = AssetManagerService.getInstance();
        if (assetManager.deleteAsset(assetId)) {
          setAssets(prev => prev.filter(asset => asset.id !== assetId));
          // setSelectedAssets(prev => prev.filter(id => id !== assetId));
          message.success('资源删除成功');
        } else {
          message.error('资源删除失败');
        }
      }
    });
  };

  // 预览资源
  const handlePreviewAsset = async (asset: Asset) => {
    try {
      const assetManager = AssetManagerService.getInstance();
      const data = await assetManager.loadAssetData(asset);
      
      if (data) {
        setPreviewAsset(asset);
        console.log('预览资源:', asset.name, data);
      } else {
        message.error('无法预览此资源');
      }
    } catch (error) {
      console.error('预览失败:', error);
      message.error('预览失败');
    }
  };

  // 渲染网格视图 - 深色主题，类似图片中的样式
  const renderGridView = () => (
    <div style={{
      display: 'flex',
      flexWrap: 'wrap',
      gap: '12px',
      padding: '12px',
      background: '#1e1e1e'
    }}>
      {filteredAssets.map((asset) => (
        <div
          key={asset.id}
          style={{
            width: '120px',
            height: '140px',
            background: '#2a2a2a',
            border: '1px solid #444',
            borderRadius: '6px',
            cursor: 'pointer',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '8px',
            transition: 'all 0.2s',
            color: '#fff'
          }}
          onClick={() => handlePreviewAsset(asset)}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = '#3a3a3a';
            e.currentTarget.style.borderColor = '#555';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = '#2a2a2a';
            e.currentTarget.style.borderColor = '#444';
          }}
        >
          <div style={{
            width: '80px',
            height: '80px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '8px',
            background: '#1a1a1a',
            borderRadius: '4px',
            overflow: 'hidden'
          }}>
            {asset.thumbnail ? (
              <Image
                src={asset.thumbnail}
                alt={asset.name}
                width={76}
                height={76}
                style={{ objectFit: 'cover', borderRadius: 4 }}
                fallback="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='76' height='76'%3E%3Crect width='76' height='76' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%23999'%3E?%3C/text%3E%3C/svg%3E"
                preview={false}
              />
            ) : (
              getAssetIcon(asset.type)
            )}
          </div>
          <div style={{
            fontSize: '12px',
            textAlign: 'center',
            width: '100%',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            marginBottom: '2px'
          }}>
            {asset.name}
          </div>
          <div style={{
            fontSize: '10px',
            color: '#888',
            textAlign: 'center'
          }}>
            {asset.format || asset.type}
          </div>
        </div>
      ))}
    </div>
  );

  // 渲染列表视图
  const renderListView = () => (
    <List
      dataSource={filteredAssets}
      loading={isLoading}
      renderItem={(asset) => (
        <List.Item
          actions={[
            <Button type="text" icon={<EyeOutlined />} onClick={() => handlePreviewAsset(asset)} />,
            <Button type="text" icon={<DeleteOutlined />} danger onClick={() => handleDeleteAsset(asset.id)} />
          ]}
        >
          <List.Item.Meta
            avatar={getAssetIcon(asset.type)}
            title={asset.name}
            description={
              <Space>
                <Tag>{asset.type}</Tag>
                {asset.size && <span>{formatFileSize(asset.size)}</span>}
                {asset.format && <span>{asset.format.toUpperCase()}</span>}
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      background: '#1e1e1e',
      color: '#fff'
    }}>
      {/* 工具栏 */}
      <div style={{
        padding: '8px',
        borderBottom: '1px solid #444',
        background: '#2a2a2a'
      }}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Search
            placeholder="搜索资源..."
            allowClear
            style={{ width: 200 }}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Space>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
              size="small"
            >
              上传资源
            </Button>
            <Button
              type={viewMode === 'grid' ? 'primary' : 'default'}
              icon={<AppstoreOutlined />}
              onClick={() => setViewMode('grid')}
              size="small"
            />
            <Button
              type={viewMode === 'list' ? 'primary' : 'default'}
              icon={<BarsOutlined />}
              onClick={() => setViewMode('list')}
              size="small"
            />
          </Space>
        </Space>
      </div>

      {/* 分类标签 */}
      <div style={{
        background: '#2a2a2a',
        borderBottom: '1px solid #444'
      }}>
        <Tabs
          activeKey={selectedCategory}
          onChange={setSelectedCategory}
          size="small"
          style={{ padding: '0 8px' }}
        >
          <TabPane tab={`Models (${assets.length})`} key="all" />
          <TabPane tab="Characters" key="characters" />
          <TabPane tab="Environments" key="environments" />
          <TabPane tab="Props" key="props" />
        </Tabs>
      </div>

      {/* 资源列表 */}
      <div style={{ flex: 1, overflow: 'auto', background: '#1e1e1e' }}>
        {filteredAssets.length === 0 ? (
          <Empty
            description="暂无资源"
            style={{ color: '#888' }}
          />
        ) : (
          viewMode === 'grid' ? renderGridView() : renderListView()
        )}
      </div>

      {/* 上传模态框 */}
      <Modal
        title="导入资源"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Dragger
          multiple
          beforeUpload={handleUpload}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持模型、纹理、音频、视频等多种格式
          </p>
        </Dragger>
        
        {uploadProgress > 0 && (
          <Progress 
            percent={uploadProgress} 
            style={{ marginTop: 16 }}
          />
        )}
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title={previewAsset?.name}
        open={!!previewAsset}
        onCancel={() => setPreviewAsset(null)}
        footer={null}
        width={600}
      >
        {previewAsset && (
          <div style={{ textAlign: 'center' }}>
            {previewAsset.type === AssetType.TEXTURE && previewAsset.thumbnail && (
              <Image src={previewAsset.thumbnail} alt={previewAsset.name} />
            )}
            {previewAsset.type === AssetType.AUDIO && (
              <audio controls style={{ width: '100%' }}>
                <source src={previewAsset.path} />
              </audio>
            )}
            {previewAsset.type === AssetType.VIDEO && (
              <video controls style={{ width: '100%', maxHeight: '400px' }}>
                <source src={previewAsset.path} />
              </video>
            )}
            <div style={{ marginTop: 16, textAlign: 'left' }}>
              <p><strong>类型:</strong> {previewAsset.type}</p>
              <p><strong>格式:</strong> {previewAsset.format}</p>
              {previewAsset.size && (
                <p><strong>大小:</strong> {formatFileSize(previewAsset.size)}</p>
              )}
              {previewAsset.tags && previewAsset.tags.length > 0 && (
                <p><strong>标签:</strong> {previewAsset.tags.map(tag => (
                  <Tag key={tag} style={{ marginLeft: 4 }}>{tag}</Tag>
                ))}</p>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EnhancedAssetsPanel;
