{"asset": {"generator": "IREngine.SceneExporter", "version": "2.0"}, "nodes": [{"name": "Settings", "extensions": {"EE_uuid": {"entityID": "53705e73-f0b4-4923-89b7-0719e64865a0"}, "EE_envmapbake": {"bakePosition": {"x": 0, "y": 0, "z": 0}, "bakePositionOffset": {"x": 0, "y": 0, "z": 0}, "bakeScale": {"x": 1, "y": 1, "z": 1}, "bakeType": "Baked", "resolution": 1024, "refreshMode": "OnAwake", "envMapOrigin": "__$project$__/ir-engine/default-project/public/scenes/sky-station.envmap.ktx2", "boxProjection": true}, "EE_camera_settings": {"fov": 50, "cameraNearClip": 0.1, "cameraFarClip": 1000, "projectionType": 1, "minCameraDistance": 1, "maxCameraDistance": 50, "startCameraDistance": 3, "cameraMode": "FOLLOW", "minPhi": -70, "maxPhi": 85}, "EE_fog": {"type": "disabled", "color": "#FFFFFF", "density": 0.005, "near": 1, "far": 1000, "timeScale": 1, "height": 0.05}, "EE_render_settings": {"primaryLight": "e05a3e9b-755b-47b7-b79c-a148a1e21b77", "csm": true, "cascades": 5, "toneMapping": 1, "toneMappingExposure": 0.8, "shadowMapType": 2}, "EE_visible": {}, "EE_scene_settings": {"thumbnailURL": "__$project$__/ir-engine/default-project/public/scenes/sky-station.thumbnail.jpg", "loadingScreenURL": "__$project$__/ir-engine/default-project/public/scenes/sky-station.loadingscreen.ktx2", "primaryColor": "#0A6493", "backgroundColor": "rgb(238, 232, 243)", "alternativeColor": "#316F9E", "sceneKillHeight": -10, "spectateEntity": ""}}}, {"name": "scene preview camera", "matrix": [0.18830433415271125, -5.176414852314788e-15, -0.9821107258040217, 0, -0.2134127570711327, 0.9761048444515633, -0.04091855028574257, 0, 0.9586430372451468, 0.21730009810901849, 0.1838047727976863, 0, 52.88653533502665, 8.772787203317975, 2.223495539796688, 1], "extensions": {"EE_uuid": {"entityID": "1e8b1451-a87f-4cd4-8556-d0732f328a3c"}, "EE_scene_preview_camera": {}}}, {"name": "Skybox", "extensions": {"EE_uuid": {"entityID": "35621369-7e83-4878-953c-f30bf81dc775"}, "EE_skybox": {"backgroundColor": 0, "equirectangularPath": "__$project$__/ir-engine/default-project/assets/sky_skybox.ktx2?hash=93b6d8", "cubemapPath": "", "backgroundType": 2, "skyboxProps": {"turbidity": 10, "rayleigh": 1, "luminance": 1, "mieCoefficient": 0.004999999999999893, "mieDirectionalG": 0.99, "inclination": 0.10471975511965978, "azimuth": 0.16666666666666666}}, "EE_visible": {}}}, {"name": "spawn point", "matrix": [2.220446049250313e-16, 0, 1, 0, 0, 1, 0, 0, -1, 0, 2.220446049250313e-16, 0, 44.5, 2, 0, 1], "extensions": {"EE_uuid": {"entityID": "2b68a123-8a48-4258-a985-69a36679b4ef"}, "EE_spawn_point": {"permissionedUsers": []}}}, {"name": "directional-light", "matrix": [0.7371876386306472, 0.002334607738053649, 0.6756840497280369, 0, -0.24818026883421135, 0.9310325030813662, 0.26755379340877294, 0, -0.6284591789540106, -0.36492879827817815, 0.6869250560109855, 0, 36, 13.5, 0, 1], "extensions": {"EE_uuid": {"entityID": "e05a3e9b-755b-47b7-b79c-a148a1e21b77"}, "EE_visible": {}, "EE_directional_light": {"color": 16777215, "intensity": 6, "castShadow": true, "shadowBias": -5e-05, "shadowRadius": 1, "cameraFar": 100}}}, {"name": "Model", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -6.099999904632568, 1.2999999523162842, 0, 1], "extensions": {"EE_uuid": {"entityID": "bd83d58b-80e4-40a2-9a1d-d45aaee0c76a"}, "EE_shadow": {"cast": true, "receive": true}, "EE_model": {"src": "__$project$__/ir-engine/default-project/assets/Skybase.glb", "cameraOcclusion": true, "applyColliders": false, "shape": "box"}, "EE_loop_animation": {"activeClipIndex": -1, "animationPack": "", "useVRM": false, "enabled": true, "paused": false, "time": 0, "timeScale": 1, "blendMode": 2500, "loop": 2201, "clampWhenFinished": false, "zeroSlopeAtStart": true, "zeroSlopeAtEnd": true, "weight": 1}, "EE_envmap": {"type": "Skybox", "envMapSourceColor": 1193046, "envMapSourceURL": "__$project$__/ir-engine/default-project/assets/sky_skybox.jpg", "envMapCubemapURL": "", "envMapSourceEntityUUID": "", "envMapIntensity": 1.5}, "EE_visible": {}}}, {"name": "Portal - to Apartment", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 49.5, 3, 0, 1], "extensions": {"EE_uuid": {"entityID": "6a1cf0d6-ddfe-4058-b5ca-5bb18bc7efae"}, "EE_shadow": {"cast": true, "receive": true}, "EE_portal": {"linkedPortalId": "da8a8f5a-7a72-4dae-acef-9ef013cb31b6", "location": "apartment", "effectType": "Hyperspace", "previewType": "Spherical", "previewImageURL": "__$project$__/ir-engine/default-project/public/scenes/sky-station-Portal--%20to%20Apartment.ktx2", "redirect": false, "spawnPosition": {"x": 49.5, "y": 2, "z": 0}, "spawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}, "remoteSpawnPosition": {"x": 0, "y": 0, "z": 0}, "remoteSpawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}}, "EE_visible": {}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "sphere", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 0.5, "collisionLayer": 8, "collisionMask": 2, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "teleport", "onExit": "", "target": ""}]}}}, {"name": "Portal - Sky Station Interior", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 15, 9, -5, 1], "extensions": {"EE_uuid": {"entityID": "dd1eafe4-dc87-4353-b3ae-567d4f81c032"}, "EE_shadow": {"cast": true, "receive": true}, "EE_portal": {"linkedPortalId": "2d7f9f97-20a0-40e8-a504-682f650af501", "location": "", "effectType": "None", "previewType": "Spherical", "previewImageURL": "__$project$__/ir-engine/default-project/public/scenes/sky-station-Portal--%20Sky%20Station%20Interior.ktx2", "redirect": false, "spawnPosition": {"x": 15, "y": 9, "z": -5}, "spawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}, "remoteSpawnPosition": {"x": 0, "y": 0, "z": 0}, "remoteSpawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}}, "EE_visible": {}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "sphere", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 0.5, "collisionLayer": 8, "collisionMask": 2, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "teleport", "onExit": "", "target": ""}]}}}, {"name": "Portal - Sky Station Exterior", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -60, 3, 0, 1], "extensions": {"EE_uuid": {"entityID": "2d7f9f97-20a0-40e8-a504-682f650af501"}, "EE_shadow": {"cast": true, "receive": true}, "EE_portal": {"linkedPortalId": "dd1eafe4-dc87-4353-b3ae-567d4f81c032", "location": "", "effectType": "None", "previewType": "Spherical", "previewImageURL": "__$project$__/ir-engine/default-project/public/scenes/sky-station-Portal--%20Sky%20Station%20Exterior.ktx2", "redirect": false, "spawnPosition": {"x": -60, "y": 3, "z": 0}, "spawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}, "remoteSpawnPosition": {"x": 0, "y": 0, "z": 0}, "remoteSpawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}}, "EE_visible": {}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "sphere", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 0.5, "collisionLayer": 8, "collisionMask": 2, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "teleport", "onExit": "", "target": ""}]}}}, {"name": "postprocessing", "extensions": {"EE_uuid": {"entityID": "10eb1bb2-5cf2-4b4e-abab-3132b3a995fa"}, "EE_visible": {}, "EE_postprocessing": {"enabled": true, "effects": {"SSREffect": {"isActive": false, "distance": 10, "thickness": 10, "denoiseIterations": 1, "denoiseKernel": 2, "denoiseDiffuse": 10, "denoiseSpecular": 10, "radius": 3, "phi": 0.5, "lumaPhi": 5, "depthPhi": 2, "normalPhi": 50, "roughnessPhi": 50, "specularPhi": 50, "envBlur": 0.5, "importanceSampling": true, "steps": 20, "refineSteps": 5, "resolutionScale": 1, "missedRays": false}, "SSGIEffect": {"isActive": false, "distance": 10, "thickness": 10, "denoiseIterations": 1, "denoiseKernel": 2, "denoiseDiffuse": 10, "denoiseSpecular": 10, "radius": 3, "phi": 0.5, "lumaPhi": 5, "depthPhi": 2, "normalPhi": 50, "roughnessPhi": 50, "specularPhi": 50, "envBlur": 0.5, "importanceSampling": true, "steps": 20, "refineSteps": 5, "resolutionScale": 1, "missedRays": false}, "SSAOEffect": {"isActive": true, "blendFunction": 21, "distanceScaling": true, "depthAwareUpsampling": true, "samples": 9, "rings": 7, "distanceThreshold": 0.125, "distanceFalloff": 0.02, "rangeThreshold": 0.0005, "rangeFalloff": 0.001, "minRadiusScale": 0.1, "luminanceInfluence": 0.7, "bias": 0.025, "radius": 0.15, "intensity": 6, "fade": 0.01, "resolutionScale": 1, "resolutionX": -1, "resolutionY": -1, "width": -1, "height": -1, "kernelSize": 2, "blur": true}, "DepthOfFieldEffect": {"isActive": false, "blendFunction": 23, "focusDistance": 0.1, "focalLength": 0.1, "focusRange": 0.1, "bokehScale": 1, "resolutionScale": 1, "resolutionX": -1, "resolutionY": -1}, "BloomEffect": {"isActive": true, "blendFunction": 28, "kernelSize": 3, "luminanceThreshold": 0.9, "luminanceSmoothing": 0.025, "mipmapBlur": true, "intensity": 1.5, "radius": 0.89, "levels": 4}, "ToneMappingEffect": {"isActive": false, "blendFunction": 23, "adaptive": false, "mode": 5, "resolution": 256, "maxLuminance": 4, "whitePoint": 4, "middleGrey": 0.6, "minLuminance": 0.01, "averageLuminance": 1, "adaptationRate": 1}, "BrightnessContrastEffect": {"isActive": false, "blendFunction": 23, "brightness": 0, "contrast": 0}, "HueSaturationEffect": {"isActive": false, "blendFunction": 23, "hue": 0, "saturation": 0}, "ColorDepthEffect": {"isActive": false, "blendFunction": 23, "bits": 16}, "LinearTosRGBEffect": {"isActive": false}, "TRAAEffect": {"isActive": false, "blend": 0.8, "constantBlend": true, "dilation": true, "blockySampling": false, "logTransform": false, "depthDistance": 10, "worldDistance": 5, "neighborhoodClamping": true}, "MotionBlurEffect": {"isActive": false, "intensity": 1, "jitter": 1, "samples": 16}, "VignetteEffect": {"isActive": false, "blendFunction": 23, "technique": 0, "eskil": false, "offset": 0.5, "darkness": 0.5}, "LUT3DEffect": {"isActive": false}}}}}], "scene": 0, "scenes": [{"nodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "extensionsUsed": ["EE_uuid", "EE_envmapbake", "EE_camera_settings", "EE_fog", "EE_render_settings", "EE_visible", "EE_scene_settings", "EE_scene_preview_camera", "EE_skybox", "EE_spawn_point", "EE_directional_light", "E<PERSON>_shadow", "EE_model", "EE_loop_animation", "EE_envmap", "EE_portal", "EE_rigidbody", "EE_collider", "EE_trigger", "EE_postprocessing"]}