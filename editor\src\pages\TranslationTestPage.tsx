/**
 * 翻译测试页面
 * 用于测试地形编辑器相关的翻译是否正常工作
 */
import React from 'react';
import { Card, Typography, Space, Divider, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { addEntity, selectEntity } from '../store/scene/sceneSlice';
import { Entity } from '../store/scene/sceneSlice';

const { Title, Text } = Typography;

/**
 * 翻译测试页面
 */
const TranslationTestPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 创建测试实体数据
  const createTestEntities = () => {
    const testEntities: Entity[] = [
      {
        id: 'entity-1',
        name: '地形实体',
        type: 'terrain',
        parentId: null,
        visible: true,
        locked: false,
        transform: {
          position: [0, 0, 0],
          rotation: [0, 0, 0],
          scale: [1, 1, 1]
        },
        components: {
          TerrainComponent: {
            width: 1000,
            height: 1000,
            resolution: 256,
            maxHeight: 100
          }
        }
      },
      {
        id: 'entity-2',
        name: '光源',
        type: 'light',
        parentId: null,
        visible: true,
        locked: false,
        transform: {
          position: [10, 10, 10],
          rotation: [0, 0, 0],
          scale: [1, 1, 1]
        },
        components: {}
      },
      {
        id: 'entity-3',
        name: '相机',
        type: 'camera',
        parentId: null,
        visible: true,
        locked: false,
        transform: {
          position: [0, 5, 10],
          rotation: [0, 0, 0],
          scale: [1, 1, 1]
        },
        components: {}
      }
    ];

    testEntities.forEach(entity => {
      dispatch(addEntity(entity));
    });

    // 选择第一个实体
    dispatch(selectEntity('entity-1'));
  };

  const testKeys = [
    'editor.common.entities',
    'editor.common.properties', 
    'editor.common.noEntitySelected',
    'editor.common.noEntities',
    'editor.common.searchEntities',
    'editor.common.addEntity',
    'editor.common.noSearchResults',
    'editor.common.hide',
    'editor.common.show',
    'editor.common.lock',
    'editor.common.unlock',
    'editor.common.basicInfo',
    'editor.common.nameRequired',
    'editor.common.enterName',
    'editor.common.transform',
    'editor.common.components',
    'editor.common.noComponents',
    'editor.common.addComponent',
    'editor.common.componentConfigured',
    'editor.terrain.terrainEditor',
    'editor.terrain.noTerrainComponent',
    'editor.terrain.addTerrainComponent',
    'editor.terrain.terrainComponentAdded',
    'editor.terrain.addTerrainComponentFailed'
  ];

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>地形编辑器翻译测试</Title>
      <Text type="secondary">
        此页面用于测试地形编辑器相关的中文翻译是否正常工作
      </Text>

      <Divider />

      <Card title="测试操作" size="small" style={{ marginBottom: '16px' }}>
        <Space>
          <Button type="primary" onClick={createTestEntities}>
            创建测试实体数据
          </Button>
          <Button onClick={() => window.open('/terrain-editor', '_blank')}>
            打开地形编辑器
          </Button>
        </Space>
      </Card>
      
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="通用翻译键测试" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {testKeys.filter(key => key.includes('common')).map(key => (
              <div key={key} style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text code>{key}</Text>
                <Text>{t(key)}</Text>
              </div>
            ))}
          </Space>
        </Card>
        
        <Card title="地形编辑器翻译键测试" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {testKeys.filter(key => key.includes('terrain')).map(key => (
              <div key={key} style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text code>{key}</Text>
                <Text>{t(key)}</Text>
              </div>
            ))}
          </Space>
        </Card>
        
        <Card title="基础翻译键测试" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text code>common.name</Text>
              <Text>{t('common.name')}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text code>common.type</Text>
              <Text>{t('common.type')}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text code>common.visible</Text>
              <Text>{t('common.visible')}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text code>common.position</Text>
              <Text>{t('common.position')}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text code>common.rotation</Text>
              <Text>{t('common.rotation')}</Text>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Text code>common.scale</Text>
              <Text>{t('common.scale')}</Text>
            </div>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default TranslationTestPage;
