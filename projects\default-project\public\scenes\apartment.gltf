{"asset": {"generator": "IREngine.SceneExporter", "version": "2.0"}, "nodes": [{"name": "Settings", "extensions": {"EE_uuid": {"entityID": "51d71835-68a8-430b-96f6-4c618b8ea407"}, "EE_envmapbake": {"bakePositionOffset": {"x": 0, "y": 0, "z": 0}, "bakeScale": {"x": 0, "y": 0, "z": 0}, "bakeType": "Baked", "resolution": 2048, "refreshMode": "OnAwake", "envMapOrigin": "__$project$__/ir-engine/default-project/public/scenes/apartment.envmap.ktx2", "boxProjection": false}, "EE_camera_settings": {"cameraNearClip": 0.1, "cameraFarClip": 1000, "projectionType": 1, "fov": 50, "cameraMode": "FOLLOW", "minPhi": -70, "maxPhi": 85, "isAvatarVisible": true, "followCameraScrollSensitivity": 1, "canCameraFirstPerson": true, "canCameraThirdPerson": true, "canCameraTopDown": true, "isFistPersonFreeCamera": true, "isThirdPersonFreeCamera": true, "isTopDownFreeCamera": false, "firstPersonCameraLimits": 360, "thirdPersonCameraLimits": 360, "topDownCameraLimits": 360, "isFirstPersonCameraReset": true, "isThirdPersonCameraReset": true, "isTopDownCameraReset": true, "thirdPersonMinDistance": 1.5, "thirdPersonMaxDistance": 50, "thirdPersonDefaultDistance": 3, "topDownMinDistance": 10, "topDownMaxDistance": 70, "topDownDefaultDistance": 40, "poiEntities": [], "poiLerpSpeed": 0.5, "scrollDeadzone": 1, "scrollSensitivity": 0.1, "scrollDistancePerPoi": 3, "scrollBehavior": "C<PERSON>", "poiScrollTransitionType": "Scrolling", "enableTransitionButtons": false}, "EE_fog": {"type": "disabled", "color": "#FFFFFF", "density": 0.005, "near": 1, "far": 1000, "timeScale": 1, "height": 0.05}, "EE_render_settings": {"primaryLight": "c4e80371-a13b-42f1-b5e1-6d61f572bb34", "csm": false, "cascades": 5, "toneMapping": 1, "toneMappingExposure": 0.8, "shadowMapType": 2}, "EE_visible": {}, "EE_scene_settings": {"thumbnailURL": "__$project$__/ir-engine/default-project/public/scenes/apartment.thumbnail.jpg", "loadingScreenURL": "__$project$__/ir-engine/default-project/public/scenes/apartment.loadingscreen.ktx2", "primaryColor": "#A8B6C6", "backgroundColor": "rgb(64, 70, 71)", "alternativeColor": "#8EBBAE", "sceneKillHeight": -10, "spectateEntity": ""}}}, {"name": "scene preview camera", "matrix": [0.9985554300035294, 2.276824562219555e-18, -0.05373130568358266, 0, 0.0010012117547803885, 0.9998263779823392, 0.018606758603761143, 0, 0.05372197674587839, -0.0186336762534007, 0.9983820587950272, 0, -2.3184722251170213, 1.6898777584130713, 8.731049954361012, 1], "extensions": {"EE_uuid": {"entityID": "a0a03866-c1d0-46d4-94ce-6f6bb0098c20"}, "EE_scene_preview_camera": {}, "EE_visible": {}}}, {"name": "Skybox", "extensions": {"EE_uuid": {"entityID": "86bda8f8-a9bc-4447-b92a-6514815a5b22"}, "EE_visible": {}, "EE_skybox": {"backgroundColor": 0, "equirectangularPath": "__$project$__/ir-engine/default-project/assets/apartment_skybox.ktx2?hash=6d6fe4", "cubemapPath": "", "backgroundType": 2, "skyboxProps": {"turbidity": 10.95, "rayleigh": 4, "luminance": 0.836, "mieCoefficient": 0, "mieDirectionalG": 0.442, "inclination": 0.5890311692555663, "azimuth": 0.5208333333333334}}}}, {"name": "ground plane", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0.019999999552965164, 0, 1], "extensions": {"EE_uuid": {"entityID": "45113aff-0bdb-4670-917a-dbf2a3b86bf0"}, "EE_ground_plane": {"color": 3561699, "visible": true}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "plane", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 0.5, "collisionLayer": 4, "collisionMask": 3, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}}}, {"name": "spawn point", "matrix": [-4.599999904632568, 5.633375159286406e-16, -5.633375159286405e-16, 0, 5.633375159286406e-16, 4.599999904632568, 5.633375159286406e-16, 0, 5.633375159286405e-16, 5.633375159286406e-16, -4.599999904632568, 0, -2.5, 0.5, 6, 1], "extensions": {"EE_uuid": {"entityID": "23816114-e405-47d7-bfff-0baa2339b773"}, "EE_visible": {}, "EE_spawn_point": {"permissionedUsers": []}}}, {"name": "hemisphere light", "extensions": {"EE_uuid": {"entityID": "1452a9a3-3271-4989-b341-90cf215325a3"}, "EE_visible": {}, "EE_hemisphere_light": {"skyColor": 16777215, "groundColor": 16777215, "intensity": 1}}}, {"name": "directional light", "matrix": [0.8972583704807328, 0.41190443398052057, 0.15894072438946266, 0, 1.8100639209706063e-08, 0.35999683542667743, -0.9329535242887386, 0, -0.4415058511529147, 0.8371003618144984, 0.32301016647742736, 0, 0.5, 0, 0.5, 1], "extensions": {"EE_uuid": {"entityID": "c4e80371-a13b-42f1-b5e1-6d61f572bb34"}, "EE_directional_light": {"color": 16777215, "intensity": 1, "castShadow": true, "shadowBias": -1e-05, "shadowRadius": 1, "cameraFar": 200}, "EE_visible": {}}}, {"name": "model", "matrix": [0.75, 0, 0, 0, 0, 0.75, 0, 0, 0, 0, 0.75, 0, 0, 0, 0, 1], "extensions": {"EE_uuid": {"entityID": "b46b57c1-bd37-43cc-b000-8b4411fe728b"}, "EE_visible": {}, "EE_model": {"src": "__$project$__/ir-engine/default-project/assets/apartment.glb", "cameraOcclusion": true, "applyColliders": false, "shape": "box"}, "EE_loop_animation": {"activeClipIndex": -1, "animationPack": "", "useVRM": false, "enabled": true, "paused": false, "time": 0, "timeScale": 1, "blendMode": 2500, "loop": 2201, "clampWhenFinished": false, "zeroSlopeAtStart": true, "zeroSlopeAtEnd": true, "weight": 1}, "EE_shadow": {"cast": false, "receive": false}, "EE_envmap": {"type": "Bake", "envMapSourceColor": 1193046, "envMapSourceURL": "", "envMapCubemapURL": "", "envMapSourceEntityUUID": "1f374932-e649-4b30-b6e7-de990fdee2a1", "envMapIntensity": 1}}}, {"name": "Box Collider 25", "matrix": [0.8799999952316284, 0, 0, 0, 0, 11.379999923706055, 0, 0, 0, 0, 28.25999984741211, 0, 8.400000381469727, 5.699999809265137, 2.499999999999999, 1], "extensions": {"EE_uuid": {"entityID": "46add0e6-cbc5-4caa-8845-03defdc40e29"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 26", "matrix": [0.5799999952316284, 0, 0, 0, 0, 11.979999923706055, 0, 0, 0, 0, 27.85999984741211, 0, -15.5, 5.699999809265137, 2.5, 1], "extensions": {"EE_uuid": {"entityID": "78a26534-fdd6-4a79-a7fe-97e83895640c"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 27", "matrix": [0.3800000011920929, 0, 0, 0, 0, 11.579999923706055, 0, 0, 0, 0, 23.8600004196167, 0, -9, 5.699999809265137, 5.5, 1], "extensions": {"EE_uuid": {"entityID": "1052a07b-414f-4887-a7aa-c70c972598b1"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 28", "matrix": [24.179999923706056, 0, 0, 0, 0, 11.679999923706056, 0, 0, 0, 0, 0.639999994635582, 0, -3.499999999999999, 5.699999809265137, -10.4, 1], "extensions": {"EE_uuid": {"entityID": "ab324600-7600-455f-a116-4ed957603b71"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 29", "matrix": [24.179999923706056, 0, 0, 0, 0, 11.579999923706055, 0, 0, 0, 0, 0.43999999463558204, 0, -3.5, 5.699999809265137, 16.5, 1], "extensions": {"EE_uuid": {"entityID": "c8992c75-4881-4186-a47f-614d2fa61c67"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 30", "matrix": [12.579999923706055, 0, 0, 0, 0, 11.479999923706055, 0, 0, 0, 0, 0.439999994635582, 0, -3, 5.699999809265137, 11, 1], "extensions": {"EE_uuid": {"entityID": "9c073abf-9ce3-4cd7-be90-b0ace33aca5c"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 31", "matrix": [0.9983418166418103, 2.776182378447577e-16, -0.05756402647774106, 0, -3.3299198020319258e-16, 1.200000023841858, 1.2196394734588236e-17, 0, 0.09785884569837647, 9.905599949421534e-18, 1.6971811001922392, 0, -3.4000000953674316, 0, 3, 1], "extensions": {"EE_uuid": {"entityID": "ac0c4391-8394-4601-85d5-64bca9df30dc"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "Box Collider 32", "matrix": [0.7940369210618097, 0, 0.09749547677036896, 0, 0, 1.2, 0, 0, -0.09749547677036896, 0, 0.7940369210618097, 0, -3.3722314834594727, 0, -2.3505780696868896, 1], "extensions": {"EE_uuid": {"entityID": "aee1caa8-e502-489b-aca7-425b14da4cf6"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_visible": {}}}, {"name": "EE_portal", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -12, 1, 14, 1], "extensions": {"EE_uuid": {"entityID": "da8a8f5a-7a72-4dae-acef-9ef013cb31b6"}, "EE_shadow": {"cast": true, "receive": true}, "EE_portal": {"linkedPortalId": "6a1cf0d6-ddfe-4058-b5ca-5bb18bc7efae", "location": "sky-station", "effectType": "Hyperspace", "previewType": "Spherical", "previewImageURL": "__$project$__/ir-engine/default-project/public/scenes/apartment-Portal.ktx2", "redirect": false, "spawnPosition": {"x": -12, "y": 1, "z": 14}, "spawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}, "remoteSpawnPosition": {"x": 0, "y": 0, "z": 0}, "remoteSpawnRotation": {"x": 0, "y": 0, "z": 0, "w": 1}}, "EE_visible": {}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "sphere", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 0.5, "collisionLayer": 8, "collisionMask": 2, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "teleport", "onExit": "", "target": ""}]}}}, {"name": "Trigger Volume", "extensions": {"EE_uuid": {"entityID": "0f5b5b02-67f9-4682-b687-d8ef95448c16"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 7, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "", "onExit": "", "target": ""}]}, "EE_visible": {}}}, {"name": "Door", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -9, 1.5, -8, 1], "extensions": {"EE_uuid": {"entityID": "d8a020bd-0795-44f2-b00a-97c6bc9984a7"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 8, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "", "onExit": "", "target": ""}]}, "EE_visible": {}}}, {"name": "Model", "matrix": [0.0010000000474974513, 0, 0, 0, 0, 0.0010000000474974513, 0, 0, 0, 0, 0.0010000000474974513, 0, -1, 0.5, 4, 1], "extensions": {"EE_uuid": {"entityID": "61304262-c00c-4f1f-80a2-e4163d97a007"}, "EE_visible": {}, "EE_shadow": {"cast": true, "receive": true}, "EE_model": {"src": "__$project$__/ir-engine/default-project/assets/keycard.glb", "cameraOcclusion": false, "applyColliders": false, "shape": "box"}, "EE_loop_animation": {"activeClipIndex": -1, "animationPack": "", "useVRM": false, "enabled": true, "paused": false, "time": 0, "timeScale": 1, "blendMode": 2500, "loop": 2201, "clampWhenFinished": false, "zeroSlopeAtStart": true, "zeroSlopeAtEnd": true, "weight": 1}, "EE_envmap": {"type": "Skybox", "envMapSourceColor": 1193046, "envMapSourceURL": "__$project$__/ir-engine/default-project/assets/skyboxsun25deg/", "envMapCubemapURL": "", "envMapSourceEntityUUID": "", "envMapIntensity": 1}}}, {"name": "<PERSON><PERSON>ey Trigger Volume", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -1, 0, 4, 1], "extensions": {"EE_uuid": {"entityID": "9d06e7cb-bd8f-4537-80ad-d62dcc8b89c3"}, "EE_rigidbody": {"type": "fixed", "ccd": false, "allowRolling": true, "enabledRotations": [true, true, true], "canSleep": true, "gravityScale": 1}, "EE_collider": {"shape": "box", "mass": 1, "massCenter": {"x": 0, "y": 0, "z": 0}, "friction": 0.5, "restitution": 1, "collisionLayer": 1, "collisionMask": 8, "matchMesh": true, "centerOffset": {"x": 0, "y": 0, "z": 0}, "boxSize": {"x": 1, "y": 1, "z": 1}, "radius": 1, "height": 2}, "EE_trigger": {"triggers": [{"onEnter": "", "onExit": "", "target": ""}]}, "EE_visible": {}}}, {"name": "New EnvMap Bake", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.7826329664618463, 2, 0.5285113797429986, 1], "extensions": {"EE_uuid": {"entityID": "1f374932-e649-4b30-b6e7-de990fdee2a1"}, "EE_envmapbake": {"bakePositionOffset": {"x": 0, "y": 4, "z": 0}, "bakeScale": {"x": 17.200000000000003, "y": 12, "z": 20.5}, "bakeType": "Baked", "resolution": 1024, "refreshMode": "OnAwake", "envMapOrigin": "__$project$__/ir-engine/default-project/public/scenes/apartment.gltf-New-EnvMap%20Bake.ktx2?hash=876f20", "boxProjection": true}, "EE_visible": {}}}, {"name": "EE_mount_point", "matrix": [2.220446049250313e-16, 0, 1, 0, 0, 1, 0, 0, -1, 0, 2.220446049250313e-16, 0, -3.4, 0.5, -2.4, 1], "extensions": {"EE_uuid": {"entityID": "4e6a56be-a4d8-4091-a741-10fbefaea21d"}, "EE_mount_point": {"type": "seat", "dismountOffset": {"x": 0, "y": 0, "z": 1}, "forceDismountPosition": false}, "EE_visible": {}}}, {"name": "postprocessing", "extensions": {"EE_uuid": {"entityID": "10eb1bb2-5cf2-4b4e-abab-3132b3a995fa"}, "EE_visible": {}, "EE_postprocessing": {"enabled": true, "effects": {"SSREffect": {"isActive": false, "distance": 10, "thickness": 10, "denoiseIterations": 1, "denoiseKernel": 2, "denoiseDiffuse": 10, "denoiseSpecular": 10, "radius": 3, "phi": 0.5, "lumaPhi": 5, "depthPhi": 2, "normalPhi": 50, "roughnessPhi": 50, "specularPhi": 50, "envBlur": 0.5, "importanceSampling": true, "steps": 20, "refineSteps": 5, "resolutionScale": 1, "missedRays": false}, "SSGIEffect": {"isActive": false, "distance": 10, "thickness": 10, "denoiseIterations": 1, "denoiseKernel": 2, "denoiseDiffuse": 10, "denoiseSpecular": 10, "radius": 3, "phi": 0.5, "lumaPhi": 5, "depthPhi": 2, "normalPhi": 50, "roughnessPhi": 50, "specularPhi": 50, "envBlur": 0.5, "importanceSampling": true, "steps": 20, "refineSteps": 5, "resolutionScale": 1, "missedRays": false}, "SSAOEffect": {"isActive": false, "blendFunction": 21, "distanceScaling": true, "depthAwareUpsampling": true, "samples": 9, "rings": 7, "distanceThreshold": 0.125, "distanceFalloff": 0.02, "rangeThreshold": 0.0005, "rangeFalloff": 0.001, "minRadiusScale": 0.1, "luminanceInfluence": 0.7, "bias": 0.025, "radius": 0.1825, "intensity": 1, "fade": 0.01, "resolutionScale": 1, "resolutionX": -1, "resolutionY": -1, "width": -1, "height": -1, "kernelSize": 1, "blur": true}, "DepthOfFieldEffect": {"isActive": false, "blendFunction": 23, "focusDistance": 0.1, "focalLength": 0.1, "focusRange": 0.1, "bokehScale": 1, "resolutionScale": 1, "resolutionX": -1, "resolutionY": -1}, "BloomEffect": {"isActive": true, "blendFunction": 28, "kernelSize": 3, "luminanceThreshold": 0.9, "luminanceSmoothing": 0.025, "mipmapBlur": true, "intensity": 1.82, "radius": 0.85, "levels": 3}, "ToneMappingEffect": {"isActive": false, "blendFunction": 23, "adaptive": false, "mode": 5, "resolution": 256, "maxLuminance": 4, "whitePoint": 4, "middleGrey": 0.6, "minLuminance": 0.01, "averageLuminance": 1, "adaptationRate": 1}, "BrightnessContrastEffect": {"isActive": false, "blendFunction": 23, "brightness": 0, "contrast": 0}, "HueSaturationEffect": {"isActive": false, "blendFunction": 23, "hue": 0, "saturation": 0}, "ColorDepthEffect": {"isActive": false, "blendFunction": 23, "bits": 16}, "LinearTosRGBEffect": {"isActive": false}, "TRAAEffect": {"isActive": false, "blend": 0.8, "constantBlend": true, "dilation": true, "blockySampling": false, "logTransform": false, "depthDistance": 10, "worldDistance": 5, "neighborhoodClamping": true}, "MotionBlurEffect": {"isActive": false, "intensity": 1, "jitter": 1, "samples": 16}, "VignetteEffect": {"isActive": false, "blendFunction": 23, "technique": 0, "eskil": false, "offset": 0.5, "darkness": 0.5}}}}}], "scene": 0, "scenes": [{"nodes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]}], "extensionsUsed": ["EE_uuid", "EE_envmapbake", "EE_camera_settings", "EE_fog", "EE_render_settings", "EE_visible", "EE_scene_settings", "EE_scene_preview_camera", "EE_skybox", "EE_ground_plane", "EE_rigidbody", "EE_collider", "EE_spawn_point", "EE_hemisphere_light", "EE_directional_light", "EE_model", "EE_loop_animation", "E<PERSON>_shadow", "EE_envmap", "EE_portal", "EE_trigger", "EE_mount_point", "EE_postprocessing"]}