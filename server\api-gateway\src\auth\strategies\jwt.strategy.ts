/**
 * JWT认证策略
 */
import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET', 'your-secret-key'),
    });
  }

  async validate(payload: any) {
    try {
      // 检查是否是开发环境的token
      if (process.env.NODE_ENV === 'development' && payload.signature === 'dev-signature') {
        console.log('开发环境：使用开发token验证');
        return {
          id: payload.sub || 'dev-user-123',
          username: payload.username || 'developer',
          email: payload.email || '<EMAIL>',
          role: payload.role || 'admin'
        };
      }

      // 尝试从用户服务获取用户信息
      const user = await this.authService.validateJwt(payload);
      return user;
    } catch (error) {
      // 如果用户服务不可用，返回简化的用户对象
      console.warn('用户服务验证失败，使用简化用户对象:', error.message);
      return {
        id: payload.sub || payload.userId || 'default-user',
        username: payload.username || 'user',
        email: payload.email || '<EMAIL>',
        role: payload.role || 'user'
      };
    }
  }
}
