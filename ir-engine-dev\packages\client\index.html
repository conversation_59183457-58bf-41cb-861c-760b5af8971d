<!doctype html>
<html lang="en">
  <head>
    <style>
      html,
      body {
        margin: 0;
        width: 100%;
        height: 100%;
      }
    </style>
    <base href="/" />
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover"
    />

    <meta name="description" content="<%- description %>" />
    <title><%- title %></title>

    <meta name="monetization" content="<%- paymentPointer %>" />

    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="preconnect" crossorigin="anonymous" href="https://fonts.googleapis.com" />
    <link rel="preconnect" crossorigin="anonymous" href="https://fonts.gstatic.com" />
    <link
      rel="stylesheet"
      crossorigin="anonymous"
      href="https://fonts.googleapis.com/css?family=Lato:300,400,700|DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000|Manrope:wght@200..800|PT+Sans:400,400italic,700,700italic|Quicksand:400,300|Raleway:400|Roboto:300,400,700|Source+Sans+Pro:300,400,700|Khula:300,400,700&display=swap"
    />

    <link rel="stylesheet" type="text/css" href="/src/themes/base.css" />
    <link rel="stylesheet" type="text/css" href="/src/themes/components.css" />
    <link rel="stylesheet" type="text/css" href="/src/themes/utilities.css" />

    <link rel="apple-touch-icon" sizes="180x180" href="<%- appleTouchIcon %>" />
    <link rel="manifest" href="<%- siteManifest %>" />
    <link rel="mask-icon" href="<%- safariPinnedTab %>" color="#5bbad5" />
    <link rel="icon" type="image/png" sizes="32x32" href="<%- favicon32px %>" />
    <link rel="icon" type="image/png" sizes="16x16" href="<%- favicon16px %>" />
    <link rel="shortcut icon" href="<%- favicon %>" />
    <meta name="msapplication-TileColor" content="#da532c" />
    <meta name="theme-color" content="#000000" />

    <script type="text/javascript">
      var global = globalThis
      var exports = { __esModule: true }

      if ('serviceWorker' in navigator && `<%- swScriptLink %>` !== '') {
        window.addEventListener('load', () => {
          console.log(`[PWA] Registering sw at ${window.location.origin}/<%- swScriptLink %>`)

          // Register the service worker
          navigator.serviceWorker
            .register(`${window.location.origin}/<%- swScriptLink %>`, {
              scope: `./`
            })
            .then(function (reg) {
              console.log('[PWA] SW registered at ' + reg.scope)
            })
        })
      }

      //Base height and width of cross-origin iframe
      const w = 400
      const h = 400

      const width = window.innerWidth
        ? window.innerWidth
        : document.documentElement.clientWidth
        ? document.documentElement.clientWidth
        : screen.width
      const height = window.innerHeight
        ? window.innerHeight
        : document.documentElement.clientHeight
        ? document.documentElement.clientHeight
        : screen.height

      const left = (width - w) / 2
      const topSetting = (height - h) / 2

      document.addEventListener('DOMContentLoaded', async (event) => {
        const rootIframe = document.getElementById('root-cookie-accessor')
        rootIframe.style.width = `${w}px`
        rootIframe.style.height = `${h}px`
        rootIframe.style.top = `${topSetting}px`
        rootIframe.style.left = `${left}px`
      })
    </script>
    <!-- Google Tag Manager -->
    <script>
      ;(function (w, d, s, l, i) {
        w[l] = w[l] || []
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : ''
        j.async = true
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl + '<%- gtmEnvironent %>'
        f.parentNode.insertBefore(j, f)
      })(window, document, 'script', 'dataLayer', '<%- gtmId %>')
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=<%- gtmId %><%- gtmEnvironent %>"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div
      id="root"
      style="z-index: 1; margin: 0; width: 100%; height: 100vh; position: absolute; pointer-events: none"
    ></div>
    <iframe
      style="
        display: none;
        visibility: hidden;
        height: 400px;
        width: 400px;
        pointer-events: auto;
        position: absolute;
        z-index: 100;
        max-width: 100%;
        max-height: 100%;
      "
      id="root-cookie-accessor"
      src="<%- rootCookieAccessor %>"
      sandbox="allow-storage-access-by-user-activation allow-scripts allow-same-origin"
    ></iframe>
    <script type="module" src="/src/main.tsx"></script>
    <canvas
      hidden
      id="engine-renderer-canvas"
      tabindex="1"
      style="
        outline: none;
        z-index: 0;
        width: 100%;
        height: 100%;
        position: fixed;
        -webkit-user-select: none;
        pointer-events: auto;
        user-select: none;
      "
    ></canvas>
  </body>
</html>
