# 布局修改说明

## 修改背景

根据图片中显示的要求，需要对3D编辑器界面的右侧面板区域进行布局调整，使其采用左图3D场景的布局设计。

## 修改内容

### 1. 布局结构变更

**修改前：**
- 右侧面板区域采用垂直布局（`mode: 'vertical'`）
- 所有面板垂直排列在一个区域内
- 布局相对简单，但与左侧3D场景的布局风格不一致

**修改后：**
- 右侧面板区域改为水平布局（`mode: 'horizontal'`）
- 分为左右两个子区域，每个子区域内部采用垂直布局
- 模拟左侧3D场景的布局结构，实现视觉一致性

### 2. 具体变更

#### 布局配置调整
- 右侧面板区域的 `mode` 从 `'vertical'` 改为 `'horizontal'`
- 右侧面板区域的 `size` 从 3 增加到 4，以容纳新的布局结构
- 在右侧面板区域内创建两个子区域，每个子区域 `size: 2`

#### 面板分布
- **左侧子区域：** 层级面板、场景面板、材质面板
- **右侧子区域：** 属性面板、检查器面板

### 3. 修改的文件

1. **`editor/src/store/ui/layoutSlice.ts`**
   - 更新 `defaultLayout` 配置
   - 更新所有预定义布局（debug、coding）以保持一致性

2. **`editor/src/components/layout/EditorLayout.tsx`**
   - 更新 `createDefaultLayout` 函数
   - 添加相关注释说明

3. **`editor/src/components/MainLayout.tsx`**
   - 更新主布局配置
   - 保持与其他布局文件的一致性

4. **`editor/src/i18n/locales/zh-CN.json`**
   - 添加布局相关的中文说明文本

## 预期效果

修改后的布局将实现以下效果：

1. **视觉一致性：** 右侧面板区域的布局风格与左侧3D场景保持一致
2. **功能完整性：** 所有面板功能保持不变，只是重新组织了布局结构
3. **用户体验：** 更加直观的界面布局，提升用户操作体验
4. **扩展性：** 新的布局结构为未来添加更多面板提供了更好的空间利用

## 技术实现

使用 `rc-dock` 库的布局配置系统：
- 通过嵌套的 `children` 数组定义布局层次
- 使用 `mode` 属性控制布局方向（horizontal/vertical）
- 通过 `size` 属性控制各区域的相对大小
- 保持所有面板的 `tabs` 配置不变，确保功能完整性

## 验证方法

可以通过以下方式验证修改效果：
1. 启动编辑器应用
2. 观察右侧面板区域的布局变化
3. 确认所有面板功能正常工作
4. 检查布局在不同屏幕尺寸下的适应性

## 注意事项

- 所有预定义布局都已同步更新，确保整体一致性
- 保持了原有的面板功能和交互逻辑
- 布局配置支持动态调整和保存
- 兼容现有的主题和样式系统
