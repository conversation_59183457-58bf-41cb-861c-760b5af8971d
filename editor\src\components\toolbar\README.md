# 编辑器工具栏组件

## 概述

编辑器工具栏是位于编辑器界面顶部的核心UI组件，提供了快速访问常用编辑功能的按钮。本组件基于图片中显示的编辑器界面设计，实现了完整的工具栏功能。

## 功能特性

### 🎯 核心功能
- **文件操作**: 新建、打开、保存、导出、导入项目和场景
- **编辑操作**: 撤销、重做操作，支持操作历史栈
- **变换工具**: 选择、移动、旋转、缩放工具切换
- **坐标空间**: 世界空间和本地空间切换
- **视图控制**: 重置视图、聚焦选中对象
- **显示选项**: 网格、坐标轴显示切换
- **播放控制**: 场景播放/暂停控制
- **界面控制**: 全屏切换、设置面板

### 🎨 界面设计
- **响应式布局**: 适配不同屏幕尺寸
- **暗色主题**: 符合专业编辑器风格
- **图标系统**: 使用Ant Design图标库
- **状态反馈**: 按钮状态实时反映编辑器状态
- **工具提示**: 完整的悬停提示信息

### ⚡ 交互体验
- **快捷键支持**: 主要功能支持键盘快捷键
- **状态同步**: 与Redux状态管理系统集成
- **动画效果**: 平滑的过渡和悬停效果
- **分组布局**: 功能按钮逻辑分组，提高可用性

## 文件结构

```
toolbar/
├── Toolbar.tsx          # 主组件文件
├── Toolbar.less         # 样式文件
├── README.md           # 说明文档
└── __tests__/
    └── Toolbar.test.tsx # 测试文件
```

## 组件架构

### 状态管理
- **Editor State**: 变换模式、显示选项、播放状态等
- **UI State**: 全屏状态、主题设置等
- **Redux Integration**: 完整的状态同步和更新

### 事件处理
- **Transform Mode**: 变换工具切换
- **Transform Space**: 坐标空间切换
- **Display Options**: 显示选项切换
- **File Operations**: 文件操作处理
- **View Controls**: 视图控制操作

## 按钮功能映射

根据图片中显示的工具栏，各按钮功能如下：

| 位置 | 按钮 | 功能 | 快捷键 |
|------|------|------|--------|
| 左侧 | IR Logo | 返回仪表板 | - |
| 左侧 | 下拉菜单 | 文件操作菜单 | - |
| 左侧 | 撤销 | 撤销操作 | Ctrl+Z |
| 左侧 | 重做 | 重做操作 | Ctrl+Y |
| 中间 | 选择工具 | 选择模式 | Q |
| 中间 | 移动工具 | 移动模式 | W |
| 中间 | 旋转工具 | 旋转模式 | E |
| 中间 | 缩放工具 | 缩放模式 | R |
| 中间 | 世界空间 | 世界坐标系 | - |
| 中间 | 本地空间 | 本地坐标系 | - |
| 中间 | 重置视图 | 重置相机 | Home |
| 中间 | 聚焦选中 | 聚焦对象 | F |
| 中间 | 网格显示 | 切换网格 | - |
| 中间 | 坐标轴显示 | 切换坐标轴 | - |
| 中间 | 播放控制 | 播放/暂停 | Space |
| 右侧 | 全屏 | 全屏切换 | F11 |
| 右侧 | 设置 | 设置面板 | - |
| 右侧 | 用户 | 用户信息 | - |
| 右侧 | 发布 | 发布项目 | - |

## 样式系统

### CSS类名规范
- `.editor-toolbar`: 主容器
- `.toolbar-section`: 功能区域
- `.toolbar-group`: 按钮组
- `.toolbar-divider`: 分隔线
- `.tool-button`: 工具按钮
- `.selected`: 选中状态
- `.disabled`: 禁用状态

### 主题变量
- 背景色: `#1e1e1e`
- 边框色: `#333`
- 文字色: `#ccc` / `#fff`
- 主色调: `#1890ff`
- 悬停色: `rgba(255, 255, 255, 0.1)`

## 使用方法

### 基本使用
```tsx
import Toolbar from './components/toolbar/Toolbar';

function EditorLayout() {
  return (
    <div className="editor-layout">
      <Toolbar />
      {/* 其他编辑器组件 */}
    </div>
  );
}
```

### 状态集成
工具栏自动与Redux状态管理系统集成，无需额外配置。

### 自定义样式
```less
.editor-toolbar {
  // 自定义工具栏样式
  .toolbar-group {
    // 自定义按钮组样式
  }
}
```

## 测试

### 运行测试
```bash
npm test -- Toolbar.test.tsx
```

### 测试覆盖
- ✅ 组件渲染
- ✅ 按钮点击事件
- ✅ 状态更新
- ✅ 快捷键支持
- ✅ 响应式布局

## 扩展性

### 添加新按钮
1. 在相应的工具组中添加按钮
2. 实现事件处理函数
3. 更新状态管理
4. 添加样式和测试

### 自定义布局
工具栏支持通过CSS自定义布局和样式，可以根据需要调整按钮位置和分组。

## 依赖项

- React 18+
- Ant Design 5+
- Redux Toolkit
- React Router
- Less

## 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 更新日志

### v1.0.0
- ✅ 完整的工具栏功能实现
- ✅ 响应式设计
- ✅ 暗色主题支持
- ✅ 完整的测试覆盖
- ✅ 详细的文档说明
