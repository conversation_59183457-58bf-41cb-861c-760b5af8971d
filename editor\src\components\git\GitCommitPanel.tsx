/**
 * Git提交面板组件
 * 提供Git提交功能界面
 */
import React, { useState } from 'react';
import { Card, Input, Button, Space, List, Checkbox, message } from 'antd';
import { PlusOutlined, SaveOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { getGitService } from '../../services/GitService';
import './GitCommitPanel.less';

const { TextArea } = Input;

/**
 * Git提交面板组件
 */
const GitCommitPanel: React.FC = () => {
  const { t } = useTranslation();
  const { unstagedFiles, stagedFiles, isLoading } = useSelector((state: RootState) => state.git);
  const [commitMessage, setCommitMessage] = useState<string>('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  // 合并未暂存和已暂存的文件
  const allFiles = [...unstagedFiles, ...stagedFiles];

  // 处理文件选择
  const handleFileSelect = (filePath: string, checked: boolean) => {
    if (checked) {
      setSelectedFiles([...selectedFiles, filePath]);
    } else {
      setSelectedFiles(selectedFiles.filter(f => f !== filePath));
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked && allFiles.length > 0) {
      setSelectedFiles(allFiles.map(f => f.path));
    } else {
      setSelectedFiles([]);
    }
  };

  // 处理提交
  const handleCommit = async () => {
    if (!commitMessage.trim()) {
      message.error(t('git.commitMessageRequired'));
      return;
    }

    if (selectedFiles.length === 0) {
      message.error(t('git.noFilesSelected'));
      return;
    }

    try {
      const gitService = getGitService();

      // 添加选中的文件到暂存区
      await gitService.addFiles(selectedFiles);

      // 提交
      await gitService.commit(commitMessage);
      
      // 清空表单
      setCommitMessage('');
      setSelectedFiles([]);
      
      message.success(t('git.commitSuccess'));
    } catch (error) {
      message.error(t('git.commitFailed'));
    }
  };

  // 渲染文件列表
  const renderFileList = () => {
    if (allFiles.length === 0) {
      return (
        <div className="git-no-changes">
          {t('git.noChanges')}
        </div>
      );
    }

    return (
      <div className="git-file-list">
        <div className="git-file-list-header">
          <Checkbox
            checked={selectedFiles.length === allFiles.length}
            indeterminate={selectedFiles.length > 0 && selectedFiles.length < allFiles.length}
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            {t('git.selectAll')} ({allFiles.length})
          </Checkbox>
        </div>
        <List
          dataSource={allFiles}
          renderItem={(file) => (
            <List.Item className="git-file-item">
              <Checkbox
                checked={selectedFiles.includes(file.path)}
                onChange={(e) => handleFileSelect(file.path, e.target.checked)}
              >
                <span className={`git-file-status git-file-status-${file.status}`}>
                  {file.status}
                </span>
                <span className="git-file-path">{file.path}</span>
              </Checkbox>
            </List.Item>
          )}
        />
      </div>
    );
  };

  return (
    <div className="git-commit-panel">
      <Card title={t('git.commit')} className="git-commit-card">
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 提交消息输入 */}
          <div className="git-commit-message">
            <TextArea
              placeholder={t('git.commitMessagePlaceholder') || ''}
              value={commitMessage}
              onChange={(e) => setCommitMessage(e.target.value)}
              rows={3}
              maxLength={500}
              showCount
            />
          </div>

          {/* 文件列表 */}
          {renderFileList()}

          {/* 操作按钮 */}
          <div className="git-commit-actions">
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleCommit}
                loading={isLoading}
                disabled={!commitMessage.trim() || selectedFiles.length === 0}
              >
                {t('git.commitChanges')}
              </Button>
              <Button
                icon={<PlusOutlined />}
                onClick={() => selectedFiles.length > 0 && getGitService().addFiles(selectedFiles)}
                loading={isLoading}
                disabled={selectedFiles.length === 0}
              >
                {t('git.stageSelected')}
              </Button>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default GitCommitPanel;
