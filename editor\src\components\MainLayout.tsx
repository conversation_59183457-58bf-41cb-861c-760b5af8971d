/**
 * 编辑器主布局组件
 * 使用rc-dock库实现可停靠的面板布局
 */
import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Modal, Tooltip } from 'antd';
import type { MenuProps } from 'antd';
import {

  QuestionCircleOutlined,
  BookOutlined,
  VideoCameraOutlined,
  UserOutlined,
  GlobalOutlined,
  InfoCircleOutlined,
  SelectOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  CameraOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { DockLayout } from 'rc-dock';
import 'rc-dock/dist/rc-dock.css';
import { useTranslation } from 'react-i18next';

import {
  ViewportPanelTab,
  HierarchyPanelTab,
  AssetsPanelTab,
  // ScenePanelTab, // 暂时注释掉未使用的导入
  // InspectorPanelTab, // 暂时注释掉未使用的导入
  PropertiesPanelTab,
  MaterialsPanelTab,
  // ConsolePanelTab, // 暂时注释掉未使用的导入
  // VisualScriptPanelTab // 暂时注释掉未使用的导入
} from './panels/PanelTabs';
import HelpPanel from './panels/HelpPanel';
import TutorialPanel from './tutorials/TutorialPanel';

import './MainLayout.less';

const { Header, Sider, Content } = Layout;

interface MainLayoutProps {
  // 可以添加属性
}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const { t, i18n } = useTranslation();
  // const [collapsed, setCollapsed] = useState(false);
  const [helpVisible, setHelpVisible] = useState(false);
  const [tutorialVisible, setTutorialVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  // 监听语言变化
  useEffect(() => {
    setCurrentLanguage(i18n.language);
  }, [i18n.language]);
  
  // 初始化停靠布局配置 - 根据图片中的布局结构
  // 右侧面板区域采用左图3D场景的布局设计
  const defaultLayout = {
    dockbox: {
      mode: 'horizontal' as const,
      children: [
        {
          mode: 'vertical' as const,
          size: 8, // 主要区域，包含视口和底部资源面板
          children: [
            {
              tabs: [ViewportPanelTab],
              size: 7 // 视口占主要空间
            },
            {
              tabs: [AssetsPanelTab],
              size: 3 // 底部资源面板
            }
          ]
        },
        {
          mode: 'horizontal' as const, // 改为水平布局，模拟3D场景的布局结构
          size: 4, // 右侧面板区域，增加宽度
          children: [
            {
              mode: 'vertical' as const,
              size: 2, // 左侧子区域
              children: [
                {
                  tabs: [HierarchyPanelTab],
                  activeId: 'hierarchyPanel', // 默认激活层级面板
                  size: 1 // 上半部分
                },
                {
                  tabs: [MaterialsPanelTab],
                  size: 1 // 下半部分
                }
              ]
            },
            {
              mode: 'vertical' as const,
              size: 2, // 右侧子区域，采用3D场景的垂直布局结构
              children: [
                {
                  tabs: [PropertiesPanelTab],
                  activeId: 'propertiesPanel', // 属性面板
                  size: 1 // 上半部分
                }
              ]
            }
          ]
        }
      ]
    }
  };

  const [layout] = useState(defaultLayout);
  
  // 切换侧边栏折叠状态 - 暂时注释掉未使用的函数
  // const toggleCollapsed = () => {
  //   setCollapsed(!collapsed);
  // };
  
  // 切换帮助面板可见性
  const toggleHelp = () => {
    setHelpVisible(!helpVisible);
  };
  
  // 切换教程面板可见性
  const toggleTutorial = () => {
    setTutorialVisible(!tutorialVisible);
  };
  
  // 切换语言
  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    setCurrentLanguage(lang);
  };
  
  // 语言菜单
  const languageMenuItems: MenuProps['items'] = [
    {
      key: 'zh-CN',
      label: '中文',
      onClick: () => changeLanguage('zh-CN')
    },
    {
      key: 'en-US',
      label: 'English',
      onClick: () => changeLanguage('en-US')
    }
  ];

  // 帮助菜单
  const helpMenuItems: MenuProps['items'] = [
    {
      key: 'help',
      label: (
        <>
          <QuestionCircleOutlined /> {t('menu.help')}
        </>
      ),
      onClick: toggleHelp
    },
    {
      key: 'tutorials',
      label: (
        <>
          <VideoCameraOutlined /> {t('menu.tutorials')}
        </>
      ),
      onClick: toggleTutorial
    },
    {
      key: 'documentation',
      label: (
        <>
          <BookOutlined /> {t('menu.documentation')}
        </>
      )
    },
    {
      type: 'divider'
    },
    {
      key: 'about',
      label: (
        <>
          <InfoCircleOutlined /> {t('menu.about')}
        </>
      )
    }
  ];
  
  return (
    <Layout className="main-layout">
      <Header className="header">
        <div className="logo">IR Editor</div>
        <Menu theme="dark" mode="horizontal" defaultSelectedKeys={['1']}>
          <Menu.Item key="1">{t('menu.file')}</Menu.Item>
          <Menu.Item key="2">{t('menu.edit')}</Menu.Item>
          <Menu.Item key="3">{t('menu.view')}</Menu.Item>
          <Menu.Item key="4">{t('menu.window')}</Menu.Item>
        </Menu>
        <div className="header-right">
          <Dropdown menu={{ items: languageMenuItems }} placement="bottomRight">
            <Button type="text" icon={<GlobalOutlined />} style={{ color: 'white' }}>
              {currentLanguage === 'zh-CN' ? '中文' : 'English'}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomRight">
            <Button type="text" icon={<QuestionCircleOutlined />} style={{ color: 'white' }} />
          </Dropdown>
          <Button type="text" icon={<UserOutlined />} style={{ color: 'white' }} />
        </div>
      </Header>
      <Layout>
        {/* 左侧工具栏 */}
        <Sider
          width={48}
          className="left-toolbar"
          style={{
            background: '#2a2a2a',
            borderRight: '1px solid #444',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <div style={{
            padding: '8px 0',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px'
          }}>
            <Tooltip title={t('tools.select') || '选择'} placement="right">
              <Button
                type="text"
                icon={<SelectOutlined />}
                style={{ color: '#fff', width: '32px', height: '32px' }}
              />
            </Tooltip>
            <Tooltip title={t('tools.move') || '移动'} placement="right">
              <Button
                type="text"
                icon={<ArrowsAltOutlined />}
                style={{ color: '#fff', width: '32px', height: '32px' }}
              />
            </Tooltip>
            <Tooltip title={t('tools.rotate') || '旋转'} placement="right">
              <Button
                type="text"
                icon={<RotateRightOutlined />}
                style={{ color: '#fff', width: '32px', height: '32px' }}
              />
            </Tooltip>
            <Tooltip title={t('tools.scale') || '缩放'} placement="right">
              <Button
                type="text"
                icon={<ColumnWidthOutlined />}
                style={{ color: '#fff', width: '32px', height: '32px' }}
              />
            </Tooltip>
            <div style={{ width: '24px', height: '1px', background: '#444', margin: '4px 0' }} />
            <Tooltip title={t('tools.camera') || '相机'} placement="right">
              <Button
                type="text"
                icon={<CameraOutlined />}
                style={{ color: '#fff', width: '32px', height: '32px' }}
              />
            </Tooltip>
            <Tooltip title={t('tools.light') || '灯光'} placement="right">
              <Button
                type="text"
                icon={<EyeOutlined />}
                style={{ color: '#fff', width: '32px', height: '32px' }}
              />
            </Tooltip>
          </div>
        </Sider>

        <Content className="content">
          <DockLayout
            defaultLayout={layout}
            style={{ position: 'absolute', left: 0, top: 0, right: 0, bottom: 0 }}
          />
        </Content>
      </Layout>
      
      {/* 帮助面板模态框 */}
      <Modal
        title={t('help.title')}
        open={helpVisible}
        onCancel={toggleHelp}
        footer={null}
        width={800}
        styles={{ body: { height: '600px', padding: 0, overflow: 'hidden' } }}
      >
        <HelpPanel />
      </Modal>

      {/* 教程面板模态框 */}
      <Modal
        title={t('tutorials.title')}
        open={tutorialVisible}
        onCancel={toggleTutorial}
        footer={null}
        width={800}
        styles={{ body: { height: '600px', padding: 0, overflow: 'hidden' } }}
      >
        <TutorialPanel onClose={toggleTutorial} />
      </Modal>
    </Layout>
  );
};

export default MainLayout;
