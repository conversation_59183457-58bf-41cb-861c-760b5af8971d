/**
 * 示例项目数据
 * 定义编辑器中的示例项目
 */
import { ExampleProject } from '../services/ExampleProjectService';

/**
 * 示例项目数据
 */
export const exampleProjects: ExampleProject[] = [
  // 从原项目复制的示例场景
  {
    id: 'default-scene',
    title: '默认场景',
    description: '基础演示场景，包含基本的几何体和光照设置，适合初学者了解编辑器功能。',
    category: 'getting-started',
    difficulty: 'beginner',
    thumbnailUrl: '/projects/default-project/public/scenes/default.thumbnail.jpg',
    path: '/projects/default-project/public/scenes/default.gltf',
    tags: ['基础', '演示', '几何体', '光照'],
    features: [
      { title: '基础几何体', description: '包含立方体、球体、平面等基础几何体' },
      { title: '光照系统', description: '展示环境光、方向光和点光源的使用' },
      { title: '材质演示', description: '展示不同材质类型和属性' },
      { title: '相机控制', description: '演示相机的基本控制和设置' },
    ],
    estimatedTime: 10,
    popularity: 100,
    rating: 4.9,
    videoTutorialId: 'default-scene',
    interactiveTutorialId: 'default-scene'
  },

  {
    id: 'apartment-scene',
    title: '公寓场景',
    description: '室内公寓场景，展示室内环境设计、家具布置和室内光照技巧。',
    category: 'architecture',
    difficulty: 'intermediate',
    thumbnailUrl: '/projects/default-project/public/scenes/apartment.thumbnail.jpg',
    path: '/projects/default-project/public/scenes/apartment.gltf',
    tags: ['室内', '建筑', '家具', '光照'],
    features: [
      { title: '室内设计', description: '完整的室内空间设计和布局' },
      { title: '家具模型', description: '各种家具和装饰物品的摆放' },
      { title: '室内光照', description: '窗户光照和室内灯光的配置' },
      { title: '材质应用', description: '木材、金属、布料等真实材质的应用' },
    ],
    estimatedTime: 20,
    popularity: 85,
    rating: 4.7,
    videoTutorialId: 'apartment-scene',
    interactiveTutorialId: 'apartment-scene'
  },

  {
    id: 'sky-station-scene',
    title: '天空站场景',
    description: '科幻风格的天空站场景，展示科幻环境设计和特殊效果的制作。',
    category: 'sci-fi',
    difficulty: 'advanced',
    thumbnailUrl: '/projects/default-project/public/scenes/sky-station.thumbnail.jpg',
    path: '/projects/default-project/public/scenes/sky-station.gltf',
    tags: ['科幻', '太空', '特效', '环境'],
    features: [
      { title: '科幻建筑', description: '未来风格的建筑和结构设计' },
      { title: '特殊材质', description: '金属、玻璃、发光等科幻材质' },
      { title: '环境效果', description: '天空盒、雾效和大气效果' },
      { title: '光效设计', description: '霓虹灯、投射光和环境光效' },
    ],
    estimatedTime: 30,
    popularity: 75,
    rating: 4.6,
    videoTutorialId: 'sky-station-scene',
    interactiveTutorialId: 'sky-station-scene'
  },

  {
    id: 'sponza-scene',
    title: 'Sponza 测试场景',
    description: '经典的Sponza测试场景，用于光照和渲染技术的测试和演示。',
    category: 'testing',
    difficulty: 'advanced',
    thumbnailUrl: '/projects/default-project/public/scenes/sponza.thumbnail.jpg',
    path: '/projects/default-project/public/scenes/sponza.gltf',
    tags: ['测试', '渲染', '光照', '性能'],
    features: [
      { title: '复杂几何体', description: '包含大量细节的复杂建筑结构' },
      { title: '光照测试', description: '测试各种光照算法和阴影效果' },
      { title: '性能基准', description: '用于测试渲染性能和优化效果' },
      { title: '材质丰富', description: '包含多种不同类型的材质和纹理' },
    ],
    estimatedTime: 25,
    popularity: 60,
    rating: 4.5,
    videoTutorialId: 'sponza-scene',
    interactiveTutorialId: 'sponza-scene'
  },

  // 基础编辑器功能演示
  {
    id: 'editor-basics',
    title: '基础编辑器功能演示',
    description: '展示编辑器的基本功能，包括场景创建、对象操作和属性编辑。',
    category: 'getting-started',
    difficulty: 'beginner',
    thumbnailUrl: '/assets/images/examples/editor-basics.jpg',
    path: '/examples/editor-basics/',
    tags: ['编辑器', '基础', '界面', '操作'],
    features: [
      { title: '场景创建与管理', description: '创建新场景、设置场景属性、保存和加载场景' },
      { title: '对象操作', description: '选择、移动、旋转和缩放3D对象' },
      { title: '属性编辑', description: '通过属性面板修改对象的各种属性' },
      { title: '材质编辑', description: '创建和编辑基本材质属性' },
      { title: '灯光设置', description: '添加和调整不同类型的灯光' },
    ],
    estimatedTime: 15,
    popularity: 95,
    rating: 4.8,
    videoTutorialId: 'editor-basics',
    interactiveTutorialId: 'editor-basics'},
  
  // 材质编辑器演示
  {
    id: 'material-editor',
    title: '材质编辑器演示',
    description: '展示如何创建和编辑各种材质类型，包括PBR材质、基础材质和特殊效果材质。',
    category: 'materials',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/material-editor.jpg',
    path: '/examples/material-editor/',
    tags: ['材质', 'PBR', '着色器', '纹理'],
    features: [
      { title: '标准PBR材质编辑', description: '编辑物理基础渲染材质的各种参数' },
      { title: '基础材质编辑', description: '创建和编辑基础材质属性' },
      { title: '特殊效果材质', description: '创建卡通渲染、全息和发光等特殊效果材质' },
      { title: '材质预览', description: '实时预览材质效果和参数调整' },
      { title: '材质库', description: '浏览和使用预设材质库' },
    ],
    estimatedTime: 25,
    popularity: 85,
    rating: 4.6,
    videoTutorialId: 'material-basics'},
  
  // 动画系统演示
  {
    id: 'animation-demo',
    title: '动画系统演示',
    description: '展示关键帧动画、骨骼动画和动画混合功能，以及动画状态机的使用。',
    category: 'animation',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/animation-demo.jpg',
    path: '/examples/animation-demo/',
    tags: ['动画', '骨骼', '状态机', '混合'],
    features: [
      { title: '关键帧动画', description: '创建和编辑关键帧动画' },
      { title: '骨骼动画', description: '导入和播放骨骼动画' },
      { title: '动画混合', description: '混合多个动画并创建平滑过渡' },
      { title: '动画状态机', description: '创建和编辑动画状态机' },
      { title: '动画事件', description: '在动画中添加和触发事件' },
    ],
    estimatedTime: 30,
    popularity: 90,
    rating: 4.7,
    videoTutorialId: 'animation-system',
    interactiveTutorialId: 'animation-system'},
  
  // 物理系统演示
  {
    id: 'physics-demo',
    title: '物理系统演示',
    description: '展示刚体物理、碰撞检测、物理约束和物理材质等功能。',
    category: 'physics',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/physics-demo.jpg',
    path: '/examples/physics-demo/',
    tags: ['物理', '碰撞', '约束', '刚体'],
    features: [
      { title: '刚体物理', description: '创建和模拟刚体物理对象' },
      { title: '碰撞检测', description: '设置和检测物体之间的碰撞' },
      { title: '物理约束', description: '创建铰链、弹簧和滑轨等物理约束' },
      { title: '物理材质', description: '设置摩擦、弹性和密度等物理材质属性' },
      { title: '射线检测', description: '使用射线检测物体和环境' },
    ],
    estimatedTime: 25,
    popularity: 80,
    rating: 4.5,
    videoTutorialId: 'physics-basics'},
  
  // 视觉脚本演示
  {
    id: 'visualscript-demo',
    title: '视觉脚本演示',
    description: '展示视觉脚本系统的节点创建、连接、调试和执行功能。',
    category: 'scripting',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/visualscript-demo.jpg',
    path: '/examples/visualscript-demo/',
    tags: ['脚本', '节点', '逻辑', '事件'],
    features: [
      { title: '节点创建', description: '创建和连接视觉脚本节点' },
      { title: '事件处理', description: '响应用户输入和系统事件' },
      { title: '逻辑流程', description: '创建条件分支和循环结构' },
      { title: '变量和数据', description: '管理和操作脚本数据' },
      { title: '调试工具', description: '调试和测试视觉脚本' },
    ],
    estimatedTime: 35,
    popularity: 85,
    rating: 4.6,
    videoTutorialId: 'visualscript-basics'},
  
  // 场景构建教程
  {
    id: 'scene-building-tutorial',
    title: '场景构建教程',
    description: '从零开始构建完整的3D场景，包括地形、建筑、植被、光照和后期处理效果。',
    category: 'scene-building',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/scene-building-tutorial.jpg',
    path: '/examples/scene-building-tutorial/',
    tags: ['场景', '地形', '建筑', '植被', '光照'],
    features: [
      { title: '地形系统', description: '创建和编辑地形，设置地形纹理和细节' },
      { title: '建筑系统', description: '放置和编辑建筑物，设置材质和细节' },
      { title: '植被系统', description: '添加树木、草和其他植被，设置分布和密度' },
      { title: '光照系统', description: '设置环境光、方向光、点光源和区域光源' },
      { title: '后期处理', description: '添加景深、环境光遮蔽、屏幕空间反射等效果' },
    ],
    estimatedTime: 60,
    popularity: 90,
    rating: 4.8,
    videoTutorialId: 'scene-building'},
  
  // 角色创建教程
  {
    id: 'character-creation-tutorial',
    title: '角色创建教程',
    description: '导入和设置3D角色模型，配置骨骼和动画系统，添加控制器和交互功能。',
    category: 'character',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/character-creation-tutorial.jpg',
    path: '/examples/character-creation-tutorial/',
    tags: ['角色', '动画', '骨骼', '控制器', '交互'],
    features: [
      { title: '角色模型导入', description: '导入和优化3D角色模型' },
      { title: '骨骼设置和绑定', description: '配置骨骼层次结构和蒙皮权重' },
      { title: '动画导入和设置', description: '导入和调整角色动画' },
      { title: '动画状态机配置', description: '创建和编辑动画状态机' },
      { title: '角色控制器实现', description: '实现角色移动和交互控制' },
      { title: '角色交互功能', description: '添加角色与环境和其他对象的交互' },
    ],
    estimatedTime: 90,
    popularity: 95,
    rating: 4.9,
    videoTutorialId: 'character-creation'},
  
  // 游戏机制教程
  {
    id: 'gameplay-tutorial',
    title: '游戏机制教程',
    description: '使用视觉脚本创建游戏逻辑，实现物理交互和碰撞检测，添加UI和得分系统。',
    category: 'gameplay',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/gameplay-tutorial.jpg',
    path: '/examples/gameplay-tutorial/',
    tags: ['游戏', '逻辑', '交互', 'UI', '得分'],
    features: [
      { title: '游戏逻辑设计', description: '设计和实现游戏核心逻辑' },
      { title: '物理交互', description: '创建基于物理的游戏交互' },
      { title: '碰撞检测', description: '检测和响应游戏对象之间的碰撞' },
      { title: 'UI设计', description: '创建游戏界面和HUD' },
      { title: '得分系统', description: '实现得分、进度和成就系统' },
    ],
    estimatedTime: 75,
    popularity: 85,
    rating: 4.7,
    videoTutorialId: 'gameplay-basics'},
  
  // 多人场景教程
  {
    id: 'multiplayer-tutorial',
    title: '多人场景教程',
    description: '设置网络连接和同步，实现多人交互和状态同步，添加聊天和社交功能。',
    category: 'networking',
    difficulty: 'advanced',
    thumbnailUrl: '/assets/images/examples/multiplayer-tutorial.jpg',
    path: '/examples/multiplayer-tutorial/',
    tags: ['多人', '网络', '同步', '交互', '聊天'],
    features: [
      { title: '网络连接设置', description: '设置和管理网络连接' },
      { title: '实体同步', description: '同步多个客户端之间的实体状态' },
      { title: '状态同步', description: '同步游戏状态和事件' },
      { title: '多人交互', description: '实现多个用户之间的交互' },
      { title: '聊天系统', description: '添加文本和语音聊天功能' },
    ],
    estimatedTime: 120,
    popularity: 80,
    rating: 4.6,
    videoTutorialId: 'multiplayer-basics'},
  
  // 性能优化最佳实践
  {
    id: 'performance-optimization',
    title: '性能优化最佳实践',
    description: '展示场景优化技术，如LOD、实例化、合并等，以及性能分析和瓶颈检测。',
    category: 'optimization',
    difficulty: 'advanced',
    thumbnailUrl: '/assets/images/examples/performance-optimization.jpg',
    path: '/examples/performance-optimization/',
    tags: ['优化', '性能', 'LOD', '实例化', '分析'],
    features: [
      { title: '场景复杂度分析', description: '分析和评估场景复杂度' },
      { title: 'LOD实现', description: '实现细节层次系统' },
      { title: '实例化渲染', description: '使用实例化技术优化渲染' },
      { title: '合并静态网格', description: '合并静态对象减少绘制调用' },
      { title: '性能分析工具', description: '使用性能分析工具检测瓶颈' },
    ],
    estimatedTime: 60,
    popularity: 75,
    rating: 4.5,
    videoTutorialId: 'performance-analysis'},
  
  // 高级面部动画示例
  {
    id: 'advanced-facial-animation',
    title: '高级面部动画示例',
    description: '展示DL（Digital Learning）引擎的高级面部动画功能，包括AI驱动的情感分析和表情生成、多模型支持以及性能优化技巧。',
    category: 'animation',
    difficulty: 'expert',
    thumbnailUrl: '/assets/images/examples/advanced-facial-animation.jpg',
    path: '/examples/advanced-facial-animation/',
    tags: ['面部动画', 'AI', '表情', '情感分析', '口型同步'],
    features: [
      { title: '基于AI的情感分析', description: '使用AI分析文本情感并生成表情' },
      { title: '自然的面部表情', description: '创建自然、生动的面部表情动画' },
      { title: '多种预训练模型', description: '支持多种AI模型，如BERT、RoBERTa等' },
      { title: '微表情系统', description: '添加细微的面部表情变化' },
      { title: '表情混合和过渡', description: '平滑混合不同表情并创建自然过渡' },
    ],
    estimatedTime: 90,
    popularity: 70,
    rating: 4.8,
    videoTutorialId: 'facial-animation-advanced'},
  
  // 数据可视化示例
  {
    id: 'data-visualization',
    title: '数据可视化示例',
    description: '展示如何使用DL（Digital Learning）引擎进行数据可视化，包括3D柱状图、散点图、热力图和地理数据可视化等。',
    category: 'visualization',
    difficulty: 'intermediate',
    thumbnailUrl: '/assets/images/examples/data-visualization.jpg',
    path: '/examples/data-visualization/',
    tags: ['数据', '可视化', '图表', '3D', '交互'],
    features: [
      { title: '3D柱状图', description: '将数据以3D柱状图形式展示' },
      { title: '3D散点图', description: '以3D空间中的点展示多维数据关系' },
      { title: '热力图', description: '使用颜色渐变展示数据密度和分布' },
      { title: '地理数据可视化', description: '在3D地图上展示地理相关数据' },
      { title: '交互式数据探索', description: '支持缩放、旋转、筛选和查询数据' },
    ],
    estimatedTime: 45,
    popularity: 80,
    rating: 4.7,
    videoTutorialId: 'data-visualization'},
];
