/**
 * 相机控制服务
 * 处理3D视口的相机交互和控制
 */
import * as THREE from 'three';

export interface CameraControlOptions {
  enableDamping: boolean;
  dampingFactor: number;
  enableZoom: boolean;
  enableRotate: boolean;
  enablePan: boolean;
  autoRotate: boolean;
  autoRotateSpeed: number;
  minDistance: number;
  maxDistance: number;
  minPolarAngle: number;
  maxPolarAngle: number;
}

class CameraControlService {
  private camera: THREE.PerspectiveCamera | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private target: THREE.Vector3 = new THREE.Vector3();
  
  // 控制状态
  private isEnabled = true;
  private isDragging = false;
  private isRotating = false;
  private isPanning = false;
  // private isZooming = false;
  
  // 鼠标状态
  private mousePosition = new THREE.Vector2();
  private lastMousePosition = new THREE.Vector2();
  private mouseDelta = new THREE.Vector2();
  
  // 相机状态
  private spherical = new THREE.Spherical();
  private sphericalDelta = new THREE.Spherical();
  private scale = 1;
  private panOffset = new THREE.Vector3();
  
  // 配置选项
  private options: CameraControlOptions = {
    enableDamping: true,
    dampingFactor: 0.05,
    enableZoom: true,
    enableRotate: true,
    enablePan: true,
    autoRotate: false,
    autoRotateSpeed: 2.0,
    minDistance: 0.1,
    maxDistance: Infinity,
    minPolarAngle: 0,
    maxPolarAngle: Math.PI
  };

  /**
   * 初始化相机控制
   */
  public initialize(
    camera: THREE.PerspectiveCamera, 
    canvas: HTMLCanvasElement,
    options: Partial<CameraControlOptions> = {}
  ): void {
    this.camera = camera;
    this.canvas = canvas;
    this.options = { ...this.options, ...options };
    
    // 设置初始目标点
    this.target.set(0, 0, 0);
    
    // 计算初始球坐标
    this.updateSpherical();
    
    // 绑定事件监听器
    this.bindEventListeners();
    
    console.log('相机控制器初始化完成');
  }

  /**
   * 更新球坐标
   */
  private updateSpherical(): void {
    if (!this.camera) return;
    
    const offset = new THREE.Vector3();
    offset.copy(this.camera.position).sub(this.target);
    this.spherical.setFromVector3(offset);
  }

  /**
   * 绑定事件监听器
   */
  private bindEventListeners(): void {
    if (!this.canvas) return;

    // 鼠标事件
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));
    
    // 触摸事件
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));
    
    // 上下文菜单
    this.canvas.addEventListener('contextmenu', this.onContextMenu.bind(this));
  }

  /**
   * 鼠标按下事件
   */
  private onMouseDown(event: MouseEvent): void {
    if (!this.isEnabled) return;
    
    event.preventDefault();
    
    this.updateMousePosition(event);
    this.lastMousePosition.copy(this.mousePosition);
    
    if (event.button === 0) { // 左键 - 旋转
      if (this.options.enableRotate) {
        this.isRotating = true;
      }
    } else if (event.button === 1) { // 中键 - 平移
      if (this.options.enablePan) {
        this.isPanning = true;
      }
    } else if (event.button === 2) { // 右键 - 平移
      if (this.options.enablePan) {
        this.isPanning = true;
      }
    }
    
    this.isDragging = true;
    document.addEventListener('mousemove', this.onMouseMove.bind(this));
    document.addEventListener('mouseup', this.onMouseUp.bind(this));
  }

  /**
   * 鼠标移动事件
   */
  private onMouseMove(event: MouseEvent): void {
    if (!this.isEnabled || !this.isDragging) return;
    
    event.preventDefault();
    
    this.updateMousePosition(event);
    this.mouseDelta.subVectors(this.mousePosition, this.lastMousePosition);
    
    if (this.isRotating) {
      this.rotateCamera(this.mouseDelta);
    } else if (this.isPanning) {
      this.panCamera(this.mouseDelta);
    }
    
    this.lastMousePosition.copy(this.mousePosition);
  }

  /**
   * 鼠标释放事件
   */
  private onMouseUp(event: MouseEvent): void {
    if (!this.isEnabled) return;
    
    event.preventDefault();
    
    this.isDragging = false;
    this.isRotating = false;
    this.isPanning = false;
    
    document.removeEventListener('mousemove', this.onMouseMove.bind(this));
    document.removeEventListener('mouseup', this.onMouseUp.bind(this));
  }

  /**
   * 鼠标滚轮事件
   */
  private onWheel(event: WheelEvent): void {
    if (!this.isEnabled || !this.options.enableZoom) return;
    
    event.preventDefault();
    
    const delta = event.deltaY;
    if (delta < 0) {
      this.zoomIn();
    } else {
      this.zoomOut();
    }
  }

  /**
   * 触摸开始事件
   */
  private onTouchStart(_event: TouchEvent): void {
    // TODO: 实现触摸控制
  }

  /**
   * 触摸移动事件
   */
  private onTouchMove(_event: TouchEvent): void {
    // TODO: 实现触摸控制
  }

  /**
   * 触摸结束事件
   */
  private onTouchEnd(_event: TouchEvent): void {
    // TODO: 实现触摸控制
  }

  /**
   * 上下文菜单事件
   */
  private onContextMenu(event: Event): void {
    event.preventDefault();
  }

  /**
   * 更新鼠标位置
   */
  private updateMousePosition(event: MouseEvent): void {
    if (!this.canvas) return;
    
    const rect = this.canvas.getBoundingClientRect();
    this.mousePosition.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mousePosition.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  }

  /**
   * 旋转相机
   */
  private rotateCamera(delta: THREE.Vector2): void {
    if (!this.canvas) return;
    
    const element = this.canvas;
    const rotateSpeed = 2 * Math.PI / element.clientHeight;
    
    this.sphericalDelta.theta -= delta.x * rotateSpeed;
    this.sphericalDelta.phi -= delta.y * rotateSpeed;
  }

  /**
   * 平移相机
   */
  private panCamera(delta: THREE.Vector2): void {
    if (!this.camera || !this.canvas) return;
    
    const element = this.canvas;
    const targetDistance = this.camera.position.distanceTo(this.target);
    const panSpeed = 2 * targetDistance * Math.tan((this.camera.fov / 2) * Math.PI / 180) / element.clientHeight;
    
    const panLeft = new THREE.Vector3();
    const panUp = new THREE.Vector3();
    
    // 计算平移向量
    panLeft.setFromMatrixColumn(this.camera.matrix, 0);
    panUp.setFromMatrixColumn(this.camera.matrix, 1);
    
    panLeft.multiplyScalar(-delta.x * panSpeed);
    panUp.multiplyScalar(delta.y * panSpeed);
    
    this.panOffset.add(panLeft).add(panUp);
  }

  /**
   * 放大
   */
  private zoomIn(): void {
    this.scale /= 1.1;
  }

  /**
   * 缩小
   */
  private zoomOut(): void {
    this.scale *= 1.1;
  }

  /**
   * 更新相机位置
   */
  public update(): void {
    if (!this.camera) return;
    
    const offset = new THREE.Vector3();
    
    // 应用旋转
    this.spherical.theta += this.sphericalDelta.theta;
    this.spherical.phi += this.sphericalDelta.phi;
    
    // 限制极角
    this.spherical.phi = Math.max(this.options.minPolarAngle, Math.min(this.options.maxPolarAngle, this.spherical.phi));
    
    // 确保phi不为0或PI
    this.spherical.phi = Math.max(0.000001, Math.min(Math.PI - 0.000001, this.spherical.phi));
    
    // 应用缩放
    this.spherical.radius *= this.scale;
    this.spherical.radius = Math.max(this.options.minDistance, Math.min(this.options.maxDistance, this.spherical.radius));
    
    // 应用平移
    this.target.add(this.panOffset);
    
    // 计算新的相机位置
    offset.setFromSpherical(this.spherical);
    this.camera.position.copy(this.target).add(offset);
    this.camera.lookAt(this.target);
    
    // 应用阻尼
    if (this.options.enableDamping) {
      this.sphericalDelta.theta *= (1 - this.options.dampingFactor);
      this.sphericalDelta.phi *= (1 - this.options.dampingFactor);
      this.panOffset.multiplyScalar(1 - this.options.dampingFactor);
    } else {
      this.sphericalDelta.set(0, 0, 0);
      this.panOffset.set(0, 0, 0);
    }
    
    this.scale = 1;
  }

  /**
   * 设置目标点
   */
  public setTarget(target: THREE.Vector3): void {
    this.target.copy(target);
    this.updateSpherical();
  }

  /**
   * 获取目标点
   */
  public getTarget(): THREE.Vector3 {
    return this.target.clone();
  }

  /**
   * 启用/禁用控制
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 销毁控制器
   */
  public dispose(): void {
    if (this.canvas) {
      this.canvas.removeEventListener('mousedown', this.onMouseDown.bind(this));
      this.canvas.removeEventListener('mousemove', this.onMouseMove.bind(this));
      this.canvas.removeEventListener('mouseup', this.onMouseUp.bind(this));
      this.canvas.removeEventListener('wheel', this.onWheel.bind(this));
      this.canvas.removeEventListener('touchstart', this.onTouchStart.bind(this));
      this.canvas.removeEventListener('touchmove', this.onTouchMove.bind(this));
      this.canvas.removeEventListener('touchend', this.onTouchEnd.bind(this));
      this.canvas.removeEventListener('contextmenu', this.onContextMenu.bind(this));
    }
    
    this.camera = null;
    this.canvas = null;
  }
}

export default CameraControlService;
