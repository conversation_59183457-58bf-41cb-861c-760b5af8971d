/**
 * JWT认证守卫
 */
import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { JwtService } from '@nestjs/jwt';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(
    private readonly jwtService: JwtService,
    @Inject('USER_SERVICE') private readonly userService: ClientProxy,
  ) {
    super();
  }

  // 使用 Passport 的默认行为，让 JWT 策略处理验证
  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      // 调用父类的 canActivate 方法，这会触发 JWT 策略
      const result = await super.canActivate(context);
      return result as boolean;
    } catch (error) {
      // 如果 JWT 策略验证失败，尝试简化验证
      const request = context.switchToHttp().getRequest();
      const authHeader = request.headers.authorization;

      if (!authHeader) {
        throw new UnauthorizedException('缺少认证令牌');
      }

      const [type, token] = authHeader.split(' ');

      if (type !== 'Bearer') {
        throw new UnauthorizedException('无效的认证类型');
      }

      try {
        // 检查是否是开发环境的简单token
        if (process.env.NODE_ENV === 'development' && token.includes('dev-signature')) {
          console.log('开发环境：处理开发token');

          // 解析开发token
          const parts = token.split('.');
          if (parts.length === 3) {
            try {
              const payload = JSON.parse(atob(parts[1]));
              const user = {
                id: payload.sub || 'dev-user-123',
                username: payload.username || 'developer',
                email: payload.email || '<EMAIL>',
                role: payload.role || 'admin'
              };

              request.user = user;
              return true;
            } catch (parseError) {
              console.warn('开发token解析失败:', parseError);
            }
          }
        }

        // 验证JWT
        const payload = this.jwtService.verify(token);

        // 创建简化的用户对象，避免微服务调用
        const user = {
          id: payload.sub || payload.userId || 'default-user',
          username: payload.username || 'user',
          email: payload.email || '<EMAIL>',
          role: payload.role || 'user'
        };

        // 将用户信息添加到请求中
        request.user = user;

        return true;
      } catch (jwtError) {
        throw new UnauthorizedException('无效的认证令牌');
      }
    }
  }
}
