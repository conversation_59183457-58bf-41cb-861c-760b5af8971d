{"location": {"youHaveBeenBannedMsg": "You have been banned from this location", "locationName": {"pageTitle": "Home"}}, "editor": {"projects": {"title": "Projects", "header": "Please Login", "welcomeMsg": "Welcome", "locationHeader": "Locations", "projectHeader": "Projects", "sceneHeader": "Scenes", "description": "If you are new here we recommend going through the tutorial. Otherwise, jump right in and create a project from scratch or from one of our templates.", "lbl-startTutorial": "Start Tutorial", "lbl-newProject": "New Project", "lbl-createProject": "Create", "lbl-newScene": "New Scene", "lbl-searchProject": "Search Existing Projects...", "back": "Back", "contextMenu": "Delete Project", "installed": "Installed Projects", "official": "Official Projects", "community": "Community Projects", "install": "Install", "uninstall": "Uninstall", "rename": "<PERSON><PERSON>", "createProject": "Create Project", "installProject": "Install Project", "processing": "Processing", "projectName": "Project Name", "projectURL": "Project URL", "link": "Link to GitHub repo", "unlink": "Edit GitHub link", "resetToMain": "Reset to Main Branch", "updateFromGithub": "Update project from GitHub", "pushToGithub": "Push to GitHub", "githubURL": "GitHub repo URL", "linkRepo": "Link to GitHub repo", "linkButton": "Link", "changeLinkButton": "Change Link", "unLinkButton": "Unlink", "userInviteCode": "User's Invite Code or User ID", "editProjectPermissions": "Manage User Permissions on Project", "addUser": "Add User", "permissions": "Project Permissions", "bake": "Bake"}}, "error": {"goHomeLink": "Go Home", "withStatusCode": "An error {{code}} occurred on server", "withoutStatusCode": "An error occurred on server"}, "404": {"msg": "404 - Page Not Found"}, "503": {"msg": "503 - Service Unavailable"}, "no-projects": {"msg": "Error: No Projects Installed - please contact a site admin"}, "dev": {"pageTitle": "Home"}, "login": {"pageTitle": "<PERSON><PERSON>"}, "workerTest": {"pageTitle": "Worker Test"}, "index": {"by": "BY", "xr": "XR", "foundation": "Foundation", "description": "FREE, OPEN, & INTEROPERABLE IMMERSIVE WEB TECHNOLOGY"}}