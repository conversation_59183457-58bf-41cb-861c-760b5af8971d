/**
 * 视口面板组件 - 参考原项目ir-engine-dev的视口面板
 */
import React, { useRef, useEffect, useState } from 'react';
import { Button, Space, Dropdown, Tooltip, Select } from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,

  SettingOutlined,
  BorderOutlined,
  AppstoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  SelectOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  setTransformMode,
  setTransformSpace,
  TransformMode,
  TransformSpace
} from '../../store/editor/editorSlice';
import SceneLoader, { SceneData } from '../../services/SceneLoader';
import ThreeRenderService from '../../services/ThreeRenderService';

const { Option } = Select;

interface ViewportPanelProps {
  // 可以添加属性
}

const ViewportPanel: React.FC<ViewportPanelProps> = () => {
  const { t } = useTranslation();
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();
  const dispatch = useAppDispatch();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [cameraMode, setCameraMode] = useState('perspective');
  const [renderMode, setRenderMode] = useState('shaded');
  const [sceneData, setSceneData] = useState<SceneData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isThreeInitialized, setIsThreeInitialized] = useState(false);
  const [showPerformanceStats, setShowPerformanceStats] = useState(false);
  const [performanceStats, setPerformanceStats] = useState<any>(null);

  const {
    showGrid,
    showAxes,
    isPlaying,
    transformMode,
    transformSpace
  } = useAppSelector((state) => state.editor);

  // 加载场景数据
  useEffect(() => {
    const loadScene = async () => {
      if (!projectId || !sceneId) return;

      setIsLoading(true);
      try {
        const sceneLoader = SceneLoader.getInstance();
        const scenePath = `/projects/${projectId}/public/scenes/${sceneId}.gltf`;
        console.log('加载场景路径:', scenePath);

        const data = await sceneLoader.loadScene(scenePath);
        setSceneData(data);
        console.log('场景加载成功:', data);
      } catch (error) {
        console.error('场景加载失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadScene();
  }, [projectId, sceneId]);

  // 初始化Three.js渲染器
  useEffect(() => {
    const initThreeJS = async () => {
      if (!canvasRef.current || isThreeInitialized) return;

      try {
        const renderService = ThreeRenderService.getInstance();
        await renderService.initialize(canvasRef.current, {
          antialias: true,
          shadows: true,
          physicallyCorrectLights: true
        });

        setIsThreeInitialized(true);
        console.log('Three.js 渲染器初始化完成');

        // 监听性能统计
        const performanceService = renderService.getPerformanceService();
        if (performanceService) {
          performanceService.addStatsUpdateListener((stats) => {
            setPerformanceStats(stats);
          });
        }
      } catch (error) {
        console.error('Three.js 初始化失败:', error);
      }
    };

    initThreeJS();

    // 清理函数
    return () => {
      if (isThreeInitialized) {
        const renderService = ThreeRenderService.getInstance();
        renderService.dispose();
        setIsThreeInitialized(false);
      }
    };
  }, []);

  // 加载场景数据到Three.js
  useEffect(() => {
    if (isThreeInitialized && sceneData) {
      const renderService = ThreeRenderService.getInstance();
      renderService.loadSceneData(sceneData);
      console.log('场景数据已加载到Three.js');
    }
  }, [isThreeInitialized, sceneData]);

  // 同步网格和坐标轴显示状态
  useEffect(() => {
    if (isThreeInitialized) {
      const renderService = ThreeRenderService.getInstance();
      renderService.setGridVisible(showGrid);
      renderService.setAxesVisible(showAxes);
    }
  }, [isThreeInitialized, showGrid, showAxes]);

  // 初始化Canvas 2D（作为备用，仅在Three.js未初始化时使用）
  useEffect(() => {
    if (canvasRef.current && !isThreeInitialized) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        // 绘制简单的占位符内容
        ctx.fillStyle = '#2a2a2a';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 绘制网格
        if (showGrid) {
          ctx.strokeStyle = '#444';
          ctx.lineWidth = 1;
          const gridSize = 50;
          for (let x = 0; x <= canvas.width; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, canvas.height);
            ctx.stroke();
          }
          for (let y = 0; y <= canvas.height; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(canvas.width, y);
            ctx.stroke();
          }
        }

        // 绘制坐标轴
        if (showAxes) {
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;
          
          // X轴 (红色)
          ctx.strokeStyle = '#ff0000';
          ctx.lineWidth = 2;
          ctx.beginPath();
          ctx.moveTo(centerX, centerY);
          ctx.lineTo(centerX + 100, centerY);
          ctx.stroke();
          
          // Y轴 (绿色)
          ctx.strokeStyle = '#00ff00';
          ctx.beginPath();
          ctx.moveTo(centerX, centerY);
          ctx.lineTo(centerX, centerY - 100);
          ctx.stroke();
          
          // Z轴 (蓝色)
          ctx.strokeStyle = '#0000ff';
          ctx.beginPath();
          ctx.moveTo(centerX, centerY);
          ctx.lineTo(centerX + 70, centerY + 70);
          ctx.stroke();
        }

        // 绘制场景信息
        ctx.fillStyle = '#888';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';

        if (isLoading) {
          ctx.fillText('加载场景中...', canvas.width / 2, canvas.height / 2);
        } else if (sceneData) {
          ctx.fillText(`场景: ${sceneData.name}`, canvas.width / 2, canvas.height / 2 - 20);
          ctx.fillText(`节点: ${sceneData.nodes.length} | 材质: ${sceneData.materials.length}`, canvas.width / 2, canvas.height / 2);
          ctx.fillText(`纹理: ${sceneData.textures.length} | 动画: ${sceneData.animations.length}`, canvas.width / 2, canvas.height / 2 + 20);
        } else {
          ctx.fillText('3D Viewport', canvas.width / 2, canvas.height / 2);
          ctx.fillText('(Placeholder)', canvas.width / 2, canvas.height / 2 + 20);
        }
      }
    }
  }, [showGrid, showAxes, isLoading, sceneData, isThreeInitialized]);

  // 切换全屏
  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    // 这里应该实现真正的全屏逻辑
  };

  // 切换网格显示
  const handleToggleGrid = () => {
    dispatch(setShowGrid(!showGrid));
  };

  // 切换坐标轴显示
  const handleToggleAxes = () => {
    dispatch(setShowAxes(!showAxes));
  };

  // 切换播放状态
  const handleTogglePlay = () => {
    dispatch(setIsPlaying(!isPlaying));
  };

  // 设置变换模式
  const handleSetTransformMode = (mode: TransformMode) => {
    dispatch(setTransformMode(mode));
    const renderService = ThreeRenderService.getInstance();
    renderService.setTransformMode(mode);
  };

  // 设置变换空间
  const handleSetTransformSpace = (space: TransformSpace) => {
    dispatch(setTransformSpace(space));
    const renderService = ThreeRenderService.getInstance();
    renderService.setTransformSpace(space);
  };

  // 视图模式菜单
  const viewModeItems = [
    {
      key: 'shaded',
      label: t('editor.shaded') || '着色',
      onClick: () => setRenderMode('shaded')
    },
    {
      key: 'wireframe',
      label: t('editor.wireframe') || '线框',
      onClick: () => setRenderMode('wireframe')
    },
    {
      key: 'material',
      label: t('editor.material') || '材质',
      onClick: () => setRenderMode('material')
    },
    {
      key: 'rendered',
      label: t('editor.rendered') || '渲染',
      onClick: () => setRenderMode('rendered')
    }
  ];

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column', background: '#1e1e1e' }}>
      {/* 视口工具栏 */}
      <div style={{ 
        padding: '4px 8px', 
        background: '#2a2a2a', 
        borderBottom: '1px solid #444',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Space size="small">
          <Select
            value={cameraMode}
            onChange={setCameraMode}
            size="small"
            style={{ width: 100 }}
          >
            <Option value="perspective">{t('editor.perspective') || '透视'}</Option>
            <Option value="orthographic">{t('editor.orthographic') || '正交'}</Option>
          </Select>
          
          <Dropdown menu={{ items: viewModeItems }} placement="bottomLeft">
            <Button size="small" type="text" style={{ color: '#fff' }}>
              {t('editor.viewMode') || '视图模式'}
            </Button>
          </Dropdown>
        </Space>

        <Space size="small">
          {/* 变换工具 */}
          <Tooltip title={t('editor.select') || '选择'}>
            <Button
              size="small"
              type="text"
              icon={<SelectOutlined />}
              style={{ color: transformMode === TransformMode.SELECT ? '#40a9ff' : '#fff' }}
              onClick={() => handleSetTransformMode(TransformMode.SELECT)}
            />
          </Tooltip>

          <Tooltip title={t('editor.translate') || '移动'}>
            <Button
              size="small"
              type="text"
              icon={<ArrowsAltOutlined />}
              style={{ color: transformMode === TransformMode.TRANSLATE ? '#40a9ff' : '#fff' }}
              onClick={() => handleSetTransformMode(TransformMode.TRANSLATE)}
            />
          </Tooltip>

          <Tooltip title={t('editor.rotate') || '旋转'}>
            <Button
              size="small"
              type="text"
              icon={<RotateRightOutlined />}
              style={{ color: transformMode === TransformMode.ROTATE ? '#40a9ff' : '#fff' }}
              onClick={() => handleSetTransformMode(TransformMode.ROTATE)}
            />
          </Tooltip>

          <Tooltip title={t('editor.scale') || '缩放'}>
            <Button
              size="small"
              type="text"
              icon={<ColumnWidthOutlined />}
              style={{ color: transformMode === TransformMode.SCALE ? '#40a9ff' : '#fff' }}
              onClick={() => handleSetTransformMode(TransformMode.SCALE)}
            />
          </Tooltip>

          {/* 变换空间切换 */}
          <Tooltip title={transformSpace === TransformSpace.LOCAL ? (t('editor.localSpace') || '本地空间') : (t('editor.worldSpace') || '世界空间')}>
            <Button
              size="small"
              type="text"
              style={{ color: '#fff', fontSize: '10px' }}
              onClick={() => handleSetTransformSpace(transformSpace === TransformSpace.LOCAL ? TransformSpace.WORLD : TransformSpace.LOCAL)}
            >
              {transformSpace === TransformSpace.LOCAL ? 'L' : 'W'}
            </Button>
          </Tooltip>

          <Tooltip title={t('editor.toggleGrid') || '切换网格'}>
            <Button
              size="small"
              type="text"
              icon={<BorderOutlined />}
              style={{ color: showGrid ? '#40a9ff' : '#fff' }}
              onClick={handleToggleGrid}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.toggleAxes') || '切换坐标轴'}>
            <Button
              size="small"
              type="text"
              icon={<AppstoreOutlined />}
              style={{ color: showAxes ? '#40a9ff' : '#fff' }}
              onClick={handleToggleAxes}
            />
          </Tooltip>
          
          <Tooltip title={isPlaying ? (t('editor.pause') || '暂停') : (t('editor.play') || '播放')}>
            <Button
              size="small"
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              style={{ color: '#fff' }}
              onClick={handleTogglePlay}
            />
          </Tooltip>
          
          <Tooltip title={t('editor.performanceStats') || '性能统计'}>
            <Button
              size="small"
              type="text"
              icon={<DashboardOutlined />}
              style={{ color: showPerformanceStats ? '#40a9ff' : '#fff' }}
              onClick={() => setShowPerformanceStats(!showPerformanceStats)}
            />
          </Tooltip>

          <Tooltip title={t('editor.viewportSettings') || '视口设置'}>
            <Button
              size="small"
              type="text"
              icon={<SettingOutlined />}
              style={{ color: '#fff' }}
            />
          </Tooltip>
          
          <Tooltip title={isFullscreen ? (t('editor.exitFullscreen') || '退出全屏') : (t('editor.fullscreen') || '全屏')}>
            <Button
              size="small"
              type="text"
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
              style={{ color: '#fff' }}
              onClick={handleToggleFullscreen}
            />
          </Tooltip>
        </Space>
      </div>

      {/* 3D视口区域 */}
      <div style={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
        <canvas
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            display: 'block',
            background: isThreeInitialized ? 'transparent' : '#2a2a2a'
          }}
        />
        
        {/* 视口信息覆盖层 */}
        <div style={{
          position: 'absolute',
          top: 8,
          left: 8,
          color: '#fff',
          fontSize: '12px',
          background: 'rgba(0,0,0,0.5)',
          padding: '4px 8px',
          borderRadius: '4px'
        }}>
          {cameraMode} | {renderMode} | {isThreeInitialized ? 'Three.js' : '2D Canvas'}
          {sceneData && (
            <div style={{ marginTop: '4px', fontSize: '10px' }}>
              场景: {sceneData.name} | 节点: {sceneData.nodes.length}
            </div>
          )}
          {isLoading && (
            <div style={{ marginTop: '4px', fontSize: '10px', color: '#ffeb3b' }}>
              加载中...
            </div>
          )}
          {showPerformanceStats && performanceStats && (
            <div style={{ marginTop: '4px', fontSize: '10px' }}>
              FPS: {performanceStats.fps} | 绘制: {performanceStats.drawCalls} | 三角形: {Math.round(performanceStats.triangles / 1000)}K
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewportPanel;
