/**
 * 可视化脚本面板组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { 

  Button, 
  Space, 

  Select, 
  Input,
  Tree,
  Modal,
  Form,
  message,
  Divider
} from 'antd';
import {
  PlusOutlined,

  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SaveOutlined,
  FolderOpenOutlined,
  CodeOutlined,
  BugOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
// import { useAppDispatch, useAppSelector } from '../../store'; // 暂时注释掉未使用的导入

const { Option } = Select;
// const { TextArea } = Input; // 暂时注释掉未使用的变量
// const { TreeNode } = Tree; // 暂时注释掉未使用的变量

interface VisualScriptNode {
  id: string;
  type: string;
  name: string;
  position: { x: number; y: number };
  inputs: any[];
  outputs: any[];
  properties: Record<string, any>;
}

interface VisualScript {
  id: string;
  name: string;
  nodes: VisualScriptNode[];
  connections: any[];
  variables: Record<string, any>;
}

const VisualScriptPanel: React.FC = () => {
  const { t } = useTranslation();
  // const dispatch = useAppDispatch(); // 暂时注释掉未使用的dispatch
  const canvasRef = useRef<HTMLDivElement>(null);
  const [scripts, setScripts] = useState<VisualScript[]>([]);
  const [selectedScript, setSelectedScript] = useState<VisualScript | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 模拟脚本数据
  useEffect(() => {
    const mockScripts: VisualScript[] = [
      {
        id: '1',
        name: 'Player Controller',
        nodes: [
          {
            id: 'node1',
            type: 'input',
            name: 'Input Event',
            position: { x: 100, y: 100 },
            inputs: [],
            outputs: [{ id: 'out1', type: 'event' }],
            properties: {}
          },
          {
            id: 'node2',
            type: 'action',
            name: 'Move Player',
            position: { x: 300, y: 100 },
            inputs: [{ id: 'in1', type: 'event' }],
            outputs: [],
            properties: { speed: 5 }
          }
        ],
        connections: [
          { from: 'node1.out1', to: 'node2.in1' }
        ],
        variables: {
          playerSpeed: 5,
          jumpHeight: 10
        }
      },
      {
        id: '2',
        name: 'Animation Controller',
        nodes: [],
        connections: [],
        variables: {}
      }
    ];
    setScripts(mockScripts);
    if (mockScripts.length > 0) {
      setSelectedScript(mockScripts[0]);
    }
  }, []);

  // 处理脚本选择
  const handleSelectScript = (scriptId: string) => {
    const script = scripts.find(s => s.id === scriptId);
    setSelectedScript(script || null);
  };

  // 处理创建新脚本
  const handleCreateScript = () => {
    setSelectedScript(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理保存脚本
  const handleSaveScript = (values: any) => {
    if (selectedScript) {
      // 编辑现有脚本
      setScripts(prev => prev.map(s => 
        s.id === selectedScript.id ? { ...selectedScript, ...values } : s
      ));
      message.success(t('editor.visualScript.updateSuccess'));
    } else {
      // 创建新脚本
      const newScript: VisualScript = {
        id: Date.now().toString(),
        ...values,
        nodes: [],
        connections: [],
        variables: {}
      };
      setScripts(prev => [...prev, newScript]);
      setSelectedScript(newScript);
      message.success(t('editor.visualScript.createSuccess'));
    }
    setIsModalVisible(false);
  };

  // 处理删除脚本 - 暂时注释掉未使用的函数
  // const handleDeleteScript = (scriptId: string) => {
  //   Modal.confirm({
  //     title: t('editor.visualScript.confirmDelete'),
  //     content: t('editor.visualScript.confirmDeleteMessage'),
  //     onOk: () => {
  //       setScripts(prev => prev.filter(s => s.id !== scriptId));
  //       if (selectedScript?.id === scriptId) {
  //         setSelectedScript(scripts.length > 1 ? scripts[0] : null);
  //       }
  //       message.success(t('editor.visualScript.deleteSuccess'));
  //     }
  //   });
  // };

  // 处理运行脚本
  const handleRunScript = () => {
    if (!selectedScript) return;
    setIsRunning(true);
    setIsPaused(false);
    message.info(t('editor.visualScript.scriptStarted'));
    
    // 模拟脚本执行
    setTimeout(() => {
      setIsRunning(false);
      message.success(t('editor.visualScript.scriptCompleted'));
    }, 3000);
  };

  // 处理暂停脚本
  const handlePauseScript = () => {
    setIsPaused(!isPaused);
    message.info(isPaused ? t('editor.visualScript.scriptResumed') : t('editor.visualScript.scriptPaused'));
  };

  // 处理停止脚本
  const handleStopScript = () => {
    setIsRunning(false);
    setIsPaused(false);
    message.info(t('editor.visualScript.scriptStopped'));
  };

  // 渲染节点树
  const renderNodeTree = () => {
    if (!selectedScript) return null;

    const treeData = [
      {
        title: 'Variables',
        key: 'variables',
        children: Object.keys(selectedScript.variables).map(key => ({
          title: `${key}: ${selectedScript.variables[key]}`,
          key: `var-${key}`,
          isLeaf: true
        }))
      },
      {
        title: 'Nodes',
        key: 'nodes',
        children: selectedScript.nodes.map(node => ({
          title: `${node.name} (${node.type})`,
          key: `node-${node.id}`,
          isLeaf: true
        }))
      }
    ];

    return (
      <Tree
        treeData={treeData}
        defaultExpandAll
        style={{ marginTop: '8px' }}
      />
    );
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 工具栏 */}
      <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />} 
              size="small"
              onClick={handleCreateScript}
            >
              {t('editor.visualScript.create')}
            </Button>
            <Button 
              icon={<SaveOutlined />} 
              size="small"
              disabled={!selectedScript}
            >
              {t('editor.visualScript.save')}
            </Button>
            <Button 
              icon={<FolderOpenOutlined />} 
              size="small"
            >
              {t('editor.visualScript.load')}
            </Button>
          </Space>
          
          <Select
            value={selectedScript?.id}
            onChange={handleSelectScript}
            placeholder={t('editor.visualScript.selectScript')}
            size="small"
            style={{ width: '100%' }}
          >
            {scripts.map(script => (
              <Option key={script.id} value={script.id}>
                {script.name}
              </Option>
            ))}
          </Select>
        </Space>
      </div>

      {/* 执行控制 */}
      {selectedScript && (
        <div style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
          <Space>
            <Button
              type={isRunning ? 'default' : 'primary'}
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={handleRunScript}
              disabled={isRunning}
            >
              {t('editor.visualScript.run')}
            </Button>
            <Button
              icon={<PauseCircleOutlined />}
              size="small"
              onClick={handlePauseScript}
              disabled={!isRunning}
            >
              {isPaused ? t('editor.visualScript.resume') : t('editor.visualScript.pause')}
            </Button>
            <Button
              icon={<StopOutlined />}
              size="small"
              onClick={handleStopScript}
              disabled={!isRunning}
            >
              {t('editor.visualScript.stop')}
            </Button>
            <Divider type="vertical" />
            <Button
              icon={<BugOutlined />}
              size="small"
            >
              {t('editor.visualScript.debug')}
            </Button>
            <Button
              icon={<SettingOutlined />}
              size="small"
            >
              {t('editor.visualScript.settings')}
            </Button>
          </Space>
        </div>
      )}

      {/* 脚本内容 */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* 左侧：节点树 */}
        <div style={{ width: '200px', borderRight: '1px solid #f0f0f0', padding: '8px' }}>
          <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '8px' }}>
            {t('editor.visualScript.structure')}
          </div>
          {renderNodeTree()}
        </div>

        {/* 右侧：画布区域 */}
        <div style={{ flex: 1, position: 'relative' }}>
          <div
            ref={canvasRef}
            style={{
              width: '100%',
              height: '100%',
              background: '#fafafa',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            {selectedScript ? (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                color: '#999'
              }}>
                <CodeOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>{t('editor.visualScript.canvasPlaceholder')}</div>
                <div style={{ fontSize: '12px', marginTop: '8px' }}>
                  {selectedScript.nodes.length} {t('editor.visualScript.nodes')}
                </div>
              </div>
            ) : (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                color: '#999'
              }}>
                {t('editor.visualScript.noScriptSelected')}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 脚本编辑对话框 */}
      <Modal
        title={selectedScript ? t('editor.visualScript.editTitle') : t('editor.visualScript.createTitle')}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveScript}
        >
          <Form.Item
            name="name"
            label={t('editor.visualScript.name')}
            rules={[{ required: true, message: t('editor.visualScript.nameRequired') as string }]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VisualScriptPanel;
