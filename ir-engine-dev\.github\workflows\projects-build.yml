name: projects-build
on:
  push:
    branches:
      [qat]
jobs:
  secrets-gate:
    runs-on: ubuntu-latest
    outputs:
      ok: ${{ steps.check-secrets.outputs.ok }}
    steps:
      - name: check for secrets needed to run workflows
        id: check-secrets
        run: |
          if [ ${{ secrets.PROJECTS_BUILD_ENABLED }} == 'true' ]; then
            echo "ok=enabled" >> $GITHUB_OUTPUT
          fi
  compile-codebase:
    needs:
      - secrets-gate
    if: ${{ needs.secrets-gate.outputs.ok == 'enabled' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Infinite Reality Engine
        uses: actions/checkout@v3
      - name: Checkout ir-development-test-suite
        uses: actions/checkout@v3
        with:
          repository: ir-engine/ir-development-test-suite
          path: './packages/projects/projects/ir-engine/ir-development-test-suite'
      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22.x
      - name: restore lerna
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            */*/node_modules
            package-lock.json
          key: ${{ runner.os }}-branch-build-${{ hashFiles('**/package.json') }}
      - run: cp .env.local.default .env.local
      - run: npm install --production=false --loglevel notice --legacy-peer-deps
      - run: npm run lint
      - run: npx lerna run --scope '@ir-engine/*' check-errors
      - run: npm run check-eslint
      - run: npm run dev-docker
      - run: npm run dev-reinit
      - run: npm run test
      - run: npm run build-client
      
